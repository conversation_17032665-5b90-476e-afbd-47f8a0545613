const request = require("../request");
const groupTag = require("../group-tag");
const token = require("../token");
const api = require("../../api");
/**
 *
 * @param {string} authCode 临时授权码
 *
 * @returns
 */
exports.main = async ({ authCode, SuiteId, corpId }) => {
  let suite_access_token = await token.getSuiteToken({ corpId });
  console.log("suite_access_token", suite_access_token);
  let url = `https://qyapi.weixin.qq.com/cgi-bin/service/get_permanent_code?suite_access_token=${suite_access_token}`;
  let params = {
    auth_code: authCode,
  };
  let corpInfo = await request.main(url, params, "POST");
  console.log("获取到企业信息", corpInfo);
  await getCorpToken(corpInfo, suite_access_token);
};
// 获取到企业token
async function getCorpToken(info, suite_access_token) {
  let item = {
    corpId: info.auth_corp_info.corpid,
    corp_name: info.auth_corp_info.corp_name,
    corp_square_logo_url: info.auth_corp_info.corp_square_logo_url,
    auth_info: info.auth_info,
    auth_corp_info: info.auth_corp_info,
    createTime: new Date().getTime(),
    permanent_code: info.permanent_code,
  };
  let corpData = await api.getCorpApi({
    type: "getCorpInfo",
    corpId: info.auth_corp_info.corpid,
  });
  let corpList = corpData.data;
  if (Array.isArray(corpList) && corpList.length > 0) {
    await updateCorp(info.auth_corp_info.corpid, item);
  } else {
    const corpId = await getUninstalledCorpList(
      info.auth_corp_info.corpid,
      info,
      suite_access_token
    );
    if (corpId) {
      await updateCorp(corpId, item);
    } else {
      await api.getCorpApi({
        type: "addCorp",
        params: item,
      });
    }
    // 添加默认角色、默认模版
    await api.getCorpApi({
      type: "addCorpDefaultInfo",
      corpid: info.auth_corp_info.corpid,
    });
    // 同步机构标签
    await groupTag.getCorpTagList({
      type: "getCorpTagList",
      corpId: info.auth_corp_info.corpid,
    });
  }
}

async function updateCorp(corpId, params) {
  await api.getCorpApi({
    type: "updateCorp",
    params,
    corpId,
  });
}

async function getCorpInfo(corpId) {
  let result = await api.getCorpApi({
    type: "getCorpInfo",
    corpId,
  });
  return result.data;
}

//获取到未安装的应用列表
async function getUninstalledCorpList(corpId, corpInfo, suite_access_token) {
  let data = await getCorpInfo(corpId);
  let corpIds = [];
  console.log("获取到未装应用的机构", JSON.stringify(data));
  if (data.length === 0) return "";
  const arr = data.map((item) => {
    return corpIdToOpencorpId(item.corpId, corpInfo, suite_access_token);
  });
  // 获取到所有未安装应用的corpId
  for (let i = 0; i < arr.length; i += 10) {
    const promiseArr = arr.slice(i, i + 10);
    const promiseList = await Promise.all(promiseArr);
    corpIds.push(...promiseList);
  }
  const corpObj = corpIds.filter(
    (item) => item && item.open_corpid === corpId
  )[0];
  return corpObj ? corpObj.corpId : false;
}
async function corpIdToOpencorpId(corpId, corpInfo, suite_access_token) {
  const provider_access_token = await getProviderAccessToken();
  let url = `https://qyapi.weixin.qq.com/cgi-bin/service/corpid_to_opencorpid?provider_access_token=${provider_access_token}`;
  let params = {
    corpid: corpId,
  };
  let { open_corpid, errcode, errmsg } = await request.main(
    url,
    params,
    "POST"
  );
  if (errcode === 0) {
    await updateRolesCorpId(corpId, open_corpid);
    await updateUserInfo(corpId, corpInfo, suite_access_token);
    return {
      corpId,
      open_corpid,
    };
  } else {
    return {
      errcode,
      errmsg,
    };
  }
}
async function getProviderAccessToken() {
  let url = `https://qyapi.weixin.qq.com/cgi-bin/service/get_provider_token`;
  let params = {
    corpid: "wwe3fb2faa52cf9dfb",
    provider_secret:
      "v5sv-M56Amgik91OewBIzcq6ptbnlkC-QUisBq3UNErDF6Yyxd4BgBRea0_636se",
  };
  let { provider_access_token } = await request.main(url, params, "POST");
  return provider_access_token;
}

// 更新角色的open_corpid
async function updateRolesCorpId(corpId, open_corpid) {
  await api.getCorpApi({
    type: "updateRolesCorpId",
    corpId,
    openCorpId: open_corpid,
  });
}

// 通过明文corpId更新成员详情
async function updateUserInfo(corpId, corpInfo, suite_access_token) {
  let result = await api.getCorpApi({
    type: "getCorpMember",
    corpId,
    params: {
      corpId,
    },
    page: 1,
    pageSize: 1000,
  });
  const data = result.data;
  if (data.length === 0) return;
  const params = {
    suite_access_token,
    corpid: corpInfo.auth_corp_info.corpid,
    permanentCode: corpInfo.permanent_code,
  };
  console.log("获取到未安装机构的成员信息", data);
  let access_token = await token.getToken(params);
  let useridList = data.map((item) => {
    return item.userid;
  });
  const openUseridList = await userid_to_openuserid(access_token, useridList);
  console.log("获取到未安装机构的openUserid", openUseridList);
  let promiseArr = openUseridList.map((item) => {
    return getCorpMemberInfoByUserId(
      item,
      access_token,
      corpInfo.auth_corp_info.corpid
    );
  });
  await Promise.all(promiseArr);
}

// 获取成员详情
async function getCorpMemberInfoByUserId(item, toekn, corpId) {
  let url = `https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=${toekn}&userid=${item.open_userid}`;
  let res = await request.main(url, null, "GET");
  if (res.errcode === 0) {
    const { errcode, errmsg, ...rest } = res;
    rest["corpId"] = corpId;
    rest["createTime"] = new Date().getTime();
    await api.getCorpApi({
      type: "updateCorpMemberByUserId",
      userid: item.userid,
      corpId,
      params: rest,
    });
  }
}

async function userid_to_openuserid(token, useridList) {
  if (!token) return "";
  let url = `https://qyapi.weixin.qq.com/cgi-bin/batch/userid_to_openuserid?access_token=${token}`;
  let res = await request.main(
    url,
    {
      userid_list: useridList,
    },
    "POST"
  );
  return res.errcode === 0 ? res.open_userid_list : [];
}

const { getWeComData } = require('./callBack/weCom/index');

// 模拟企业微信回调数据
const mockWeComCallback = {
  AuthCorpId: 'wpLgjyawAAJ69XjD39GMOXp2EsYHYb3w',
  InfoType: 'suite_ticket',
  SuiteId: 'wwb6cc689aa5923bfb', // 不是 ww9ffaf7918c964aa4，会触发 getSuiteAccessToken 调用
  SuiteTicket: 'mock_suite_ticket_123',
  TimeStamp: Date.now()
};

async function testEnvironments() {
  console.log('=== 测试企业微信回调环境判断功能 ===\n');
  
  // 测试1: 测试环境（默认）
  console.log('🔍 测试1: 测试环境 (NODE_ENV 未设置或非 production)');
  console.log('预期: 应该跳过 getSuiteAccessToken 调用\n');
  
  // 确保 NODE_ENV 不是 production
  const originalEnv = process.env.NODE_ENV;
  process.env.NODE_ENV = 'test';
  
  try {
    await getWeComData(mockWeComCallback);
    console.log('✅ 测试环境测试完成\n');
  } catch (error) {
    console.error('❌ 测试环境测试失败:', error.message);
  }
  
  // 测试2: 生产环境
  console.log('🔍 测试2: 生产环境 (NODE_ENV=production)');
  console.log('预期: 应该调用 getSuiteAccessToken 函数\n');
  
  process.env.NODE_ENV = 'production';
  
  try {
    await getWeComData(mockWeComCallback);
    console.log('✅ 生产环境测试完成\n');
  } catch (error) {
    console.error('❌ 生产环境测试失败:', error.message);
    console.error('这是正常的，因为这是真实的API调用\n');
  }
  
  // 测试3: 开发环境
  console.log('🔍 测试3: 开发环境 (NODE_ENV=development)');
  console.log('预期: 应该跳过 getSuiteAccessToken 调用\n');
  
  process.env.NODE_ENV = 'development';
  
  try {
    await getWeComData(mockWeComCallback);
    console.log('✅ 开发环境测试完成\n');
  } catch (error) {
    console.error('❌ 开发环境测试失败:', error.message);
  }
  
  // 测试4: 测试不触发 getSuiteAccessToken 的情况
  console.log('🔍 测试4: 不触发 getSuiteAccessToken 的情况');
  console.log('预期: 使用特殊的 SuiteId，不应该调用 getSuiteAccessToken\n');
  
  const mockCallbackNoTrigger = {
    ...mockWeComCallback,
    SuiteId: 'ww9ffaf7918c964aa4' // 这个 SuiteId 不会触发调用
  };
  
  try {
    await getWeComData(mockCallbackNoTrigger);
    console.log('✅ 不触发情况测试完成\n');
  } catch (error) {
    console.error('❌ 不触发情况测试失败:', error.message);
  }
  
  // 恢复原始环境变量
  process.env.NODE_ENV = originalEnv;
  
  console.log('=== 测试完成 ===');
  console.log('请查看上面的日志输出，确认：');
  console.log('1. 测试环境和开发环境应该显示"跳过 getSuiteAccessToken 调用"');
  console.log('2. 生产环境应该显示调用相关的日志');
  console.log('3. 不触发的情况不应该有任何 getSuiteAccessToken 相关日志');
}

// 测试不同的 InfoType
async function testDifferentInfoTypes() {
  console.log('\n=== 测试不同的 InfoType ===\n');
  
  const testCases = [
    { InfoType: 'create_auth', description: '创建授权' },
    { InfoType: 'cancel_auth', description: '取消授权' },
    { InfoType: 'change_external_contact', ChangeType: 'add_external_contact', description: '添加外部联系人' },
    { InfoType: 'conversation_new_message', description: '会话新消息' }
  ];
  
  process.env.NODE_ENV = 'test';
  
  for (const testCase of testCases) {
    console.log(`🔍 测试 InfoType: ${testCase.InfoType} (${testCase.description})`);
    
    const testData = {
      ...mockWeComCallback,
      InfoType: testCase.InfoType
    };
    
    if (testCase.ChangeType) {
      testData.ChangeType = testCase.ChangeType;
    }
    
    try {
      await getWeComData(testData);
      console.log('✅ 测试完成\n');
    } catch (error) {
      console.error('❌ 测试失败:', error.message, '\n');
    }
  }
}

// 运行所有测试
async function runAllTests() {
  try {
    await testEnvironments();
    await testDifferentInfoTypes();
  } catch (error) {
    console.error('测试运行失败:', error);
  }
}

runAllTests(); 
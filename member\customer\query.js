const dayjs = require("dayjs");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getCustomersCount":
      return await getCustomersCount(content);
    case "getInHospitalCustomersCount":
      return await getInHospitalCustomersCount(content);
  }
};

// 查询报备记录人数
async function getCustomersCount(content) {
  const { corpId, dates, params, reportTeamId, introducers } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  let match = {
    corpId,
    ...params,
  };

  if (reportTeamId) match.reportTeamId = reportTeamId;
  if (dates && dates.length > 0) {
    match.createTime = {
      $gte: dayjs(dates[0]).startOf("day").valueOf(),
      $lt: dayjs(dates[1]).endOf("day").valueOf(),
    };
  }

  const pipeline = [
    { $match: match },
  ];

  if (introducers && Array.isArray(introducers) && introducers.length > 0) {
    pipeline.push(
      {
        $match: {
          $expr: {
            $in: [
              {
                $arrayElemAt: [
                  { $reverseArray: "$introducerRecord.introducer" },
                  0,
                ],
              },
              introducers,
            ],
          },
        },
      }
    );
  }

  pipeline.push({ $count: "total" });
  
  const result = await db.collection("member").aggregate(pipeline).toArray();
  const total = result.length > 0 ? result[0].total : 0;
  
  return { success: true, count: total, message: "查询成功", query: match };
}

// 查询到院人数
async function getInHospitalCustomersCount(content) {
  const { corpId, dates, reportTeamId, introducers } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  let match = {
    corpId,
    addMethod: "eStoreReport",
  };

  if (reportTeamId) match.reportTeamId = reportTeamId;
  if (dates && dates.length > 0) {
    match.inHospitalTimes = {
      $gte: dayjs(dates[0]).startOf("day").valueOf(),
      $lt: dayjs(dates[1]).endOf("day").valueOf(),
    };
  } else {
    match.inHospitalTimes = { $exists: true, $ne: [] };
  }

  const pipeline = [
    { $match: match },
  ];

  if (introducers && Array.isArray(introducers) && introducers.length > 0) {
    pipeline.push(
      {
        $match: {
          $expr: {
            $in: [
              {
                $arrayElemAt: [
                  { $reverseArray: "$introducerRecord.introducer" },
                  0,
                ],
              },
              introducers,
            ],
          },
        },
      }
    );
  }

  pipeline.push({ $count: "total" });
  
  const result = await db.collection("member").aggregate(pipeline).toArray();
  const total = result.length > 0 ? result[0].total : 0;
  
  return { success: true, count: total, message: "查询成功" };
}

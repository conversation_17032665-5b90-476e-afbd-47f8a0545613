const dayjs = require("dayjs");

/**
 * 成员手动建档的客户信息在客户未授权之前只有成员自己可见, 待授权完成之后其他成员可见
 * 客户字段 externalUserId: 用户授权之后才有值 不存在或者为空 说明 客户未完成授权
 * 客户字段 corpUserId: 关联的员工userIds
 *
 * @param {*} _ app.database().command
 * @param {*} userId
 * @returns
 */
exports.getVisibleTo = function (_, userId) {
  return _.or([
    { unionid: _.and(_.exists(true), _.neq("")) }, // 用户完成授权了 可被团队其他成员看到
    {
      unionid: _.or(_.exists(false), _.and(_.exists(true), _.eq(""))),
      corpUserId: _.and(_.exists(true), _.eq(userId)),
    }, // 未完成授权只有被 创建的人看到
  ]);
};
exports.processInBatches = async (arr, handler, batchSize = 10) => {
  const result = [];
  for (let i = 0; i < arr.length; i += batchSize) {
    const batch = arr.slice(i, i + batchSize);
    const batchResult = await Promise.all(batch.map(handler));
    result.push(...batchResult);
  }
  return result;
};
exports.getAllData = async (fetchData, db, pageSize = 100) => {
  let page = 1;
  let allData = [];
  while (true) {
    let list = await fetchData(page, pageSize, db);
    if (list.length > 0) {
      allData.push(...list);
      page++;
    } else {
      break;
    }
  }
  return allData;
};



exports.generateRandomString = (length = 10) => {
  let result = "";
  let characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

// 根据身份证号 获取 性别/年龄/出生日期
exports.calculateIdCardInfo = (idCard) => {
  const gender = idCard.charAt(16) % 2 === 0 ? "女" : "男";
  const birthYear = idCard.substring(6, 10);
  const birthMonth = idCard.substring(10, 12);
  const birthDay = idCard.substring(12, 14);
  const birthDate = `${birthYear}-${birthMonth}-${birthDay}`;
  const age = calculateAge(birthDate);
  return {
    gender,
    birthDate,
    age,
  };
};

function calculateAge(birthDate) {
  const thisYear = new Date().getFullYear();
  const birthYear = dayjs(birthDate).year();
  const birthdayOfThisYear = dayjs(birthDate).year(thisYear);
  const after = dayjs().startOf('day').isAfter(birthdayOfThisYear);
  const age = thisYear - birthYear + (after ? 0 : -1);
  return Math.max(age, 1);
}

{"name": "ykt", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "node index", "pro": "cross-env NODE_ENV=production node index", "zhenyuan": "cross-env NODE_ENV=zhenyuan node index", "watch-dev": "cross-env NODE_ENV=development nodemon index", "watch-prod": "cross-env NODE_ENV=production nodemon index", "build:pro": "cross-env NODE_ENV=production node esbuild.config.js", "build:dev": "cross-env NODE_ENV=development node esbuild.config.js", "build:hz": "cross-env NODE_ENV=hz node esbuild.config.js", "build:zhenyuan": "cross-env NODE_ENV=zhenyuan node esbuild.config.js"}, "author": "", "license": "ISC", "dependencies": {"@wecom/crypto": "^1.0.1", "alipay-sdk": "^4.13.0", "app-root-path": "^3.1.0", "axios": "^1.7.7", "bignumber": "^1.1.0", "bignumber.js": "^9.1.2", "body-parser": "^1.20.3", "cheerio": "^1.0.0", "connect-timeout": "^1.9.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "express": "^4.21.1", "form-data": "^4.0.1", "gm-crypt": "^0.0.2", "jsdom": "^26.0.0", "jsonwebtoken": "^9.0.2", "logger": "^0.0.1", "mongodb": "^6.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "node-schedule": "^2.1.1", "querystring": "^0.2.1", "sm-crypto": "^0.3.13", "web-streams-polyfill": "^4.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xml2js": "^0.6.2", "zlib": "^1.0.5"}, "devDependencies": {"cross-env": "^7.0.3", "esbuild": "^0.24.2", "jest": "^29.7.0"}}
const common = require("../../common.js");
let db = null;

const COLLECTION_NAME = "management-plan-cate";

exports.main = async (context, DB) => {
  db = DB;
  switch (context.type) {
    case "addManageMentPlanCate":
      return await addManageMentPlanCate(context);
    case "deleteManageMentPlanCate":
      return await deleteManageMentPlanCate(context);
    case "getManageMentPlanCateList":
      return await getManageMentPlanCateList(context);
    case "sortManageMentPlanCate":
      return await sortManageMentPlanCate(context);
    case "updateManageMentPlanCate":
      return await updateManageMentPlanCate(context);
  }
};

async function addManageMentPlanCate(context) {
  let { corpId, label, parentId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    if (parentId) {
      const parent = await db
        .collection(COLLECTION_NAME)
        .findOne({ _id: parentId, corpId });
      if (!parent) return { success: false, message: "父级分类不存在" };
      if (![1, 2].includes(parent.level))
        return { success: false, message: "父级分类层级错误" };
      const parentGroup = [...(parent.parentGroup || []), parent._id];
      const res = await db.collection(COLLECTION_NAME).insertOne({
        _id: common.generateRandomString(24),
        corpId,
        type: "corp",
        label,
        level: parent.level + 1,
        parentId,
        createTime: Date.now(),
        parentGroup,
      });
      return { success: true, data: res, message: "添加回访计划分类成功" };
    }
    const res = await db.collection(COLLECTION_NAME).insertOne({
      _id: common.generateRandomString(24),
      corpId,
      type: "corp",
      label,
      level: 1,
      createTime: Date.now(),
    });
    return { success: true, data: res, message: "添加回访计划分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function deleteManageMentPlanCate(context) {
  let { corpId, id: _id } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  try {
    const cate = await db
      .collection(COLLECTION_NAME)
      .findOne({ _id, corpId, type: "corp" });
    if (!cate) return { success: false, message: "机构常用分类不存在" };
    const ids = [_id];
    if (cate.level < 3) {
      const parentGroupQuery = { corpId, type: "corp" };
      if (cate.level === 1) {
        parentGroupQuery["parentGroup.0"] = _id;
      } else if (cate.level === 2) {
        parentGroupQuery["parentGroup.1"] = _id;
      }
      const list = await db
        .collection(COLLECTION_NAME)
        .find(parentGroupQuery)
        .project({ _id: 1 })
        .toArray();
      ids.push(...list.map(({ _id }) => _id));
    }
    const total = await db
      .collection("management-plan")
      .countDocuments({ corpId, cateId: { $in: ids } });
    if (total > 0)
      return { success: false, message: "该分类下有回访计划，不能删除" };
    const res = await db
      .collection(COLLECTION_NAME)
      .deleteMany({ corpId, _id: { $in: ids } });
    return { success: true, message: "删除管理计划分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function getManageMentPlanCateList(context) {
  let { corpId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const list = await db
      .collection(COLLECTION_NAME)
      .find({ corpId, type: "corp" })
      .sort({ createTime: 1 })
      .toArray();
    return { success: true, list: list, message: "获取机构常用分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}
async function sortManageMentPlanCate(context) {
  const { corpId, sortData } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  const arr = Array.isArray(sortData)
    ? sortData.filter(
        (i) =>
          i._id && typeof i.sort === "number" && i.sort % 1 === 0 && i.sort >= 0
      )
    : [];
  if (arr.length === 0) return { success: false, message: "参数错误" };

  try {
    const bulkOps = arr.map((item) => ({
      updateOne: {
        filter: {
          _id: item._id,
          corpId,
          type: "corp",
          parentId: { $exists: false },
        },
        update: { $set: { sort: item.sort } },
      },
    }));

    const res = await db.collection(COLLECTION_NAME).bulkWrite(bulkOps);
    return { success: true, message: "操作成功", data: res };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function updateManageMentPlanCate(context) {
  let { corpId, id: _id, label } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    const res = await db
      .collection(COLLECTION_NAME)
      .updateOne(
        { _id, corpId, type: "corp" },
        { $set: { label: label.trim() } }
      );
    return { success: true, data: res, message: "更新回访计划分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

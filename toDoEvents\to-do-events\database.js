/**
 * 数据库 - 待办事项
 *
 */

const expectedParams = {};

function validateParams(params, expectedParams) {
  for (let key in params) {
    if (!(key in expectedParams)) {
      delete params[key];
    }
    if (typeof params[key] !== expectedParams[key]) {
      return {
        success: false,
        message: `参数 ${key} 应该是 ${expectedParams[key]} 类型，但实际是 ${typeof params[key]} 类型`,
      };
    }
  }
  return {
    success: true,
    params,
  };
}

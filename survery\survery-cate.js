const common = require('../common.js');
let db = null;

exports.main = async (context, DB) => {
  db = DB;
  switch (context.type) {
    case "addSurveryCate":
      return await addSurveryCate(context);
    case "updateSurveryCate":
      return await updateSurveryCate(context);
    case "deleteSurveryCate":
      return await deleteSurveryCate(context);
    case "getSurveryCateList":
      return await getSurveryCateList(context);
    case "initCorpSurveyCate":
      return await initCorpSurveyCate(context);
    case "sortSurveryCate":
      return await sortSurveryCate(context);
    case "ifCateExist":
      return await ifCateExist(context);
  }
};

async function addSurveryCate(context) {
  let { corpId, label, parentId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    if (parentId) {
      const parent = await db.collection("survery-cate").findOne({ _id: parentId, corpId });
      if (!parent) return { success: false, message: "父级分类不存在" };
      if (![1, 2].includes(parent.level))
        return { success: false, message: "父级分类层级错误" };
      const res = await db.collection("survery-cate").insertOne({
        _id: common.generateRandomString(24),
        corpId,
        label,
        level: parent.level + 1,
        parentId,
        createTime: Date.now(),
      });
      return { success: true, data: res, message: "添加分类成功" };
    }
    const res = await db.collection("survery-cate").insertOne({
      _id: common.generateRandomString(24),
      corpId,
      label,
      level: 1,
      createTime: Date.now()
    });
    return { success: true, data: res, message: "添加分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function updateSurveryCate(context) {
  let { corpId, id: _id, label } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    const res = await db.collection("survery-cate").updateOne(
      { _id, corpId },
      { $set: { label: label.trim() } }
    );
    return { success: true, data: res, message: "更新分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function deleteSurveryCate(context) {
  let { corpId, id: _id } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  try {
    const cate = await db.collection("survery-cate").findOne({ _id, corpId });
    if (!cate) return { success: false, message: "分类不存在" };
    const ids = [_id];
    if (cate.level < 3) {
      const children = await db.collection("survery-cate").find({ corpId, parentId: _id }).toArray();
      const childrenIds = children.map((item) => item._id);
      if (cate.level === 1 && childrenIds.length) {
        const grandchildren = await db.collection("survery-cate").find({ corpId, parentId: { $in: childrenIds } }).toArray();
        ids.push(...grandchildren.map((item) => item._id));
      }
      ids.push(...childrenIds);
    }
    const res = await db.collection("survery-cate").deleteMany({ corpId, _id: { $in: ids } });
    const res2 = await db.collection("survery").deleteMany({ corpId, cateId: { $in: ids } });
    return { success: true, data: [res, res2], message: "删除分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function getSurveryCateList(context) {
  let { corpId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const list = await db.collection("survery-cate").find({ corpId, init: { $exists: false } }).sort({ createTime: 1 }).limit(10000).toArray();
    if (list.length > 0)
      return { success: true, list, message: "获取分类成功" };
    const initRecord = await db.collection("survery-cate").findOne({ corpId, init: true });
    if (initRecord) return { success: true, list, message: "获取分类成功" };
    await db.collection("survery-cate").insertOne({ _id: common.generateRandomString(24), corpId, init: true });
    await db.collection("survery-cate").insertOne({ _id: common.generateRandomString(24), corpId, label: "默认", level: 1, createTime: Date.now() });
    const newList = await db.collection("survery-cate").find({ corpId, init: { $exists: false } }).sort({ createTime: 1 }).limit(10000).toArray();
    return { success: true, list: newList, message: "获取分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}
async function sortSurveryCate(context) {
  const { corpId, sortData } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  const arr = Array.isArray(sortData)
    ? sortData.filter(
        (i) =>
          i._id && typeof i.sort === "number" && i.sort % 1 === 0 && i.sort >= 0
      )
    : [];
  if (arr.length === 0) return { success: false, message: "参数错误" };

  try {
    const list = await db.collection("survery-cate").find({
      corpId,
      _id: { $in: arr.map((i) => i._id) },
      parentId: { $exists: false },
    }).toArray();
    const ids = list.map((i) => i._id);
    const queue = arr.filter((i) => ids.includes(i._id));
    const bulkOps = queue.map((item) => ({
      updateOne: {
        filter: { _id: item._id },
        update: { $set: { sort: item.sort } }
      }
    }));
    if (bulkOps.length > 0) {
      await db.collection("survery-cate").bulkWrite(bulkOps);
    }
    return { success: true, message: "操作成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function initCorpSurveyCate(context) {
  try {
    const res = await db.collection("survery").aggregate([{ $group: { _id: "$corpId" } }]).toArray();
    const res2 = await db.collection("survery-cate").aggregate([{ $group: { _id: "$corpId" } }]).toArray();
    const corpIds = res.map((item) => item._id);
    const existsCorpIds = res2.map((item) => item._id);
    const diffCorpIds = corpIds.filter((item) => !existsCorpIds.includes(item));
    for (let corpId of diffCorpIds) {
      const data = {
        _id: common.generateRandomString(24),
        corpId,
        label: "默认",
        level: 1,
        createTime: Date.now(),
      };
      const res = await db.collection("survery-cate").insertOne(data);
      if (res.insertedId) {
        await db.collection("survery").updateMany(
          { corpId, cateId: { $exists: false } },
          { $set: { cateId: res.insertedId } }
        );
      }
    }
    return { success: true, data: diffCorpIds };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function ifCateExist(context) {
  const { corpId, cateId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const cate = await db.collection("survery-cate").findOne({ corpId, _id: cateId });
    return cate ? { success: true } : { success: false, message: "分类不存在" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

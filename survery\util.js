const data = require('./data');

exports.getSurveryData = function (payload) {
  const { name, description, list, status, enableScore, cateId } = payload;
  let message = verifyString(name, '问卷标题', 20);
  message = message || (cateId ? '' : '问卷分类不能为空');
  if (description) message = verifyString(description, '问卷说明', 1000);
  message = message || verifyList(list);
  if (message) {
    return { valid: false, message }
  }

  return {
    valid: true,
    message,
    survery: {
      name,
      cateId,
      enableScore: typeof enableScore === 'boolean' ? enableScore : false,
      status: data.SurveryStatus[status] ? status : 'init',
      description,
      list: getList(list)
    }
  }
}

function getList(list) {
  return list.map(i => ({
    extraCase: Array.isArray(i.extraCase) ? i.extraCase : [],
    extraValue: typeof i.extraValue === 'string' ? i.extraValue : '',
    id: i.id,
    maxlength: i.maxlength > 0 && i.maxlength <= 200 ? i.maxlength : 200,
    options: Array.isArray(i.options) ? i.options : [],
    require: Boolean(i.require),
    title: i.title,
    type: i.type,
    value: ''
  }))
}

const typeList = ['input', 'radio'];
function verifyList(list) {
  if (!Array.isArray(list) || list.length === 0) {
    return '问卷的题目个数不能为0'
  }
  let message = verifyRepeat(list, 'id', '问卷题目的id重复');
  if (message) return message;
  for (let item of list) {
    if (!typeList.includes(item.type)) {
      message = `无效的题目类型${item.type}`;
      break;
    }
    message = verifyString(item.title, '问卷题目的标题', 100);
    if (message) break;
    if (item.type !== 'radio') return '';
    message = verifyRepeat(item.options, 'value', '单选题的value重复');
    if (message) break;
    const options = Array.isArray(item.options) ? item.options : [];
    message = verifyOption(options)
    if (message) break;
  }
  return message;
}

function verifyOption(options) {
  let message = ''
  for (let item of options) {
    message = verifyString(item.label, '单选题的选项内容', 50);
    if (message) break;
    message = verifyString(item.value, '单选题的选项值', 50);
    if (message) break;
  }
  return message
}

function verifyString(str, title, maxlength) {
  if (typeof str !== 'string' || str.trim() === '') {
    return `${title}不能为空`
  }
  if (maxlength > 0 && str.length > maxlength) {
    return `${title}不能超过${maxlength}个字`
  }
  return ''
}

function verifyRepeat(list, key, message) {
  list = Array.isArray(list) ? list : [];
  const uniqueSet = new Set(list.map(item => item[key]));
  return Array.from(uniqueSet).length === list.length ? '' : message

}
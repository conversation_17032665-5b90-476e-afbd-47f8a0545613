const common = require("../../common.js");
let db = null;

exports.main = async (context, DB) => {
  db = DB;
  switch (context.type) {
    case "addCorpCommonWordCate":
      return await addCorpCommonWordCate(context);
    case "addUserCommonWordCate":
      return await addUserCommonWordCate(context);
    case "checkCateExist":
      return await checkCateExist(context);
    case "deleteCorpCommonWordCate":
      return await deleteCorpCommonWordCate(context);
    case "deleteUserCommonWordCate":
      return await deleteUserCommonWordCate(context);
    case "getCorpCommonWordCate":
      return await getCorpCommonWordCate(context);
    case "getUserCommonWordCate":
      return await getUserCommonWordCate(context);
    case "insertCateFromOldCate":
      return await insertCateFromOldCate(context);
    case "updateCorpCommonWordCate":
      return await updateCorpCommonWordCate(context);
    case "updateUserCommonWordCate":
      return await updateUserCommonWordCate(context);
    case "sortCorpCommonWordCate":
      return await sortCorpCommonWordCate(context);
  }
};

async function addCorpCommonWordCate(context) {
  let { corpId, label, parentId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    if (parentId) {
      const parent = await db
        .collection("common-words-cate")
        .findOne({ _id: parentId, corpId });
      if (!parent) return { success: false, message: "父级分类不存在" };
      if (![1, 2].includes(parent.level))
        return { success: false, message: "父级分类层级错误" };
      const res = await db.collection("common-words-cate").insertOne({
        _id: common.generateRandomString(24),
        corpId,
        type: "corp",
        label,
        level: parent.level + 1,
        parentId,
        createTime: Date.now(),
      });
      return { success: true, data: res, message: "添加机构常用分类成功" };
    }
    const res = await db.collection("common-words-cate").insertOne({
      _id: common.generateRandomString(24),
      corpId,
      type: "corp",
      label,
      level: 1,
      createTime: Date.now(),
    });
    return { success: true, data: res, message: "添加机构常用分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function addUserCommonWordCate(context) {
  let { corpId, label, userId } = context;
  if (!corpId || !userId)
    return { success: false, message: "机构id或者成员id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    const res = await db.collection("common-words-cate").insertOne({
      _id: common.generateRandomString(24),
      corpId,
      userId,
      type: "user",
      label,
      createTime: Date.now(),
    });
    return { success: true, data: res, message: "添加成员常用分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function updateCorpCommonWordCate(context) {
  let { corpId, id: _id, label } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    const res = await db
      .collection("common-words-cate")
      .updateOne(
        { _id, corpId, type: "corp" },
        { $set: { label: label.trim() } }
      );
    return { success: true, data: res, message: "更新机构常用分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function updateUserCommonWordCate(context) {
  let { corpId, id: _id, label, userId } = context;
  if (!corpId || !userId)
    return { success: false, message: "机构id或者成员id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    const res = await db
      .collection("common-words-cate")
      .updateOne(
        { _id, corpId, type: "user", userId },
        { $set: { label: label.trim() } }
      );
    return { success: true, data: res, message: "更新成员常用分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function deleteCorpCommonWordCate(context) {
  let { corpId, id: _id } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  try {
    const cate = await db
      .collection("common-words-cate")
      .findOne({ _id, corpId, type: "corp" });
    if (!cate) return { success: false, message: "机构常用分类不存在" };
    const ids = [_id];
    if (cate.level < 3) {
      const childrenIds = await db
        .collection("common-words-cate")
        .find({ corpId, type: "corp", parentId: _id })
        .project({ _id: 1 })
        .toArray();
      const childrenIdsArray = childrenIds.map((item) => item._id);
      if (cate.level === 1 && childrenIdsArray.length) {
        const grandChildrenIds = await db
          .collection("common-words-cate")
          .find({ corpId, type: "corp", parentId: { $in: childrenIdsArray } })
          .project({ _id: 1 })
          .toArray();
        ids.push(...grandChildrenIds.map((item) => item._id));
      }
      ids.push(...childrenIdsArray);
    }
    const res = await db
      .collection("common-words-cate")
      .deleteMany({ corpId, _id: { $in: ids } });
    const res2 = await db
      .collection("common-words")
      .deleteMany({ corpId, cateId: { $in: ids } });
    return {
      success: true,
      data: [res, res2],
      message: "删除机构常用分类成功",
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function deleteUserCommonWordCate(context) {
  let { corpId, id: _id, userId } = context;
  if (!corpId || !userId)
    return { success: false, message: "机构id或者成员id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  try {
    const cate = await db
      .collection("common-words-cate")
      .findOne({ _id, corpId, type: "user", userId });
    if (!cate) return { success: false, message: "成员常用分类不存在" };
    const res = await db
      .collection("common-words-cate")
      .deleteOne({ corpId, _id });
    const res2 = await db
      .collection("common-words")
      .deleteMany({ corpId, cateId: _id });
    return {
      success: true,
      data: [res, res2],
      message: "删除成员常用分类成功",
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function getCorpCommonWordCate(context) {
  let { corpId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const list = await db
      .collection("common-words-cate")
      .find({ corpId, type: "corp" })
      .sort({ createTime: 1 })
      .limit(10000)
      .toArray();
    if (list.length)
      return { success: true, list, message: "获取机构常用分类成功" };
    const initRecord = await db
      .collection("common-words-cate")
      .findOne({ corpId, type: "init" });
    if (initRecord)
      return { success: true, list: [], message: "获取机构常用分类成功" };
    await db
      .collection("common-words-cate")
      .insertOne({
        _id: common.generateRandomString(24),
        corpId,
        type: "init",
      });
    await db.collection("common-words-cate").insertOne({
      _id: common.generateRandomString(24),
      corpId,
      type: "corp",
      label: "默认",
      level: 1,
      createTime: Date.now(),
    });
    const newList = await db
      .collection("common-words-cate")
      .find({ corpId, type: "corp" })
      .sort({ createTime: 1 })
      .limit(10000)
      .toArray();
    return { success: true, list: newList, message: "获取机构常用分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function getUserCommonWordCate(context) {
  let { corpId, userId } = context;
  if (!corpId || !userId)
    return { success: false, message: "机构id或者用户id不能为空" };
  try {
    const list = await db
      .collection("common-words-cate")
      .find({ corpId, type: "user", userId })
      .sort({ createTime: 1 })
      .limit(10000)
      .toArray();
    return { success: true, list, message: "获取用户常用分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function insertCateFromOldCate() {
  try {
    const list = await db.collection("common-words")
      .aggregate([
        { $match: { type: "cate", cateType: "corp" } },
        { $project: { _id: 1, corpId: 1, name: 1 } },
        { $limit: 10000 },
      ])
      .toArray();
    const userCateList = await db.collection("common-words")
      .aggregate([
        { $match: { type: "cate", cateType: "user" } },
        { $project: { _id: 1, corpId: 1, name: 1, userId: 1 } },
        { $limit: 10000 },
      ])
      .toArray();

    const bulkOps = list.map((item) => ({
      insertOne: {
        document: {
          _id: item._id,
          corpId: item.corpId,
          label: item.name,
          level: 1,
          type: "corp",
          createTime: Date.now(),
        },
      },
    }));

    const userBulkOps = userCateList.map((item) => ({
      insertOne: {
        document: {
          _id: item._id,
          corpId: item.corpId,
          userId: item.userId,
          label: item.name,
          type: "user",
          createTime: Date.now(),
        },
      },
    }));

    await db.collection("common-words-cate").bulkWrite(bulkOps);
    await db.collection("common-words-cate").bulkWrite(userBulkOps);

    return { success: true, message: "操作成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function checkCateExist(context) {
  const { _id, corpId } = context;
  try {
    const cate = await db
      .collection("common-words-cate")
      .findOne({ _id, corpId });
    return {
      success: Boolean(cate),
      message: cate ? "获取分类成功" : "常用语分类不存在",
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function sortCorpCommonWordCate(context) {
  const { corpId, sortData } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  const arr = Array.isArray(sortData)
    ? sortData.filter(
        (i) =>
          i._id && typeof i.sort === "number" && i.sort % 1 === 0 && i.sort >= 0
      )
    : [];
  if (arr.length === 0) return { success: false, message: "参数错误" };

  try {
    const bulkOps = arr.map((item) => ({
      updateOne: {
        filter: {
          _id: item._id,
          corpId,
          type: "corp",
          parentId: { $exists: false },
        },
        update: { $set: { sort: item.sort } },
      },
    }));

    const res = await db.collection("common-words-cate").bulkWrite(bulkOps);
    return { success: true, message: "操作成功", data: res };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

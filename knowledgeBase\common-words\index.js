const cate = require("./cate");
const common = require("../../common");
let db = null;
let wordsDB = null;

exports.main = async (content, DB) => {
  db = DB;
  wordsDB = db.collection("common-words");
  switch (content.type) {
    case "getCommonWordsCates":
      return await exports.getCateList(content);
    case "removeCommonWordsCate":
      return await exports.removeCate(content);
    case "setCommonWordsCate":
      return await exports.setCate(content);
    case "setCommonWords":
      return await exports.setWords(content);
    case "getCommonWordsList":
      return await exports.getWordsList(content);
    case "removeCommonWords":
      return await exports.removeWords(content);
    case "getCommonWordsCount":
      return await exports.getWordsCount(content);
    case "addCorpCommonWordCate":
    case "addUserCommonWordCate":
    case "deleteCorpCommonWordCate":
    case "deleteUserCommonWordCate":
    case "getCorpCommonWordCate":
    case "getUserCommonWordCate":
    case "insertCateFromOldCate":
    case "updateCorpCommonWordCate":
    case "updateUserCommonWordCate":
    case "sortCorpCommonWordCate":
      return await cate.main(content, db);
  }
};

const CateType = {
  corp: "机构",
  user: "成员",
};
const WordsType = {
  corp: "机构",
  user: "成员",
};

exports.getCateList = async (context) => {
  const { corpId = "", userId, cateType = "corp" } = context;
  if (!corpId || !userId || !CateType[cateType])
    return { success: false, message: "操作失败" };
  try {
    const params = { corpId, type: "cate", cateType };
    if (params.cateType === "user") params.userId = userId;
    const query = wordsDB.find(params).sort({ createTime: -1 });
    const list = await query.toArray();
    return {
      success: true,
      message: "获取常用语分类成功",
      list,
    };
  } catch (e) {
    return {
      success: false,
      message: "获取常用语分类失败",
    };
  }
};

exports.removeCate = async (context) => {
  const { id: _id, corpId, cateType } = context;
  if (!_id || !corpId || !CateType[cateType])
    return { success: false, message: "操作失败" };
  try {
    const cate = await wordsDB.findOne({ _id, corpId, type: "cate", cateType });
    if (!cate) return { success: false, message: "常用语分类不存在" };
    await wordsDB.deleteMany({
      $or: [{ _id }, { cateId: _id, type: "words" }],
    });
    return { success: true, message: "删除常用语分类成功" };
  } catch (e) {
    return { success: false, message: "删除常用语分类失败" };
  }
};

exports.setCate = async (context) => {
  const { id: _id, corpId, userId, name, cateType = "corp" } = context;
  if (typeof name !== "string" || name.trim() === "")
    return { success: false, message: "分类名称不能为空" };
  if (!corpId || !userId || !CateType[cateType])
    return { success: false, message: "操作失败" };
  try {
    const res = await (_id
      ? updateCate(_id, name, userId, cateType)
      : addCate(corpId, userId, name, cateType));
    return res;
  } catch (e) {
    return {
      success: false,
      message:
        e && e.message
          ? e.message
          : e || `${_id ? "更新" : "新增"}常用语分类失败`,
    };
  }
};

async function addCate(corpId, userId, name, cateType) {
  try {
    const createTime = new Date().getTime();
    const data = {
      _id: common.generateRandomString(24),
      createTime,
      corpId,
      creator: userId,
      name,
      type: "cate",
      cateType,
    };
    if (cateType === "user") data.userId = userId;
    await wordsDB.insertOne(data);
    return { success: true, message: "新增常用语分类成功" };
  } catch (e) {
    return { success: false, message: e.message || "新增常用语分类失败" };
  }
}

async function updateCate(_id, name, userId, cateType) {
  try {
    const cate = await wordsDB.findOne({ _id, type: "cate", cateType });
    if (!cate) return { success: false, message: "常用语分类不存在" };
    if (cateType === "user" && cate.userId !== userId)
      return { success: false, message: "操作失败" };
    const data = { name, updateTime: new Date().getTime() };
    if (cateType === "corp") data.updateBy = userId;
    await wordsDB.updateOne({ _id }, { $set: data });
    return { success: true, message: "更新常用语分类成功" };
  } catch (e) {
    return { success: false, message: e.message || "更新常用语分类失败" };
  }
}

exports.setWords = async (context) => {
  const { cateId, corpId, content, userId, id: _id } = context;
  if (!cateId || !corpId || !userId)
    return { success: false, message: "操作失败" };
  if (typeof content !== "string" || content.trim() === "")
    return { success: false, message: "常用语内容不能为空" };
  try {
    const res = await (_id
      ? updateWords(_id, content, userId, cateId, corpId)
      : addWords(corpId, userId, cateId, content));
    return res;
  } catch (e) {
    console.log("setWords: ", e.message);
    return { success: false, message: `${_id ? "更新" : "新增"}常用语失败` };
  }
};

async function addWords(corpId, userId, cateId, content) {
  try {
    const { success, message } = await cate.main(
      {
        type: "checkCateExist",
        corpId,
        _id: cateId,
      },
      db
    );
    if (success) {
      const data = {
        _id: common.generateRandomString(24),
        createTime: new Date().getTime(),
        corpId,
        cateId,
        creator: userId,
        content,
        type: "words",
      };
      await wordsDB.insertOne(data);
      return { success: true, message: "新增常用语成功" };
    } else {
      return { success: false, message: message };
    }
  } catch (e) {
    return { success: false, message: "新增常用语失败" };
  }
}

async function updateWords(_id, content, userId, cateId, corpId) {
  try {
    const words = await wordsDB.findOne({ _id, type: "words" });
    if (!words) return { success: false, message: "常用语不存在" };
    if (words.cateId !== cateId) {
      const { success, message } = await cate.main(
        {
          type: "checkCateExist",
          corpId,
          cateId,
        },
        db
      );
      if (!success) return { success: false, message };
    }
    await wordsDB.updateOne(
      { _id },
      {
        $set: {
          cateId,
          content,
          updateTime: new Date().getTime(),
          updateBy: userId,
        },
      }
    );
    return { success: true, message: "更新常用语成功" };
  } catch (e) {
    return { success: false, message: "更新常用语失败" };
  }
}

exports.getWordsList = async (context) => {
  const {
    pageSize = 10,
    page = 1,
    cateId = "",
    cateIds,
    corpId,
    userId,
    keyword = "",
  } = context;
  if (!corpId || !userId) return { success: false, message: "查询失败" };
  try {
    const params = { corpId, type: "words" };
    if (cateId) params.cateId = cateId;
    if (Array.isArray(cateIds) && cateIds.length > 0)
      params.cateId = { $in: cateIds };
    if (typeof keyword === "string" && keyword.trim())
      params.content = { $regex: keyword.trim() };
    const query = wordsDB.find(params).sort({ createTime: -1 });
    const total = await query.count();
    const list = await query
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    return {
      success: true,
      message: "获取成功",
      list,
      total,
      pages: Math.ceil(total / pageSize),
    };
  } catch (e) {
    console.log("getList", e);
    return {
      success: false,
      message: "获取常用语列表数据失败",
    };
  }
};

exports.removeWords = async (context) => {
  try {
    const { id: _id, corpId } = context;
    const words = await wordsDB.findOne({ _id, corpId, type: "words" });
    if (words) {
      await wordsDB.deleteOne({ _id });
      return { success: true, message: "删除常用语成功" };
    } else {
      return { success: false, message: "常用语不存在" };
    }
  } catch (e) {
    return { success: false, message: "删除常用语失败" };
  }
};

exports.correctData = async () => {
  try {
    const list = await wordsDB
      .find({ type: "cate" }, { projection: { _id: 1, cateType: 1 } })
      .toArray();
    const corpCateIds = list
      .filter((i) => i.cateType === "corp")
      .map((i) => i._id)
      .filter(Boolean);
    const userCateIds = list
      .filter((i) => i.cateType === "user")
      .map((i) => i._id)
      .filter(Boolean);
    await wordsDB.updateMany(
      { cateId: { $in: corpCateIds } },
      { $set: { wordsType: "corp" } }
    );
    await wordsDB.updateMany(
      { cateId: { $in: userCateIds } },
      { $set: { wordsType: "user" } }
    );
  } catch (e) { }
};

exports.insertCateFromOldCate = async () => {
  try {
    const list = await wordsDB
      .aggregate([{ $match: { type: "cate", cateType: "corp" } }])
      .toArray();
    const userCateList = await wordsDB
      .aggregate([{ $match: { type: "cate", cateType: "user" } }])
      .toArray();

    const bulkOps = [];

    for (let item of list) {
      const data = {
        _id: common.generateRandomString(24),
        cateId: item._id,
        corpId: item.corpId,
        label: item.name,
        level: 1,
        type: "corp",
      };
      bulkOps.push({
        insertOne: {
          document: data,
        },
      });
    }

    for (let item of userCateList) {
      const data = {
        _id: common.generateRandomString(24),
        cateId: item._id,
        corpId: item.corpId,
        userId: item.userId,
        label: item.name,
        type: "user",
      };
      bulkOps.push({
        insertOne: {
          document: data,
        },
      });
    }

    if (bulkOps.length > 0) {
      await db.collection("common-words-cate").bulkWrite(bulkOps);
    }

    return { success: true, list };
  } catch (e) {
    return { success: false, message: e.message };
  }
};

exports.getWordsCount = async (context) => {
  const { corpId, cateIds } = context;
  if (!corpId) return { success: false, msg: "缺少corpId参数" };
  if (!Array.isArray(cateIds))
    return { success: false, msg: "缺少cateIds参数" };
  try {
    const total = await wordsDB
      .find({ corpId, cateId: { $in: cateIds } })
      .count();
    return { success: true, data: total };
  } catch (e) {
    return { success: false, msg: e.message };
  }
};

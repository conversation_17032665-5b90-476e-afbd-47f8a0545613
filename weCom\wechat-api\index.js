const request = require("../request");
exports.getWeChatUserInfo = async (context) => {
  // 获取access_token
  try {
    const { access_token = "", openid = "" } = await getWeChatAccessToken(context);
    if (!access_token || !openid)
      return {
        success: false,
        message: "获取失败",
      };
    let url = `https://api.weixin.qq.com/sns/userinfo?access_token=${access_token}&openid=${openid}`;
    let res = await request.main(url, null, "GET");
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};
async function getWeChatAccessToken(context) {
  let { code } = context;
  let url = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=wxce46d19ff09c1832&secret=595f894aecd2dccfe79afbcffca0b10e&code=${code}&grant_type=authorization_code`;
  let res = await request.main(url, null, "GET");
  return res;
}

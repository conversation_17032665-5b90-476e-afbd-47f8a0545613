// 院区管理工具函数

/**
 * 验证院区名称
 * @param {string} areaName 院区名称
 * @returns {object} 验证结果
 */
function validateAreaName(areaName) {
  if (!areaName || typeof areaName !== "string") {
    return { valid: false, message: "院区名称不能为空" };
  }
  
  const trimmedName = areaName.trim();
  if (trimmedName === "") {
    return { valid: false, message: "院区名称不能为空" };
  }
  
  if (trimmedName.length > 50) {
    return { valid: false, message: "院区名称不能超过50个字符" };
  }
  
  return { valid: true, value: trimmedName };
}

/**
 * 验证院区ID
 * @param {string} areaId 院区ID
 * @returns {object} 验证结果
 */
function validateAreaId(areaId) {
  if (!areaId || typeof areaId !== "string") {
    return { valid: false, message: "院区ID不能为空" };
  }
  
  const trimmedId = areaId.trim();
  if (trimmedId === "") {
    return { valid: false, message: "院区ID不能为空" };
  }
  
  if (trimmedId.length > 20) {
    return { valid: false, message: "院区ID不能超过20个字符" };
  }
  
  // 检查是否包含特殊字符（只允许字母、数字、下划线、中划线）
  const validPattern = /^[a-zA-Z0-9_-]+$/;
  if (!validPattern.test(trimmedId)) {
    return { valid: false, message: "院区ID只能包含字母、数字、下划线和中划线" };
  }
  
  return { valid: true, value: trimmedId };
}

/**
 * 验证院区介绍
 * @param {string} areaDescription 院区介绍
 * @returns {object} 验证结果
 */
function validateAreaDescription(areaDescription) {
  if (areaDescription === null || areaDescription === undefined) {
    return { valid: true, value: "" };
  }
  
  if (typeof areaDescription !== "string") {
    return { valid: false, message: "院区介绍格式不正确" };
  }
  
  const trimmedDesc = areaDescription.trim();
  
  if (trimmedDesc.length > 500) {
    return { valid: false, message: "院区介绍不能超过500个字符" };
  }
  
  return { valid: true, value: trimmedDesc };
}

/**
 * 验证状态值
 * @param {number} status 状态值
 * @returns {object} 验证结果
 */
function validateStatus(status) {
  if (status === null || status === undefined) {
    return { valid: true, value: 1 }; // 默认正常状态
  }
  
  if (![0, 1].includes(status)) {
    return { valid: false, message: "状态值无效，只能是0或1" };
  }
  
  return { valid: true, value: status };
}

/**
 * 构建查询条件
 * @param {object} params 查询参数
 * @param {string} corpId 机构ID
 * @returns {object} MongoDB查询条件
 */
function buildQueryConditions(params, corpId) {
  const { status, keyword } = params;
  const query = { corpId };
  
  // 状态筛选
  if (status !== undefined && status !== null && status !== "") {
    query.status = status;
  }
  
  // 关键词搜索
  if (keyword && keyword.trim()) {
    const keywordRegex = new RegExp(keyword.trim(), "i");
    query.$or = [
      { areaName: keywordRegex },
      { areaId: keywordRegex },
      { areaDescription: keywordRegex }
    ];
  }
  
  return query;
}

/**
 * 构建排序条件
 * @param {string} sortBy 排序字段
 * @param {number} sortOrder 排序方向
 * @returns {object} MongoDB排序条件
 */
function buildSortConditions(sortBy = "createTime", sortOrder = -1) {
  const allowedSortFields = ["createTime", "updateTime", "areaName", "areaId"];
  const finalSortBy = allowedSortFields.includes(sortBy) ? sortBy : "createTime";
  const finalSortOrder = [1, -1].includes(sortOrder) ? sortOrder : -1;
  
  const sort = {};
  sort[finalSortBy] = finalSortOrder;
  
  return sort;
}

/**
 * 格式化院区数据（用于返回给前端）
 * @param {object} area 院区数据
 * @returns {object} 格式化后的院区数据
 */
function formatAreaData(area) {
  if (!area) return null;
  
  return {
    _id: area._id,
    corpId: area.corpId,
    areaName: area.areaName,
    areaId: area.areaId,
    areaDescription: area.areaDescription || "",
    status: area.status,
    statusText: area.status === 1 ? "正常" : "禁用",
    createTime: area.createTime,
    updateTime: area.updateTime
  };
}

/**
 * 格式化院区列表（用于返回给前端）
 * @param {Array} areas 院区列表
 * @returns {Array} 格式化后的院区列表
 */
function formatAreaList(areas) {
  if (!Array.isArray(areas)) return [];
  
  return areas.map(area => formatAreaData(area));
}

/**
 * 生成院区ID（如果不提供的话）
 * @param {string} areaName 院区名称
 * @param {string} corpId 机构ID
 * @returns {string} 生成的院区ID
 */
function generateAreaId(areaName, corpId) {
  // 提取院区名称的拼音首字母或简化版本
  const timestamp = Date.now().toString().slice(-6);
  const corpIdSuffix = corpId.slice(-3);
  
  // 简单的ID生成策略，实际项目中可能需要更复杂的逻辑
  return `AREA_${corpIdSuffix}_${timestamp}`;
}

module.exports = {
  validateAreaName,
  validateAreaId,
  validateAreaDescription,
  validateStatus,
  buildQueryConditions,
  buildSortConditions,
  formatAreaData,
  formatAreaList,
  generateAreaId
};

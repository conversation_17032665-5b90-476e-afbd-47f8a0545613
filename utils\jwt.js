const jwt = require('jsonwebtoken');

const SECRET_KEY = process.env.CONFIG_JWT_SECRET_KEY || 'YOUCAN365';
const TOKEN_EXPIRES = process.env.CONFIG_JWT_EXPIRE_TIME || '1d'

function encodeJWT(payload, expiresIn = TOKEN_EXPIRES) {
  return jwt.sign(payload, SECRET_KEY, { expiresIn });
}

function decodeJWT(token) {
  try {
    return jwt.decode(token, SECRET_KEY);
  } catch (error) {
    throw new Error('Invalid token' + error.message);
  }
}

function verifyJWT(token) {
  try {
    return jwt.verify(token, SECRET_KEY);
  } catch (error) {
    console.log('JWT verification failed:', error.message);
    return false
  }
}

module.exports = {
  encodeJWT,
  decodeJWT,
  verifyJWT
}
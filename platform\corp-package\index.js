const dayjs = require('dayjs');
const request = require('../request');
const corpToekn = require('../corp-token');
const { ObjectId } = require('mongodb');



exports.updatePackage = async (item, db) => {
  const { env, id, params, corp_id, corpId } = item;
  if (!id) {
    return {
      success: false,
      message: '套餐ID未传',
    };
  }
  try {
    const { packageStartTime, packageTime, givePackageTime } = params;
    if (params.packageStatus !== 'closed') {
      const day1 = dayjs(packageStartTime).add(packageTime, 'month');
      const packageEndTime = dayjs(day1).add(givePackageTime, 'day').subtract(1, 'day').format('YYYY-MM-DD');
      params['packageStartTime'] = dayjs(packageStartTime).valueOf();
      params['packageEndTime'] = dayjs(packageEndTime).valueOf();
      const count = await db
        .collection('package-list')
        .countDocuments({
          corpId: corp_id,
          packageStartTime: { $lte: dayjs().valueOf() },
          packageEndTime: { $gte: params.packageStartTime },
          packageStatus: { $ne: 'closed' },
          _id: { $ne: id},
        });
      if (count > 0) {
        return {
          success: false,
          message: '开始时间不得小于正在进行套餐的终止时间',
        };
      }
    }
    await db.collection('package-list').updateOne(
      { _id: id }, 
      { $set: params }
    );
    if (params.packageStatus === 'closed') {
      await stopCorpPackage(corp_id, db);
    }
    const endTime = dayjs(params.packageEndTime).format('YYYY-MM-DD');
    const diffEndtTime = dayjs().diff(endTime, 'days');
    if (diffEndtTime <= 0) {
      const { accountCount, giveAccountCount } = params;
      const totalAccount = accountCount + giveAccountCount;
      await db.collection('corp').updateOne(
        { _id: corpId },
        { $set: { package: params } }
      );
      await disabledAccount(corpId, totalAccount, db);
    }
    return {
      success: true,
      message: '更新成功',
    };
  } catch (error) {
    return {
      success: false,
      message: '更新失败',
    };
  }
};

async function disabledAccount(corpId, totalAccount, db) {
  // 获取账号数量
  const accountList = await db
    .collection('corp-member')
    .find({
      corpId,
      open_userid: { $exists: true },
    })
    .toArray();
  const accountCount = accountList.length;
  if (accountCount > totalAccount) {
    const diff = accountCount - totalAccount;
    let disableList = accountList
      .filter((item) => item.roleType !== 'superAdmin' && item.accountState !== 'disable')
      .slice(0, diff);
    let disabledAccountUserIds = disableList.map((item) => item.open_userid);
    // await db
    //   .collection("corp-member")
    //   .updateMany(
    //     {
    //       corpId,
    //       open_userid: { $in: disabledAccountUserIds },
    //     },
    //     { $set: { accountState: "disable" } }
    //   );
  }
}

exports.removePackage = async (item, db) => {
  const { env, id } = item;
  try {
    await db.collection('package-list').deleteOne({ _id: id });
    return {
      success: true,
      message: '删除成功',
    };
  } catch (error) {
    return {
      success: false,
      message: '删除失败',
    };
  }
};

exports.addPackage = async (item, db) => {
  const { env, params, corp_id, corpId } = item;
  try {
    const { packageStartTime, packageTime, givePackageTime } = params;
    const day1 = dayjs(packageStartTime).add(packageTime, 'month');
    const packageEndTime = dayjs(day1).add(givePackageTime, 'day').subtract(1, 'day').format('YYYY-MM-DD');
    params['createTime'] = dayjs().valueOf();
    params['packageStartTime'] = dayjs(packageStartTime).valueOf();
    params['packageEndTime'] = dayjs(packageEndTime).valueOf();
    // 如果套餐开始时间小于等于今天，状态为init(初始化状态)
    if (dayjs(packageStartTime).isSame(dayjs(), 'day')) {
      params['packageStatus'] = 'init';
    } else {
      params['packageStatus'] = 'other';
    }

    const count = await db
      .collection('package-list')
      .countDocuments({
        corpId: corp_id,
        packageEndTime: { $gte: params.packageStartTime },
        packageStatus: { $ne: 'closed' },
      });
    if (count > 0) {
      return {
        success: false,
        message: '开始时间不得小于正在进行套餐的终止时间',
      };
    }
    const result = await db.collection('package-list').insertOne(params);
    await updateCorpPackage(params, db);
    // await disabledAccount(
    //   corpId,
    //   params.accountCount + params.giveAccountCount,
    //   db
    // );
    return {
      success: true,
      id: result.insertedId,
      message: '新增成功',
    };
  } catch (error) {
    return {
      success: false,
      message: '新增失败',
    };
  }
};

exports.getPackageList = async (item, db) => {
  const { env, page, pageSize, corpId } = item;
  try {
    let query = {
      corpId,
    };
    const total = await db.collection('package-list').countDocuments(query); //总数
    const pages = Math.ceil(total / pageSize);
    const corpList = await db
      .collection('package-list')
      .find(query)
      .sort({ createTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    return {
      total: total,
      pages: pages,
      size: pageSize,
      data: corpList,
      success: true,
      message: '获取成功',
    };
  } catch (error) {
    return {
      success: false,
      message: '获取失败',
    };
  }
};

async function updateCorpPackage(package, db) {
  try {
    const { corpId, ...rest } = package;
    await db.collection('corp').updateOne(
      { _id: corpId},
      { $set: { package: rest } }
    );
    return {
      success: true,
      message: '获取成功',
    };
  } catch (error) {
    return {
      success: false,
      message: '获取失败',
    };
  }
}

async function stopCorpPackage(corpId, db) {
  await db.collection('corp').updateOne(
    { _id: corpId},
    { $set: { 'package.packageStatus': 'closed' } }
  );
}

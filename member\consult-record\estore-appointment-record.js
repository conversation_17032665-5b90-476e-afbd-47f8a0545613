const dayjs = require("dayjs");
const utils = require("../utils.js");
const eConsultRecord = require("./e-consult-record");
const { ObjectId } = require("mongodb"); // 添加 ObjectId 导入
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    // 新增的预约记录CRUD操作
    case "getAppointmentRecord":
      return getAppointmentRecord(content);
    case "addAppointmentRecord":
      return addAppointmentRecord(content);
    case "updateAppointmentRecord":
      return updateAppointmentRecord(content);
    case "deleteAppointmentRecord":
      return deleteAppointmentRecord(content);
    case "updateAppointmentStatusToFailToHospital":
      return updateAppointmentStatusToFailToHospital(content);
    case "batchUpdateAppointmentRecordStatus":
      return batchUpdateAppointmentRecordStatus(content);
    default:
      return { success: false, message: "未找到对应的处理方法", data: null };
  }
};
// 获取预约记录
async function getAppointmentRecord(content) {
  try {
    const {
      id,
      page = 1,
      pageSize = 10,
      introducers,
      startAppointmentTime,
      endAppointmentTime,
      name,
      mobile,
      customerId,
      appointmentStatus,
      projectIds,
      infoSource,
    } = content || {};
    let query = {};
    // 构建查询条件
    if (id) query._id = id;
    if (introducers && Array.isArray(introducers) && introducers.length > 0)
      query.introducer = { $in: introducers };
    // 时间范围查询
    if (startAppointmentTime && endAppointmentTime) {
      query.appointmentTime = {
        $gte: dayjs(startAppointmentTime).startOf("day").valueOf(),
        $lte: dayjs(endAppointmentTime).endOf("day").valueOf(),
      };
    }
    if (projectIds && Array.isArray(projectIds) && projectIds.length > 0) {
      query.projectIds = { $in: projectIds };
    }
    if (infoSource && Array.isArray(infoSource) && infoSource.length > 0) {
      query.source = { $in: infoSource };
    }
    // 模糊查询
    if (typeof name === "string" && name.trim()) {
      query.name = { $regex: ".*" + name.trim(), $options: "i" };
    }
    if (typeof mobile === "string" && mobile.trim()) {
      query.mobile = { $regex: ".*" + mobile.trim(), $options: "i" };
    }
    if (customerId) query.customerId = customerId;
    if (appointmentStatus) query.appointmentStatus = appointmentStatus;
    // 分页查询
    const total = await db.collection("estore-appointment-record").count(query);
    const list = await db
      .collection("estore-appointment-record")
      .find(query)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .sort({ appointmentTime: 1 })
      .toArray();
    return {
      success: true,
      message: "获取预约记录成功",
      data: {
        list,
        total,
        page,
        pageSize,
      },
    };
  } catch (error) {
    console.error("获取预约记录失败:", error);
    return {
      success: false,
      message: "获取预约记录失败: " + error.message,
      data: null,
    };
  }
}

// 添加预约记录
async function addAppointmentRecord(content) {
  try {
    const appointmentData = content.params;
    if (!appointmentData)
      return { success: false, message: "预约数据不能为空", data: null };
    // 检查当天是否已有预约记录
    if (appointmentData.customerId) {
      const existingAppointment = await db
        .collection("estore-appointment-record")
        .findOne({
          customerId: appointmentData.customerId,
          appointmentStatus: "notInHospital",
        });
      if (existingAppointment) {
        // 更新已存在的预约记录的时间
        await db.collection("estore-appointment-record").updateOne(
          { _id: existingAppointment._id },
          {
            $set: {
              appointmentTime: appointmentData.appointmentTime,
              updateTime: dayjs().valueOf(),
            },
          }
        );
        return {
          success: true,
          message: "已更新现有预约记录的时间",
          data: existingAppointment._id,
        };
      }
    }
    // 添加创建时间和更新时间
    appointmentData.createTime = dayjs().valueOf();
    appointmentData.updateTime = appointmentData.createTime;
    // 获取当前患者的报备记录
    const eRecord = await eConsultRecord.main(
      {
        type: "getEConsuleRecord",
        customerId: appointmentData.customerId,
        page: 1,
        pageSize: 1,
        corpId: content.corpId,
      },
      db
    );
    if (eRecord.success && eRecord.list.length > 0) {
      const {
        date: eConusltTime,
        projectIds,
        projectNames,
        source,
        reportDesc,
      } = eRecord.list[0];
      appointmentData.eConusltTime = eConusltTime;
      appointmentData.projectIds = projectIds;
      appointmentData.projectNames = projectNames;
      appointmentData.source = source;
      appointmentData.reportDesc = reportDesc;
    }
    appointmentData.appointmentStatus = "notInHospital";
    const result = await db
      .collection("estore-appointment-record")
      .insertOne(appointmentData);
    return {
      success: true,
      message: "添加预约记录成功",
      data: result.insertedId,
    };
  } catch (error) {
    console.error("添加预约记录失败:", error);
    return {
      success: false,
      message: "添加预约记录失败: " + error.message,
      data: null,
    };
  }
}

// 更新预约记录
async function updateAppointmentRecord(content) {
  try {
    const { id, ...updateData } = content;
    if (!id) return { success: false, message: "记录ID不能为空", data: null };

    // 添加更新时间
    updateData.updateTime = dayjs().valueOf();

    const result = await db
      .collection("estore-appointment-record")
      .updateOne({ _id: new ObjectId(id) }, { $set: updateData });

    if (result.matchedCount === 0) {
      return { success: false, message: "未找到该预约记录", data: null };
    }
    return {
      success: true,
      message: "更新预约记录成功",
      data: {
        updated: result.modifiedCount > 0,
        id,
      },
    };
  } catch (error) {
    console.error("更新预约记录失败:", error);
    return {
      success: false,
      message: "更新预约记录失败: " + error.message,
      data: null,
    };
  }
}

// 删除预约记录
async function deleteAppointmentRecord(content) {
  try {
    const { id } = content;
    if (!id) return { success: false, message: "记录ID不能为空", data: null };

    const result = await db
      .collection("estore-appointment-record")
      .deleteOne({ _id: new ObjectId(id) });

    if (result.deletedCount === 0) {
      return {
        success: false,
        message: "未找到该预约记录或已被删除",
        data: null,
      };
    }

    return {
      success: true,
      message: "删除预约记录成功",
      data: { deleted: true, id },
    };
  } catch (error) {
    console.error("删除预约记录失败:", error);
    return {
      success: false,
      message: "删除预约记录失败: " + error.message,
      data: null,
    };
  }
}

/**
 *  预约记录状态  预约到院状态: 已到院:reportDesc 未到院: notInHospital   爽约未到院: failToHospital    appointmentStatus
 */
async function updateAppointmentStatusToFailToHospital() {
  try {
    const today = dayjs();
    const yesterday = today.subtract(1, "day");
    const startOfDay = yesterday.startOf("day").valueOf();
    const endOfDay = yesterday.endOf("day").valueOf();
    const updateData = {
      appointmentStatus: "failToHospital",
      updateTime: today.valueOf(),
    };
    const result = await db.collection("estore-appointment-record").updateMany(
      {
        appointmentTime: { $lte: endOfDay },
        appointmentStatus: "notInHospital",
      },
      { $set: updateData }
    );
    if (result.matchedCount === 0) {
      return { success: false, message: "未找到今日的预约记录", data: null };
    }
    return {
      success: true,
      message: "更新预约状态为爽约未到院成功",
      data: {
        updated: result.modifiedCount > 0,
      },
    };
  } catch (error) {
    console.error("更新预约状态为爽约未到院失败:", error);
    return {
      success: false,
      message: "更新预约状态为爽约未到院失败: " + error.message,
      data: null,
    };
  }
}

// 批量更新预约记录状态
async function batchUpdateAppointmentRecordStatus(content) {
  try {
    const { ids, appointmentStatus } = content;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return { success: false, message: "记录ID列表不能为空", data: null };
    }

    if (!appointmentStatus) {
      return { success: false, message: "预约状态不能为空", data: null };
    }

    // 将字符串ID转换为ObjectId
    const objectIds = ids.map((id) => new ObjectId(id));

    const updateData = {
      appointmentStatus,
      updateTime: dayjs().valueOf(),
    };

    const result = await db
      .collection("estore-appointment-record")
      .updateMany({ _id: { $in: objectIds } }, { $set: updateData });

    return {
      success: true,
      message: "批量更新预约记录状态成功",
      data: {
        matchedCount: result.matchedCount,
        modifiedCount: result.modifiedCount,
        ids,
      },
    };
  } catch (error) {
    console.error("批量更新预约记录状态失败:", error);
    return {
      success: false,
      message: "批量更新预约记录状态失败: " + error.message,
      data: null,
    };
  }
}

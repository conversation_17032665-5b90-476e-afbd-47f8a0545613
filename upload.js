const multer = require("multer");
const path = require("path");
const express = require("express");
const fs = require("fs");
const api = require("./api");

// 设置存储配置
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, "../uploads");
    // 确保目标目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    // 确定文件存储的目录为项目根目录的 'uploads/corpId' 文件夹
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 给文件添加一个唯一的前缀，以防重名冲突
    const fileName = Buffer.from(file.originalname, "latin1").toString("utf8");
    cb(null, `${Date.now()}-${fileName}`);
  },
});
// 文件类型过滤（可选）
const fileFilter = (req, file, cb) => {
  // Accept all file types
  return cb(null, true);
};
// 创建multer实例，并应用存储配置和文件过滤
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
});

exports.uploadFile = (app) => {
  // 设置静态文件目录
  app.use("/uploads", express.static(path.join(__dirname, "../uploads")));
  // 创建上传路由
  app.post("/upload", upload.single("file"), async (req, res) => {
    try {
      const { file } = req;
      const { corpId = "default", getType, sendType, name } = req.body;
      if (getType === "image" || getType === "mediaId") {
        const { media_id, success, url } = await getWechatTemp({
          corpId,
          sendType,
          name,
          file,
          getType,
        });
        if (!success) {
          return res.status(500).json({
            message: "文件上传失败",
            success: false,
          });
        }
        // 返回media_id
        return res.status(200).json({
          message: "文件上传成功",
          media_id,
          url,
          success: true,
        });
      }
      // 文件存储路径
      const filePath = path.join("uploads", file.filename);
      const normalizedPath = filePath.replace(/\\/g, "/");
      // 返回文件路径和成功消息
      res.status(200).json({
        message: "文件上传成功",
        filePath: normalizedPath,
        success: true,
      });
    } catch (error) {
      // 如果发生错误，返回错误消息
      console.error(error);
      res.status(500).json({
        message: "文件上传失败",
        error: error.message,
        success: false,
      });
    }
  });
};

async function getWechatTemp(item) {
  const { corpId = "", getType, sendType, name, file } = item;
  // uploadTempImage
  const { media_id, success, url } = await api.getWecomApi({
    type: getType === "image" ? "uploadTempImage" : "uploadTempMedia",
    corpId,
    sendType,
    name,
    file,
  });
  if (!success) {
    return {
      message: "文件上传失败",
      success: false,
    };
  }
  return {
    message: "文件上传成功",
    media_id,
    url,
    success: true,
  };
}

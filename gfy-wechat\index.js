const axios = require("axios");

/**
 * gfy-wechat 模块主入口
 * @param {Object} content 请求内容
 * @returns {Object} 响应结果
 */
exports.main = async (content) => {
  switch (content.type) {
    case "generateMiniProgramLink":
      return await generateMiniProgramLink(content);
    default:
      return {
        success: false,
        message: "未找到对应的操作类型",
      };
  }
};

/**
 * 获取微信Access Token
 * @returns {Object} token结果
 */
async function getWxToken() {
  try {
    const response = await axios.post(
      "https://internethospital.gyfyy.com:10053/elho-hook/getWxToken",
      {
        appid: "wxd9825560125f0e75",
        appsecret: "484c624981e02ca09bf633d580654a9a",
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    // 根据用户说明，这个接口直接返回token
    if (
      response.data &&
      typeof response.data === "string" &&
      response.data.length > 0
    ) {
      // 如果直接返回token字符串
      return {
        success: true,
        token: response.data.trim(), // 去除可能的空格
      };
    } else if (response.data && response.data.access_token) {
      // 如果返回标准格式
      return {
        success: true,
        token: response.data.access_token,
      };
    } else {
      return {
        success: false,
        message: "获取token失败",
        error: response.data,
      };
    }
  } catch (error) {
    return {
      success: false,
      message: "获取token异常",
      error: error.message,
    };
  }
}

/**
 * 生成小程序链接（使用URL Scheme）
 * @param {Object} params 参数
 * @param {string} params.path 小程序页面路径，例如：pages/tab_bar/hospHome/hospHome?actionType=89&promotionCode=21&hisDrId=123
 * @param {string} params.hisDrId 医生工号
 * @param {boolean} params.is_expire 是否设置过期时间，默认false
 * @param {number} params.expire_time 过期时间戳（秒），is_expire为true时必填
 * @returns {Object} 生成结果
 */
async function generateMiniProgramLink(params) {
  const { path, hisDrId, is_expire = false, expire_time } = params;

  if (!path) {
    return {
      success: false,
      message: "页面路径不能为空",
    };
  }

  try {
    // 1. 获取access token
    const tokenResult = await getWxToken();
    if (!tokenResult.success) {
      return tokenResult;
    }

    // 2. 构建完整的小程序路径
    let query = "";
    if (hisDrId) {
      // 如果提供了医生工号，添加到路径中
      query = `actionType=89&promotionCode=21&hisDrId=${hisDrId}`;
    }

    // 3. 构建请求参数
    const requestData = {
      jump_wxa: {
        path,
        query,
      },
      is_expire: is_expire,
    };

    // 如果设置了过期时间，添加过期时间戳
    if (is_expire && expire_time) {
      requestData.expire_time = expire_time;
    }

    // 4. 调用微信generatescheme接口
    const generateResponse = await axios.post(
      `https://api.weixin.qq.com/wxa/generatescheme?access_token=${tokenResult.token}`,
      requestData,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (generateResponse.data && generateResponse.data.errcode === 0) {
      return {
        success: true,
        message: "生成URL Scheme成功",
        data: {
          openlink: generateResponse.data.openlink,
          path,
          hisDrId: hisDrId,
          type: "url_scheme",
          is_expire: is_expire,
          expire_time: expire_time,
        },
      };
    } else {
      return {
        success: false,
        message: "生成URL Scheme失败",
        error: generateResponse.data,
      };
    }
  } catch (error) {
    return {
      success: false,
      message: "生成URL Scheme异常",
      error: error.message,
    };
  }
}

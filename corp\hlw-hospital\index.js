const common = require("../../common");
const utils = require("../utils");
const dayjs = require("dayjs");
let db = null;

/**
 * 获取指定科室的排序编号
 * @param {Object} sortOrder - 排序对象
 * @param {string} deptId - 科室ID
 * @returns {number} 排序编号
 */
const getDeptSortOrder = (sortOrder, deptId) => {
  if (!sortOrder || typeof sortOrder !== 'object') {
    return 0;
  }
  return sortOrder[deptId] || 0;
};

/**
 * 设置指定科室的排序编号
 * @param {Object} sortOrder - 当前排序对象
 * @param {string} deptId - 科室ID
 * @param {number} value - 排序编号
 * @returns {Object} 更新后的排序对象
 */
const setDeptSortOrder = (sortOrder, deptId, value) => {
  const result = (sortOrder && typeof sortOrder === 'object') ? { ...sortOrder } : {};
  result[deptId] = value;
  return result;
};

/**
 * 获取全局排序编号（用于不区分科室的排序）
 * @param {Object} sortOrder - 排序对象
 * @returns {number} 全局排序编号
 */
const getGlobalSortOrder = (sortOrder) => {
  if (!sortOrder || typeof sortOrder !== 'object') {
    return 0;
  }
  const values = Object.values(sortOrder);
  return values.length > 0 ? Math.min(...values) : 0;
};

exports.main = async (event, mongodb) => {
  db = mongodb;
  switch (event.type) {
    case "getHlwHospitalMembers":
      return await exports.getHlwHospitalMembers(event);
    case "getHlwHospitalMemberById":
      return await exports.getHlwHospitalMemberById(event);
    case "getHlwHospitalMemberByRelatedUserId":
      return await exports.getHlwHospitalMemberByRelatedUserId(event);
    case "getHlwHospitalMemberByWorkId":
      return await exports.getHlwHospitalMemberByWorkId(event);
    case "addHlwHospitalMember":
      return await exports.addHlwHospitalMember(event);
    case "updateHlwHospitalMember":
      return await exports.updateHlwHospitalMember(event);
    case "removeHlwHospitalMember":
      return await exports.removeHlwHospitalMember(event);
    case "deleteHlwHospitalMember":
      return await exports.deleteHlwHospitalMember(event);
    case "getHlwHospitalMembersByDept":
      return await exports.getHlwHospitalMembersByDept(event);
    case "getHlwHospitalMembersByJob":
      return await exports.getHlwHospitalMembersByJob(event);
    case "getRecommendedMembers":
      return await exports.getRecommendedMembers(event);
    case "updateMemberRecommendStatus":
      return await exports.updateMemberRecommendStatus(event);
    case "batchUpdateMembers":
      return await exports.batchUpdateMembers(event);
    case "searchMembers":
      return await exports.searchMembers(event);
    case "getMembersByOutpatientDept":
      return await exports.getMembersByOutpatientDept(event);
    case "hlwHospitalMemberExist":
      return await exports.hlwHospitalMemberExist(event);
    case "getHlwHospitalMemberStats":
      return await exports.getHlwHospitalMemberStats(event);
    case "batchUpdateMembersSortOrder":
      return await exports.batchUpdateMembersSortOrder(event);
    default:
      return {
        success: false,
        message: "未知操作类型",
      };
  }
};

/**
 * 获取医院人员列表（分页）
 * @param {Object} context - 查询参数
 * @param {number} context.page - 页码
 * @param {number} context.pageSize - 每页数量
 * @param {Object} context.params - 查询条件
 * @param {string} context.params.corpId - 机构ID
 * @param {string} context.params.name - 姓名（模糊查询）
 * @param {string} context.params.gender - 性别
 * @param {Array} context.params.deptId - 所属科室ID数组
 * @param {string} context.params.job - 岗位
 * @param {string} context.params.outpatientDept - 门诊科室
 * @param {number} context.params.recommend - 推荐状态（1/0）
 * @param {string} context.params.relatedUserId - 关联用户ID
 * @returns {Object} 返回结果
 */
exports.getHlwHospitalMembers = async (context) => {
  try {
    const { page = 1, pageSize = 10, params = {} } = context;
    const { corpId, name, gender, deptId, job, outpatientDept, recommend, relatedUserId } = params;
    
    if (!corpId) {
      return {
        success: false,
        message: "机构ID不能为空",
      };
    }

    let query = { corpId };
    
    // 添加查询条件
    if (name) {
      query.name = { $regex: name, $options: "i" };
    }
    if (gender) {
      query.gender = gender;
    }
    if (deptId) {
      if (Array.isArray(deptId)) {
        // 如果传入的是数组，查询 deptId 数组中包含任一元素的记录
        query.deptId = { $elemMatch: { $in: deptId } };
      } else {
        // 兼容单个科室ID的查询，查询 deptId 数组中包含该元素的记录
        query.deptId = deptId;
      }
    }
    if (job) {
      query.job = job;
    }
    if (outpatientDept) {
      query.outpatientDept = outpatientDept;
    }
    if (recommend !== undefined) {
      query.recommend = recommend;
    }
    if (relatedUserId) {
      query.relatedUserId = relatedUserId;
    }

    const total = await db.collection("hlw-hospital").countDocuments(query);
    const pages = Math.ceil(total / pageSize);
    
    // 构建聚合管道，支持对象格式的sortOrder
    const pipeline = [
      { $match: query },
      {
        $addFields: {
          effectiveSortOrder: {
            $cond: {
              if: deptId && !Array.isArray(deptId),
              // 如果查询单个科室，使用该科室的排序
              then: { $ifNull: [`$sortOrder.${deptId}`, 999999] },
              // 否则使用全局排序（取最小值）
              else: {
                $ifNull: [
                  {
                    $min: {
                      $map: {
                        input: { $objectToArray: "$sortOrder" },
                        as: "item",
                        in: "$$item.v"
                      }
                    }
                  },
                  999999
                ]
              }
            }
          }
        }
      },
      { $sort: { effectiveSortOrder: 1, createTime: -1 } },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize },
      { $project: { effectiveSortOrder: 0 } } // 移除临时字段
    ];
    
    const data = await db.collection("hlw-hospital").aggregate(pipeline).toArray();

    return {
      success: true,
      message: "获取成功",
      data,
      total,
      pages,
      page,
      pageSize,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

/**
 * 根据ID获取医院人员信息
 * @param {Object} context - 查询参数
 * @param {string} context.id - 人员ID
 * @param {string} context.corpId - 机构ID
 * @returns {Object} 返回结果
 */
exports.getHlwHospitalMemberById = async (context) => {
  try {
    const { id, corpId } = context;
    
    if (!id || !corpId) {
      return {
        success: false,
        message: "参数不能为空",
      };
    }

    const data = await db
      .collection("hlw-hospital")
      .findOne({ _id: id, corpId });

    if (!data) {
      return {
        success: false,
        message: "人员不存在",
      };
    }

    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

/**
 * 根据关联用户ID获取医院人员信息
 * @param {Object} context - 查询参数
 * @param {string} context.relatedUserId - 关联用户ID
 * @param {string} context.corpId - 机构ID
 * @returns {Object} 返回结果
 */
exports.getHlwHospitalMemberByRelatedUserId = async (context) => {
  try {
    const { relatedUserId, corpId } = context;
    
    if (!relatedUserId || !corpId) {
      return {
        success: false,
        message: "参数不能为空",
      };
    }

    const data = await db
      .collection("hlw-hospital")
      .findOne({ relatedUserId, corpId });

    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

/**
 * 根据院内工号获取医院人员信息
 * @param {Object} context - 查询参数
 * @param {string} context.workId - 院内工号
 * @param {string} context.corpId - 机构ID
 * @returns {Object} 返回结果
 */
exports.getHlwHospitalMemberByWorkId = async (context) => {
  try {
    const { workId, corpId } = context;
    
    if (!workId || !corpId) {
      return {
        success: false,
        message: "参数不能为空",
      };
    }

    const data = await db
      .collection("hlw-hospital")
      .findOne({ workId, corpId });

    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

/**
 * 新增医院人员
 * @param {Object} context - 人员信息
 * @param {string} context.corpId - 机构ID

 * @param {Array} context.deptId - 所属科室ID数组
 * @param {string} context.department - 部门

 * @param {string} context.relatedUserId - 关联用户ID

 * @param {string} context.title - 职称

 * @param {string} context.expertise - 擅长领域

 * @param {number} context.recommend - 推荐状态（1/0）
 * @param {Object} context.sortOrder - 排序（按科室存储的对象，如 { deptId: 1 }）
 * @returns {Object} 返回结果
 */
exports.addHlwHospitalMember = async (context) => {
  try {
    const {
      corpId,
      name,
      gender,
      phone,
      deptId,
      department,
      job,
      workId,
      relatedUserId,
      avatar,
      title,
      outpatientDept,
      outpatientTime,
      expertise,
      introduction,
      publicPhone,
      convenienceService,
      recommend = 0,
      sortOrder = 0,
    } = context;

    // 验证必填字段
    if (!corpId || !name || !workId) {
      return {
        success: false,
        message: "机构ID、姓名、院内工号不能为空",
      };
    }

    // 验证科室ID
    if (deptId && !Array.isArray(deptId)) {
      return {
        success: false,
        message: "科室ID必须是数组格式",
      };
    }

    // 检查院内工号是否已存在
    const existingMember = await db
      .collection("hlw-hospital")
      .findOne({ workId, corpId });
    
    if (existingMember) {
      // 如果存在相同工号的人员，检查是否在相同科室
      if (deptId && Array.isArray(deptId)) {
        const hasCommonDept = existingMember.deptId && 
          existingMember.deptId.some(dept => deptId.includes(dept));
        
        if (hasCommonDept) {
          return {
            success: false,
            message: "该院内工号在相同科室已存在",
          };
        }
        
        // 如果不在相同科室，将新科室添加到现有记录中
        const updatedDeptIds = [...new Set([...existingMember.deptId, ...deptId])];
        await db.collection("hlw-hospital").updateOne(
          { _id: existingMember._id },
          { 
            $set: { 
              deptId: updatedDeptIds,
              updateTime: new Date().getTime()
            }
          }
        );
        
        return {
          success: true,
          message: "已将人员添加到新科室",
          data: { _id: existingMember._id, deptId: updatedDeptIds },
        };
      }
    }

    // 处理排序编号：为每个科室生成独立的排序编号
    let finalSortOrder = {};
    
    if (deptId && Array.isArray(deptId) && deptId.length > 0) {
      // 为每个科室计算排序编号
      for (const dept of deptId) {
        if (sortOrder && typeof sortOrder === 'object' && sortOrder[dept]!=1) {
          // 如果传入了该科室的排序编号，直接使用
          finalSortOrder[dept] = sortOrder[dept];
        } else {
          // 否则自动计算该科室的下一个排序编号
          const pipeline = [
            { $match: { corpId, deptId: { $in: [dept] } } }, // 修复：正确查询数组字段
            {
              $addFields: {
                deptSortOrder: { $ifNull: [`$sortOrder.${dept}`, 0] }
              }
            },
            { $sort: { deptSortOrder: -1 } },
            { $limit: 1 },
            { $project: { deptSortOrder: 1 } }
          ];
          
          const maxSortResult = await db.collection("hlw-hospital").aggregate(pipeline).toArray();
          const maxSort = maxSortResult.length > 0 ? maxSortResult[0].deptSortOrder : 0;
          finalSortOrder[dept] = maxSort + 1;
        }
      }
    } else {
      // 默认排序
      finalSortOrder = {};
    }

    // 如果提供了relatedUserId，检查是否已存在
    if (relatedUserId) {
      const existingRelatedUserId = await db
        .collection("hlw-hospital")
        .findOne({ relatedUserId, corpId });
      
      if (existingRelatedUserId) {
        return {
          success: false,
          message: "关联用户ID已存在",
        };
      }
    }

    const memberData = {
      _id: common.generateRandomString(24),
      corpId,
      name,
      gender,
      phone,
      deptId, // 科室ID数组
      department, // 部门
      job,
      workId,
      relatedUserId, // 关联用户ID
      avatar,
      title,
      outpatientDept,
      outpatientTime,
      expertise,
      introduction,
      publicPhone,
      convenienceService,
      recommend,
      sortOrder: finalSortOrder,
      createTime: new Date().getTime(),
      updateTime: new Date().getTime(),
    };

    const result = await db.collection("hlw-hospital").insertOne(memberData);

    return {
      success: true,
      message: "新增成功",
      data: { _id: memberData._id, ...result },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "新增失败",
    };
  }
};

/**
 * 更新医院人员信息
 * @param {Object} context - 更新参数
 * @param {string} context.id - 人员ID
 * @param {string} context.corpId - 机构ID
 * @param {Object} context.params - 更新字段
 * @returns {Object} 返回结果
 */
exports.updateHlwHospitalMember = async (context) => {
  try {
    const { id, corpId, params } = context;
    
    if (!id || !corpId || !params) {
      return {
        success: false,
        message: "参数不能为空",
      };
    }

    // 如果更新院内工号，需要检查是否重复
    if (params.workId) {
      const existingMember = await db
        .collection("hlw-hospital")
        .findOne({ workId: params.workId, corpId, _id: { $ne: id } });
      
      if (existingMember) {
        return {
          success: false,
          message: "院内工号已存在",
        };
      }
    }

    // 处理排序字段更新
    if (params.sortOrder !== undefined) {
      if (typeof params.sortOrder === 'object') {
        // 直接使用对象格式的排序
        // 注意：这里可能会覆盖其他科室的排序，需要谨慎使用
      } else {
        // 只接受对象格式的排序
        return {
          success: false,
          message: "排序字段必须是对象格式，如 { deptId: sortValue }",
        };
      }
    }

    params.updateTime = new Date().getTime();
    
    const result = await db
      .collection("hlw-hospital")
      .updateOne({ _id: id, corpId }, { $set: params });

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: "人员不存在",
      };
    }

    return {
      success: true,
      message: "更新成功",
      data: result,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "更新失败",
    };
  }
};

/**
 * 从指定科室移除医院人员（不删除人员记录）
 * @param {Object} context - 移除参数
 * @param {string} context.id - 人员ID
 * @param {string} context.corpId - 机构ID
 * @param {string} context.deptId - 科室ID（必填）
 * @param {Object} context.params - 参数对象
 * @param {string} context.params.id - 人员ID
 * @param {string} context.params.deptId - 科室ID（必填）
 * @returns {Object} 返回结果
 */
exports.removeHlwHospitalMember = async (context) => {
  try {
    const { corpId, params = {} } = context;
    // 支持两种参数传递方式：直接传递或通过params对象传递
    const id = context.id || params.id;
    const deptId = context.deptId || params.deptId;
    
    if (!id || !corpId || !deptId) {
      return {
        success: false,
        message: "人员ID、机构ID和科室ID不能为空",
      };
    }

    // 获取人员信息
    const member = await db
      .collection("hlw-hospital")
      .findOne({ _id: id, corpId });

    if (!member) {
      return {
        success: false,
        message: "人员不存在",
      };
    }

    const currentDeptIds = member.deptId || [];
    
    // 检查人员是否属于指定科室
    if (!currentDeptIds.includes(deptId)) {
      return {
        success: false,
        message: "人员不属于指定科室",
      };
    }

    // 从科室列表中移除指定科室
    const updatedDeptIds = currentDeptIds.filter(dept => dept !== deptId);
    
    // 更新排序对象，移除对应科室的排序
    const updatedSortOrder = { ...member.sortOrder };
    if (updatedSortOrder && typeof updatedSortOrder === 'object') {
      delete updatedSortOrder[deptId];
    }

    // 更新人员信息，移除指定科室
    const result = await db
      .collection("hlw-hospital")
      .updateOne(
        { _id: id, corpId },
        { 
          $set: { 
            deptId: updatedDeptIds,
            sortOrder: updatedSortOrder,
            updateTime: new Date().getTime()
          }
        }
      );

    return {
      success: true,
      message: updatedDeptIds.length === 0 ? "已从最后一个科室移除，人员不再属于任何科室" : "已从指定科室移除",
      data: { 
        modifiedCount: result.modifiedCount,
        remainingDeptIds: updatedDeptIds,
        isEmpty: updatedDeptIds.length === 0
      },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "移除失败",
    };
  }
};

/**
 * 根据科室ID获取医院人员
 * @param {Object} context - 查询参数
 * @param {string} context.corpId - 机构ID
 * @param {Array|string} context.deptId - 科室ID（数组或单个ID）
 * @param {Object} context.params - 查询参数对象
 * @param {Array|string} context.params.deptId - 科室ID（数组或单个ID）
 * @returns {Object} 返回结果
 */
exports.getHlwHospitalMembersByDept = async (context) => {
  try {
    const { corpId, params = {} } = context;
    // 支持两种参数传递方式：直接传递或通过params对象传递
    const deptId = context.deptId || params.deptId;
    
    if (!corpId) {
      return {
        success: false,
        message: "机构ID不能为空",
      };
    }

    if (!deptId) {
      return {
        success: false,
        message: "科室ID不能为空",
      };
    }

    let query = { corpId };
    
    if (Array.isArray(deptId)) {
      // 如果是数组，查询 deptId 数组中包含任一元素的记录
      query.deptId = { $in: deptId };
    } else {
      // 如果是单个ID，查询 deptId 数组中包含该元素的记录
      query.deptId = { $in: [deptId] };
    }

    // 构建聚合管道，支持对象格式的sortOrder
    const pipeline = [
      { $match: query },
      {
        $addFields: {
          effectiveSortOrder: {
            $cond: {
              if: !Array.isArray(deptId),
              // 如果查询单个科室，使用该科室的排序
              then: { $ifNull: [`$sortOrder.${deptId}`, 999999] },
              // 如果查询多个科室，使用全局排序（取最小值）
              else: {
                $ifNull: [
                  {
                    $min: {
                      $map: {
                        input: { $objectToArray: "$sortOrder" },
                        as: "item",
                        in: "$$item.v"
                      }
                    }
                  },
                  999999
                ]
              }
            }
          }
        }
      },
      { $sort: { effectiveSortOrder: 1, createTime: -1 } },
      { $project: { effectiveSortOrder: 0 } } // 移除临时字段
    ];
    
    const data = await db.collection("hlw-hospital").aggregate(pipeline).toArray();

    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

/**
 * 根据岗位获取医院人员
 * @param {Object} context - 查询参数
 * @param {string} context.corpId - 机构ID
 * @param {Array} context.jobs - 岗位数组
 * @returns {Object} 返回结果
 */
exports.getHlwHospitalMembersByJob = async (context) => {
  try {
    const { corpId, jobs } = context;
    
    if (!corpId || !Array.isArray(jobs)) {
      return {
        success: false,
        message: "参数错误",
      };
    }

    if (jobs.length === 0) {
      return {
        success: true,
        message: "获取成功",
        data: [],
      };
    }

    const pipeline = [
      { $match: { corpId, job: { $in: jobs } } },
      {
        $addFields: {
          effectiveSortOrder: {
            $ifNull: [
              {
                $min: {
                  $map: {
                    input: { $objectToArray: "$sortOrder" },
                    as: "item",
                    in: "$$item.v"
                  }
                }
              },
              999999
            ]
          }
        }
      },
      { $sort: { effectiveSortOrder: 1, createTime: -1 } },
      { $project: { effectiveSortOrder: 0 } }
    ];
    
    const data = await db.collection("hlw-hospital").aggregate(pipeline).toArray();

    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

/**
 * 获取推荐人员
 * @param {Object} context - 查询参数
 * @param {string} context.corpId - 机构ID
 * @param {number} context.limit - 限制数量
 * @returns {Object} 返回结果
 */
exports.getRecommendedMembers = async (context) => {
  try {
    const { corpId, limit = 10 } = context;
    
    if (!corpId) {
      return {
        success: false,
        message: "机构ID不能为空",
      };
    }

    const pipeline = [
      { $match: { corpId, recommend: 1 } },
      {
        $addFields: {
          effectiveSortOrder: {
            $ifNull: [
              {
                $min: {
                  $map: {
                    input: { $objectToArray: "$sortOrder" },
                    as: "item",
                    in: "$$item.v"
                  }
                }
              },
              999999
            ]
          }
        }
      },
      { $sort: { effectiveSortOrder: 1, createTime: -1 } },
      { $limit: limit },
      { $project: { effectiveSortOrder: 0 } }
    ];
    
    const data = await db.collection("hlw-hospital").aggregate(pipeline).toArray();

    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

/**
 * 更新人员推荐状态
 * @param {Object} context - 更新参数
 * @param {string} context.id - 人员ID
 * @param {string} context.corpId - 机构ID
 * @param {number} context.recommend - 推荐状态（1/0）
 * @returns {Object} 返回结果
 */
exports.updateMemberRecommendStatus = async (context) => {
  try {
    const { id, corpId, recommend } = context;
    
    if (!id || !corpId || recommend === undefined) {
      return {
        success: false,
        message: "参数不能为空",
      };
    }

    const result = await db
      .collection("hlw-hospital")
      .updateOne(
        { _id: id, corpId },
        { $set: { recommend, updateTime: new Date().getTime() } }
      );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: "人员不存在",
      };
    }

    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "更新失败",
    };
  }
};

/**
 * 批量更新人员信息
 * @param {Object} context - 更新参数
 * @param {string} context.corpId - 机构ID
 * @param {Array} context.updates - 更新数组
 * @returns {Object} 返回结果
 */
exports.batchUpdateMembers = async (context) => {
  try {
    const { corpId, updates, members } = context;
    
    // 兼容旧的参数名称 members 和新的参数名称 updates
    const updateList = updates || members;
    
    if (!corpId || !Array.isArray(updateList) || updateList.length === 0) {
      return {
        success: false,
        message: "参数错误",
      };
    }

    // 构建批量更新操作
    const bulkOps = updateList.map(update => {
      const filter = { _id: update.id, corpId };
      const updateParams = { ...update.params };

      // 处理排序字段更新
      if (typeof updateParams.sortOrder === 'object') {
        // 直接使用对象格式的排序
      } else if (updateParams.sortOrder !== undefined) {
        // 只接受对象格式的排序
        throw new Error("排序字段必须是对象格式，如 { deptId: sortValue }");
      }

      return {
        updateOne: {
          filter,
          update: { $set: { ...updateParams, updateTime: new Date().getTime() } },
        },
      };
    });

    const result = await db.collection("hlw-hospital").bulkWrite(bulkOps);

    return {
      success: true,
      message: "批量更新成功",
      data: result,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "批量更新失败",
    };
  }
};

/**
 * 搜索医院人员
 * @param {Object} context - 搜索参数
 * @param {string} context.corpId - 机构ID
 * @param {string} context.keyword - 关键词
 * @param {number} context.page - 页码
 * @param {number} context.pageSize - 每页数量
 * @returns {Object} 返回结果
 */
exports.searchMembers = async (context) => {
  try {
    const { corpId, keyword, page = 1, pageSize = 10 } = context;
    
    if (!corpId || !keyword) {
      return {
        success: false,
        message: "参数不能为空",
      };
    }

    const query = {
      corpId,
      $or: [
        { name: { $regex: keyword, $options: "i" } },
        { job: { $regex: keyword, $options: "i" } },
        { title: { $regex: keyword, $options: "i" } },
        { outpatientDept: { $regex: keyword, $options: "i" } },
        { expertise: { $regex: keyword, $options: "i" } },
        { workId: { $regex: keyword, $options: "i" } },
        { relatedUserId: { $regex: keyword, $options: "i" } },
      ],
    };

    const total = await db.collection("hlw-hospital").countDocuments(query);
    const pages = Math.ceil(total / pageSize);
    
    const pipeline = [
      { $match: query },
      {
        $addFields: {
          effectiveSortOrder: {
            $ifNull: [
              {
                $min: {
                  $map: {
                    input: { $objectToArray: "$sortOrder" },
                    as: "item",
                    in: "$$item.v"
                  }
                }
              },
              999999
            ]
          }
        }
      },
      { $sort: { effectiveSortOrder: 1, createTime: -1 } },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize },
      { $project: { effectiveSortOrder: 0 } }
    ];
    
    const data = await db.collection("hlw-hospital").aggregate(pipeline).toArray();

    return {
      success: true,
      message: "搜索成功",
      data,
      total,
      pages,
      page,
      pageSize,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "搜索失败",
    };
  }
};

/**
 * 根据门诊科室获取医院人员
 * @param {Object} context - 查询参数
 * @param {string} context.corpId - 机构ID
 * @param {string} context.outpatientDept - 门诊科室
 * @returns {Object} 返回结果
 */
exports.getMembersByOutpatientDept = async (context) => {
  try {
    const { corpId, outpatientDept } = context;
    
    if (!corpId || !outpatientDept) {
      return {
        success: false,
        message: "参数不能为空",
      };
    }

    const pipeline = [
      { $match: { corpId, outpatientDept } },
      {
        $addFields: {
          effectiveSortOrder: {
            $ifNull: [
              {
                $min: {
                  $map: {
                    input: { $objectToArray: "$sortOrder" },
                    as: "item",
                    in: "$$item.v"
                  }
                }
              },
              999999
            ]
          }
        }
      },
      { $sort: { effectiveSortOrder: 1, createTime: -1 } },
      { $project: { effectiveSortOrder: 0 } }
    ];
    
    const data = await db.collection("hlw-hospital").aggregate(pipeline).toArray();

    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

/**
 * 检查医院人员是否存在
 * @param {Object} context - 查询参数
 * @param {string} context.corpId - 机构ID
 * @param {string} context.workId - 院内工号
 * @returns {Object} 返回结果
 */
exports.hlwHospitalMemberExist = async (context) => {
  try {
    const { corpId, workId } = context;
    
    if (!corpId || !workId) {
      return {
        success: false,
        message: "参数不能为空",
      };
    }

    const count = await db
      .collection("hlw-hospital")
      .countDocuments({ corpId, workId });

    return {
      success: true,
      exist: count > 0,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "检查失败",
    };
  }
};

/**
 * 获取医院人员统计信息
 * @param {Object} context - 查询参数
 * @param {string} context.corpId - 机构ID
 * @returns {Object} 返回结果
 */
exports.getHlwHospitalMemberStats = async (context) => {
  try {
    const { corpId } = context;
    
    if (!corpId) {
      return {
        success: false,
        message: "机构ID不能为空",
      };
    }

    // 总人数
    const totalCount = await db
      .collection("hlw-hospital")
      .countDocuments({ corpId });

    // 推荐人数
    const recommendCount = await db
      .collection("hlw-hospital")
      .countDocuments({ corpId, recommend: 1 });

    // 有关联账号的人数
    const linkedAccountCount = await db
      .collection("hlw-hospital")
      .countDocuments({ corpId, relatedUserId: { $exists: true, $ne: null, $ne: "" } });

    // 按科室统计
    const deptStats = await db
      .collection("hlw-hospital")
      .aggregate([
        { $match: { corpId } },
        { $unwind: "$deptId" }, // 展开 deptId 数组
        { $group: { _id: "$deptId", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
      ])
      .toArray();

    // 按岗位统计
    const jobStats = await db
      .collection("hlw-hospital")
      .aggregate([
        { $match: { corpId } },
        { $group: { _id: "$job", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
      ])
      .toArray();

    // 按性别统计
    const genderStats = await db
      .collection("hlw-hospital")
      .aggregate([
        { $match: { corpId } },
        { $group: { _id: "$gender", count: { $sum: 1 } } },
      ])
      .toArray();

    return {
      success: true,
      message: "获取成功",
      data: {
        totalCount,
        recommendCount,
        linkedAccountCount,
        deptStats,
        jobStats,
        genderStats,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

/**
 * 批量更新人员排序
 * @param {Object} context - 更新参数
 * @param {string} context.corpId - 机构ID
 * @param {string} context.deptId - 科室ID（必填，排序仅对当前科室有效）
 * @param {Array} context.members - 人员排序数组
 * @param {string} context.members[].id - 人员ID
 * @param {number} context.members[].sortOrder - 排序编号
 * @returns {Object} 返回结果
 */
exports.batchUpdateMembersSortOrder = async (context) => {
  try {
    const { corpId, deptId, members } = context;
    
    if (!corpId || !deptId || !members || !Array.isArray(members)) {
      return {
        success: false,
        message: "机构ID、科室ID和人员数组不能为空",
      };
    }

    if (members.length === 0) {
      return {
        success: false,
        message: "人员数组不能为空",
      };
    }

    // 获取要更新的人员ID列表
    const memberIds = members.map(m => m.id);
    
    // 获取当前人员的排序对象
    const existingMembers = await db
      .collection("hlw-hospital")
      .find({ 
        corpId, 
        _id: { $in: memberIds },
        deptId: { $in: [deptId] } // 确保人员属于指定科室
      })
      .project({ _id: 1, sortOrder: 1 })
      .toArray();

    if (existingMembers.length !== memberIds.length) {
      return {
        success: false,
        message: "部分人员不存在或不属于指定科室",
      };
    }

    // 构建批量更新操作
    const bulkOperations = [];
    const updateTime = new Date().getTime();

    for (const member of members) {
      const { id, sortOrder } = member;
      
      // 找到对应的现有人员
      const existingMember = existingMembers.find(m => m._id === id);
      if (!existingMember) continue;

      // 使用辅助函数更新指定科室的排序
      const newSortOrder = setDeptSortOrder(existingMember.sortOrder, deptId, sortOrder);

      bulkOperations.push({
        updateOne: {
          filter: { _id: id, corpId },
          update: { 
            $set: { 
              sortOrder: newSortOrder,
              updateTime
            }
          }
        }
      });
    }

    if (bulkOperations.length === 0) {
      return {
        success: true,
        message: "没有需要更新的人员",
        data: { modifiedCount: 0, matchedCount: 0 },
      };
    }

    // 执行批量更新
    const result = await db
      .collection("hlw-hospital")
      .bulkWrite(bulkOperations);

    return {
      success: true,
      message: "批量更新排序成功",
      data: {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "批量更新排序失败",
    };
  }
};

/**
 * 删除医院人员记录（完全删除）
 * @param {Object} context - 删除参数
 * @param {string} context.id - 人员ID
 * @param {string} context.corpId - 机构ID
 * @param {Object} context.params - 参数对象
 * @param {string} context.params.id - 人员ID
 * @returns {Object} 返回结果
 */
exports.deleteHlwHospitalMember = async (context) => {
  try {
    const { corpId, params = {} } = context;
    // 支持两种参数传递方式：直接传递或通过params对象传递
    const id = context.id || params.id;
    
    if (!id || !corpId) {
      return {
        success: false,
        message: "人员ID和机构ID不能为空",
      };
    }

    // 先检查人员是否存在
    const member = await db
      .collection("hlw-hospital")
      .findOne({ _id: id, corpId });

    if (!member) {
      return {
        success: false,
        message: "人员不存在",
      };
    }

    // 删除人员记录
    const result = await db
      .collection("hlw-hospital")
      .deleteOne({ _id: id, corpId });

    return {
      success: true,
      message: "删除成功",
      data: { 
        deletedCount: result.deletedCount,
        deletedMember: {
          id: member._id,
          name: member.name,
          workId: member.workId,
          deptId: member.deptId
        }
      },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "删除失败",
    };
  }
};

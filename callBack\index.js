const crypto = require("@wecom/crypto");
const xml2js = require("xml2js");
const weCom = require("./weCom/index");
const token = process.env.CONFIG_WECHAT_TOKEN;
const aeskey = process.env.CONFIG_WECHAT_AESKEY;

function decodeParams(params) {
  return Object.keys(params).reduce((acc, key) => {
    acc[key] = decodeURIComponent(params[key]);
    return acc;
  }, {});
}

async function parserXml(decodedString) {
  return new Promise((resolve, reject) => {
    const parser = new xml2js.Parser();
    parser.parseString(decodedString, (err, result) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(result);
    });
  });
}

async function processPostRequest(body) {
  const xmlRes = await parserXml(body);
  const encrypt = xmlRes.xml.Encrypt[0];
  const messageXML = crypto.decrypt(aeskey, encrypt);
  console.log(messageXML);
  const callbackDataBody = await parserXml(messageXML.message);
  for (const key in callbackDataBody.xml) {
    if (Array.isArray(callbackDataBody.xml[key])) {
      callbackDataBody.xml[key] = callbackDataBody.xml[key][0];
    }
  }
  await weCom.getWeComData(callbackDataBody.xml);
  return "success";
}

function data(e) {
  const { httpMethod, queryStringParameters } = e;
  const { timestamp, nonce, echostr, msg_signature } = decodeParams(
    queryStringParameters
  );

  const signature = crypto.getSignature(token, timestamp, nonce, echostr);
  console.log("signature", signature);
  console.log("token", token);
  console.log("token", aeskey);
  if (httpMethod === "GET" && signature === msg_signature) {
    const { message } = crypto.decrypt(aeskey, echostr);
    return message;
  } else if (httpMethod === "POST") {
    return processPostRequest(e.body);
  }
}

exports.httpGet = async function (item) {
  const { timestamp, nonce, echostr, msg_signature } = decodeParams(item);

  const signature = crypto.getSignature(token, timestamp, nonce, echostr);
  if (signature === msg_signature) {
    const { message } = crypto.decrypt(aeskey, echostr);
    return message;
  }
};

exports.httpPost = processPostRequest;

exports.main = async function (e) {
  return data(e);
};

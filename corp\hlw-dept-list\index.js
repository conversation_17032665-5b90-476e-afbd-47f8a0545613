let db = null;
const common = require("../../common");

exports.main = async (context, mongodb) => {
  db = mongodb;
  switch (context.type) {
    case "addHlwDept":
      return await addHlwDept(context);
    case "updateHlwDept":
      return await updateHlwDept(context);
    case "deleteHlwDept":
      return await deleteHlwDept(context);
    case "getHlwDeptList":
      return await getHlwDeptList(context);
    case "getHlwDeptById":
      return await getHlwDeptById(context);
    case "getHlwDeptTree":
      return await getHlwDeptTree(context);
    case "checkHlwDeptIdExists":
      return await checkHlwDeptIdExists(context);
    case "sortHlwDeptList":
      return await sortHlwDeptList(context);
    case "getHlwDeptsByParent":
      return await getHlwDeptsByParent(context);
  }
};

// 新增互联网科室
async function addHlwDept(context) {
  let { corpId, params } = context;

  // 添加调试信息
  console.log(`[DEBUG] addHlwDept 接收到的参数:`, JSON.stringify(context));
  console.log(`[DEBUG] corpId:`, corpId);
  console.log(`[DEBUG] params:`, JSON.stringify(params));

  // 容错处理：确保 params 存在
  if (!params) {
    console.log(`[DEBUG] params 为空，尝试从 context 直接获取参数`);
    params = context; // 如果 params 不存在，尝试从 context 直接获取
  }

  const { hlwDeptName, hlwDeptId, description, parentId, areaId } = params;

  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (typeof hlwDeptName !== "string" || hlwDeptName.trim() === "") {
    console.log(`[DEBUG] hlwDeptName 验证失败: hlwDeptName=${hlwDeptName}, type=${typeof hlwDeptName}, params=`, JSON.stringify(params));
    return { success: false, message: "科室名称不能为空" };
  }
  if (hlwDeptName.trim().length > 50)
    return { success: false, message: "科室名称不能超过50个字符" };
  if (typeof hlwDeptId !== "string" || hlwDeptId.trim() === "")
    return { success: false, message: "科室ID不能为空" };
  if (hlwDeptId.trim().length > 20)
    return { success: false, message: "科室ID不能超过20个字符" };
  
  try {
    // 检查科室ID是否已存在
    const existingDept = await db
      .collection("hlw-dept-list")
      .findOne({ hlwDeptId: hlwDeptId.trim(), corpId });
    
    if (existingDept) {
      return { success: false, message: "科室ID已存在，请使用其他ID" };
    }
    
    let level = 1;
    let parentData = null;
    
    // 处理上级节点逻辑
    if (parentId) {
      console.log(`[DEBUG] 开始查找父级节点: parentId=${parentId}`);

      // 检查是否为院区ID（支持两种ID格式）
      const areaExists = await db
        .collection("hlw-hospital-area")
        .findOne({
          $or: [
            { _id: parentId, corpId },      // 兼容旧格式：使用_id
            { areaId: parentId, corpId }    // 新格式：使用areaId字段
          ]
        });

      if (areaExists) {
        // 父节点是院区，当前科室为一级科室
        console.log(`[DEBUG] 找到父级院区:`, JSON.stringify(areaExists));
        level = 1;
        parentData = { type: "area", data: areaExists };
      } else {
        console.log(`[DEBUG] 未找到院区，尝试查找科室`);
        // 父节点是科室
        // 父节点是科室
        // 查找上级科室（支持两种ID格式）
        const parentDept = await db
          .collection("hlw-dept-list")
          .findOne({
            $or: [
              { _id: parentId, corpId },        // 兼容旧格式：使用_id
              { hlwDeptId: parentId, corpId }   // 新格式：使用hlwDeptId字段
            ]
          });

        if (!parentDept) {
          console.log(`[DEBUG] 上级科室不存在: parentId=${parentId}, corpId=${corpId}`);
          return { success: false, message: "上级科室不存在" };
        }

        console.log(`[DEBUG] 找到上级科室:`, JSON.stringify(parentDept));
        
        if (parentDept.level >= 3) {
          return { success: false, message: "科室层级不能超过3级" };
        }
        
        level = parentDept.level + 1;
        parentData = { type: "dept", data: parentDept };
      }
    } else if (areaId) {
      // 直接指定院区ID作为父级（支持两种ID格式）
      const areaExists = await db
        .collection("hlw-hospital-area")
        .findOne({
          $or: [
            { _id: areaId, corpId },      // 兼容旧格式：使用_id
            { areaId: areaId, corpId }    // 新格式：使用areaId字段
          ]
        });

      if (!areaExists) {
        console.log(`[DEBUG] 院区不存在: areaId=${areaId}, corpId=${corpId}`);
        return { success: false, message: "指定的院区不存在" };
      }

      console.log(`[DEBUG] 找到院区:`, JSON.stringify(areaExists));
      level = 1;
      parentData = { type: "area", data: areaExists };
    }
    
    const _id = common.generateRandomString(24);
    // 确定正确的parentId和areaId
    let finalParentId = null;
    let finalAreaId = null;

    if (parentData) {
      if (parentData.type === "dept") {
        // 父级是科室，使用其hlwDeptId作为parentId
        finalParentId = parentData.data.hlwDeptId;
        finalAreaId = parentData.data.areaId; // 继承父科室的院区
      } else if (parentData.type === "area") {
        // 父级是院区，parentId为null，使用院区的areaId
        finalParentId = null;
        finalAreaId = parentData.data.areaId || parentData.data._id; // 兼容性：优先使用areaId，fallback到_id
      }
    } else if (areaId) {
      // 直接指定院区，需要获取院区的areaId
      const area = await db.collection("hlw-hospital-area").findOne({
        _id: areaId,
        corpId
      }, { projection: { areaId: 1 } });

      if (area) {
        finalAreaId = area.areaId || areaId; // 兼容性：优先使用areaId，fallback到_id
      }
    }

    const hlwDept = {
      _id,
      corpId,
      hlwDeptName: hlwDeptName.trim(),
      hlwDeptId: hlwDeptId.trim(),
      description: description ? description.trim() : "",
      parentId: finalParentId, // 使用科室的hlwDeptId
      areaId: finalAreaId, // 使用院区的areaId
      level,
      sort: 0,
      status: 1, // 1: 正常, 0: 禁用
      createTime: Math.floor(Date.now() / 1000),
      updateTime: Math.floor(Date.now() / 1000),
    };
    
    const result = await db.collection("hlw-dept-list").insertOne(hlwDept);
    
    if (result.insertedId) {
      return {
        success: true,
        message: "科室添加成功",
        data: hlwDept
      };
    } else {
      return { success: false, message: "科室添加失败" };
    }
  } catch (error) {
    console.error("添加互联网科室时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 更新互联网科室
async function updateHlwDept(context) {
  let { corpId, params } = context;
  const { _id, hlwDeptName, hlwDeptId, description, status, sort } = params;
  
  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "科室_id不能为空" };
  
  try {
    // 验证科室是否存在
    const existingDept = await db
      .collection("hlw-dept-list")
      .findOne({ _id: _id, corpId });
    
    if (!existingDept) {
      return { success: false, message: "科室不存在" };
    }
    
    const updateData = {
      updateTime: Math.floor(Date.now() / 1000)
    };
    
    // 更新科室名称
    if (hlwDeptName !== undefined) {
      if (typeof hlwDeptName !== "string" || hlwDeptName.trim() === "")
        return { success: false, message: "科室名称不能为空" };
      if (hlwDeptName.trim().length > 50)
        return { success: false, message: "科室名称不能超过50个字符" };

      updateData.hlwDeptName = hlwDeptName.trim();
    }
    
    // 更新科室ID
    if (hlwDeptId !== undefined) {
      if (typeof hlwDeptId !== "string" || hlwDeptId.trim() === "")
        return { success: false, message: "科室ID不能为空" };
      if (hlwDeptId.trim().length > 20)
        return { success: false, message: "科室ID不能超过20个字符" };

      // 检查新ID是否与其他科室重复
      if (hlwDeptId.trim() !== existingDept.hlwDeptId) {
        const idExists = await db
          .collection("hlw-dept-list")
          .findOne({
            hlwDeptId: hlwDeptId.trim(),
            corpId,
            _id: { $ne: _id }
          });

        if (idExists) {
          return { success: false, message: "科室ID已存在，请使用其他ID" };
        }
      }

      updateData.hlwDeptId = hlwDeptId.trim();
    }
    
    // 更新科室介绍
    if (description !== undefined) {
      updateData.description = description ? description.trim() : "";
    }
    
    // 更新状态
    if (status !== undefined) {
      if (![0, 1].includes(status)) {
        return { success: false, message: "状态值无效" };
      }
      updateData.status = status;
    }
    
    // 更新排序
    if (sort !== undefined) {
      if (typeof sort !== "number" || sort < 0) {
        return { success: false, message: "排序值无效" };
      }
      updateData.sort = sort;
    }
    
    const result = await db
      .collection("hlw-dept-list")
      .updateOne(
        { _id: _id, corpId },
        { $set: updateData }
      );
    
    if (result.modifiedCount > 0) {
      return { success: true, message: "科室更新成功" };
    } else {
      return { success: false, message: "没有数据被更新" };
    }
  } catch (error) {
    console.error("更新互联网科室时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 递归获取所有下级科室ID
async function getAllChildDeptIds(parentId, corpId) {
  // 支持两种格式的parentId查询
  const childDepts = await db
    .collection("hlw-dept-list")
    .find({
      $or: [
        { parentId: parentId }, // 新格式：parentId存储hlwDeptId
        { parentId: { $exists: true }, hlwDeptId: parentId } // 兼容性：查找hlwDeptId匹配的科室
      ],
      corpId
    })
    .toArray();

  let allChildIds = childDepts.map(dept => dept._id);

  // 递归获取每个子科室的下级科室，使用hlwDeptId作为parentId
  for (const childDept of childDepts) {
    const grandChildIds = await getAllChildDeptIds(childDept.hlwDeptId, corpId);
    allChildIds = allChildIds.concat(grandChildIds);
  }

  return allChildIds;
}

// 删除互联网科室（支持级联删除）
async function deleteHlwDept(context) {
  let { corpId, params } = context;
  const { _id } = params;

  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "科室_id不能为空" };

  try {
    // 验证科室是否存在
    const existingDept = await db
      .collection("hlw-dept-list")
      .findOne({ _id: _id, corpId });

    if (!existingDept) {
      return { success: false, message: "科室不存在" };
    }

    // 获取所有需要删除的科室ID（包括当前科室和所有下级科室）
    const childDeptIds = await getAllChildDeptIds(_id, corpId);
    const allDeptIdsToDelete = [_id, ...childDeptIds];

    console.log(`准备删除科室及其下级科室，共 ${allDeptIdsToDelete.length} 个科室:`, allDeptIdsToDelete);

    // 批量删除所有相关科室
    const deleteResult = await db
      .collection("hlw-dept-list")
      .deleteMany({
        _id: { $in: allDeptIdsToDelete },
        corpId
      });

    // 同时清理相关人员的科室关联（如果有的话）
    const memberUpdateResult = await db
      .collection("corp-member")
      .updateMany(
        {
          corpId,
          hlwDeptIds: { $in: allDeptIdsToDelete }
        },
        {
          $pullAll: { hlwDeptIds: allDeptIdsToDelete },
          $unset: allDeptIdsToDelete.reduce((unsetObj, deptId) => {
            unsetObj[`hlwSortOrder.${deptId}`] = "";
            return unsetObj;
          }, {}),
          $set: { updateTime: new Date().getTime() }
        }
      );

    console.log(`删除了 ${deleteResult.deletedCount} 个科室，更新了 ${memberUpdateResult.modifiedCount} 个成员记录`);

    if (deleteResult.deletedCount > 0) {
      const message = childDeptIds.length > 0
        ? `科室删除成功，同时删除了 ${childDeptIds.length} 个下级科室`
        : "科室删除成功";

      return {
        success: true,
        message: message,
        data: {
          deletedDeptCount: deleteResult.deletedCount,
          updatedMemberCount: memberUpdateResult.modifiedCount,
          deletedDeptIds: allDeptIdsToDelete
        }
      };
    } else {
      return { success: false, message: "科室删除失败" };
    }
  } catch (error) {
    console.error("删除互联网科室时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 获取互联网科室列表
async function getHlwDeptList(context) {
  let { corpId, params = {} } = context;
  const { 
    page = 1, 
    pageSize = 20, 
    status, 
    keyword,
    level,
    parentId,
    areaId,
    sortBy = "sort",
    sortOrder = 1 
  } = params;
  
  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  
  try {
    const query = { corpId };
    
    // 状态筛选
    if (status !== undefined && status !== null && status !== "") {
      query.status = status;
    }
    
    // 层级筛选
    if (level !== undefined && level !== null && level !== "") {
      query.level = level;
    }
    
    // 上级科室筛选 - 支持兼容性
    if (parentId !== undefined && parentId !== null && parentId !== "") {
      query.parentId = parentId; // 直接使用传入的parentId，支持hlwDeptId格式
    }

    // 院区筛选 - 支持兼容性
    if (areaId !== undefined && areaId !== null && areaId !== "") {
      query.areaId = areaId; // 直接使用传入的areaId，支持院区的areaId格式
    }
    
    // 关键词搜索（科室名称或科室ID）
    if (keyword && keyword.trim()) {
      const keywordRegex = new RegExp(keyword.trim(), "i");
      query.$or = [
        { hlwDeptName: keywordRegex },
        { hlwDeptId: keywordRegex },
        { description: keywordRegex }
      ];
    }
    
    // 构建排序条件
    const sort = {};
    sort[sortBy] = sortOrder;
    
    // 分页计算
    const skip = (page - 1) * pageSize;
    
    // 获取总数
    const total = await db.collection("hlw-dept-list").countDocuments(query);
    
    // 获取数据并关联院区信息
    const depts = await db
      .collection("hlw-dept-list")
      .aggregate([
        { $match: query },
        {
          $lookup: {
            from: "hlw-hospital-area",
            let: { areaId: "$areaId" },
            pipeline: [
              { $match: {
                $expr: {
                  $or: [
                    { $eq: ["$areaId", "$$areaId"] }, // 新格式：使用areaId字段
                    { $eq: ["$_id", "$$areaId"] }     // 兼容旧格式：使用_id字段
                  ]
                }
              }}
            ],
            as: "areaInfo"
          }
        },
        {
          $lookup: {
            from: "hlw-dept-list",
            let: { parentId: "$parentId" },
            pipeline: [
              { $match: {
                $expr: {
                  $or: [
                    { $eq: ["$hlwDeptId", "$$parentId"] }, // 新格式：使用hlwDeptId字段
                    { $eq: ["$_id", "$$parentId"] }        // 兼容旧格式：使用_id字段
                  ]
                }
              }}
            ],
            as: "parentInfo"
          }
        },
        { $sort: sort },
        { $skip: skip },
        { $limit: pageSize }
      ])
      .toArray();
    
    return {
      success: true,
      data: {
        list: depts,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  } catch (error) {
    console.error("获取互联网科室列表时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 根据ID获取单个互联网科室信息
async function getHlwDeptById(context) {
  let { corpId, params } = context;
  const { _id } = params;
  
  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "科室_id不能为空" };
  
  try {
    const dept = await db
      .collection("hlw-dept-list")
      .aggregate([
        { $match: { _id: _id, corpId } },
        {
          $lookup: {
            from: "hlw-hospital-area",
            let: { areaId: "$areaId" },
            pipeline: [
              { $match: {
                $expr: {
                  $or: [
                    { $eq: ["$areaId", "$$areaId"] }, // 新格式：使用areaId字段
                    { $eq: ["$_id", "$$areaId"] }     // 兼容旧格式：使用_id字段
                  ]
                }
              }}
            ],
            as: "areaInfo"
          }
        },
        {
          $lookup: {
            from: "hlw-dept-list",
            let: { parentId: "$parentId" },
            pipeline: [
              { $match: {
                $expr: {
                  $or: [
                    { $eq: ["$hlwDeptId", "$$parentId"] }, // 新格式：使用hlwDeptId字段
                    { $eq: ["$_id", "$$parentId"] }        // 兼容旧格式：使用_id字段
                  ]
                }
              }}
            ],
            as: "parentInfo"
          }
        }
      ])
      .toArray();
    
    if (dept.length > 0) {
      return { success: true, data: dept[0] };
    } else {
      return { success: false, message: "科室不存在" };
    }
  } catch (error) {
    console.error("获取互联网科室信息时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 获取互联网科室树形结构
async function getHlwDeptTree(context) {
  let { corpId, params = {} } = context;
  const { areaId } = params;
  
  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  
  try {
    const query = { corpId, status: 1 };
    
    // 如果指定了院区ID，只获取该院区下的科室
    if (areaId) {
      query.areaId = areaId;
    }
    
    const depts = await db
      .collection("hlw-dept-list")
      .find(query)
      .sort({ level: 1, sort: 1, createTime: 1 })
      .toArray();
    
    console.log(`找到 ${depts.length} 个科室，query条件:`, JSON.stringify(query));
    if (depts.length > 0) {
      console.log("科室示例:", depts.slice(0, 2));
    }
    
    // 构建树形结构
    const tree = buildDeptTree(depts);
    
    console.log(`构建树形结构完成，根节点数量: ${tree.length}`);
    
    return { success: true, data: tree };
  } catch (error) {
    console.error("获取互联网科室树形结构时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 检查科室ID是否存在
async function checkHlwDeptIdExists(context) {
  let { corpId, params } = context;

  // 添加调试信息
  console.log(`[DEBUG] checkHlwDeptIdExists 接收到的参数:`, JSON.stringify(context));

  // 容错处理：确保 params 存在
  if (!params) {
    console.log(`[DEBUG] params 为空，尝试从 context 直接获取参数`);
    params = context; // 如果 params 不存在，尝试从 context 直接获取
  }

  const { hlwDeptId, excludeId } = params;

  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!hlwDeptId || typeof hlwDeptId !== "string" || hlwDeptId.trim() === "") {
    console.log(`[DEBUG] hlwDeptId 验证失败: hlwDeptId=${hlwDeptId}, type=${typeof hlwDeptId}, params=`, JSON.stringify(params));
    return { success: false, message: "科室ID不能为空" };
  }

  try {
    const query = {
      hlwDeptId: hlwDeptId.trim(),
      corpId
    };
    
    // 如果提供了excludeId，则排除该记录（用于编辑时检查）
    if (excludeId) {
      query._id = { $ne: excludeId };
    }
    
    const existingDept = await db
      .collection("hlw-dept-list")
      .findOne(query);
    
    return { 
      success: true, 
      data: { 
        exists: !!existingDept,
        message: existingDept ? "科室ID已存在" : "科室ID可用"
      }
    };
  } catch (error) {
    console.error("检查科室ID时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 排序科室列表
async function sortHlwDeptList(context) {
  const { corpId, params } = context;
  const { sortData } = params;
  
  if (!corpId) return { success: false, message: "机构id不能为空" };
  
  const arr = Array.isArray(sortData)
    ? sortData.filter(
        (i) =>
          i._id && typeof i.sort === "number" && Number.isInteger(i.sort) && i.sort >= 0
      )
    : [];
  
  if (arr.length === 0) return { success: false, message: "参数错误" };
  
  try {
    const bulkOps = arr.map((item) => ({
      updateOne: {
        filter: { corpId, _id: item._id },
        update: { $set: { sort: item.sort, updateTime: Math.floor(Date.now() / 1000) } },
      },
    }));
    
    const res = await db.collection("hlw-dept-list").bulkWrite(bulkOps);
    return { success: true, message: "排序更新成功", data: res };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 根据父级ID获取科室列表
async function getHlwDeptsByParent(context) {
  let { corpId, params } = context;
  const { parentId } = params;
  
  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  
  try {
    const query = { corpId, status: 1 };
    
    if (parentId) {
      query.parentId = parentId;
    } else {
      query.parentId = null; // 获取顶级科室
    }
    
    const depts = await db
      .collection("hlw-dept-list")
      .find(query)
      .sort({ sort: 1, createTime: 1 })
      .toArray();
    
    return { success: true, data: depts };
  } catch (error) {
    console.error("根据父级ID获取科室列表时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 辅助函数：构建科室树形结构
function buildDeptTree(depts, parentId = null) {
  // 如果是根级别，查找所有一级科室（level=1或parentId为null的科室）
  let children;
  if (parentId === null) {
    children = depts.filter(dept => dept.level === 1 || dept.parentId === null ||
      (dept.parentId && !depts.find(d => d.hlwDeptId === dept.parentId || d._id === dept.parentId)));
  } else {
    // 支持两种格式：hlwDeptId 和 _id
    children = depts.filter(dept => dept.parentId === parentId);
  }
  
  return children.map(dept => ({
    ...dept,
    children: buildDeptTree(depts, dept.hlwDeptId) // 使用hlwDeptId作为parentId
  }));
}

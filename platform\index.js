const login = require("./login");
const corp = require("./corp");
const package = require("./corp-package");
const account = require("./account");

let db = null;
exports.main = async (event, DB) => {
  db = DB;
  switch (event.type) {
    case "login":
      return await login.login(event, db);
    case "getMenu":
      return await login.getMenu(event, db);
    case "getCorpList":
      return await corp.getCorpList(event, db);
    case "addCorp":
      return await corp.addCorp(event, db);
    case "updateCorp":
      return await corp.updateCorp(event, db);
    case "removeCorpMember":
      return await corp.removeCorpMember(event, db);
    case "getCorpInfoById":
      return await corp.getCorpInfoById(event, db);
    case "getSuperAdministratorForCorpId":
      return await corp.getSuperAdministratorForCorpId(event, db);
    case "addSuperAdministrator":
      return await corp.addSuperAdministrator(event, db);
    case "getCorpMemberList":
      return await corp.getCorpMemberList(event, db);
    case "addPackage":
      return await package.addPackage(event, db);
    case "getPackageList":
      return await package.getPackageList(event, db);
    case "updatePackage":
      return await package.updatePackage(event, db);
    case "removePackage":
      return await package.removePackage(event, db);
    case "activateAccount":
      return await account.activateAccount(event, db);
  }
};

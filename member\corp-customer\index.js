const dayjs = require("dayjs");
const logger = require("../../utils/logger");
const api = require("../../api");

let db = null;
exports.main = async (content, mongodb) => {
  db = mongodb;
  switch (content.type) {
    case "batchUpdateCustomer":
      return await this.batchUpdateCustomer(content);
    case "searchCorpCustomer":
      return await this.searchCorpCustomer(content);
  }
};
exports.searchCorpCustomer = async function (context) {
  if (!context.corpId) return { success: false, message: "机构id不能为空" };
  
  try {
    const startTime = Date.now();
    console.log(`[searchCorpCustomer] 开始查询: corpId=${context.corpId}`);
    
    // 验证数据库连接
    if (!db) {
      throw new Error("数据库连接未初始化");
    }
    
    const query = { corpId: context.corpId };
    const page = parseInt(context.page) > 0 ? parseInt(context.page) : 1;
    const pageSize = Math.min(
      parseInt(context.pageSize) > 0 ? parseInt(context.pageSize) : 10,
      500 // 限制最大页面大小
    );
    const andList = []; // 并且的查询条件列表
    
    // 优化：提前构建索引友好的查询条件
    const indexOptimizedQuery = { corpId: context.corpId };
    
    // 客户姓名筛选 - 使用索引优化
    if (typeof context.name === "string" && context.name.trim()) {
      const nameRegex = new RegExp(".*" + context.name.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), "i");
      query.name = { $regex: nameRegex };
    }
    
    // 收集所有需要使用 $or 的条件
    const orConditions = [];
    
    // 手机号筛选 - 优化正则表达式
    if (typeof context.mobile === "string" && context.mobile.trim()) {
      const mobilePattern = context.mobile.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      orConditions.push({
        $or: [
          { mobile: { $regex: new RegExp(".*" + mobilePattern, "i") } },
          { phone1: { $regex: new RegExp(".*" + mobilePattern, "i") } },
          { phone2: { $regex: new RegExp(".*" + mobilePattern, "i") } },
          { phone3: { $regex: new RegExp(".*" + mobilePattern, "i") } },
        ]
      });
    }
    
    // 到院状态筛选 - 优化条件
    if (context.arrivalStatus === "notInHospital") {
      orConditions.push({
        $or: [
          { inHospitalTimes: { $exists: false } },
          { inHospitalTimes: { $size: 0 } },
          { inHospitalTimes: null },
        ]
      });
    } else if (context.arrivalStatus === "inHospital") {
      query.inHospitalTimes = { $exists: true, $not: { $size: 0 } };
    }
    
    // 将所有 $or 条件合并到 andList 中
    if (orConditions.length > 0) {
      andList.push(...orConditions);
    }
    
    // 性别筛选 - 精确匹配而非正则
    if (typeof context.sex === "string" && context.sex.trim()) {
      query.sex = context.sex.trim();
    }
    
    // 客户阶段筛选 - 索引友好
    if (Array.isArray(context.customerStage) && context.customerStage.length) {
      query.customerStage = { $in: context.customerStage };
      indexOptimizedQuery.customerStage = { $in: context.customerStage };
    }
    
    // 客户来源筛选
    if (Array.isArray(context.customerSource) && context.customerSource.length) {
      query.customerSource = { $in: context.customerSource };
      indexOptimizedQuery.customerSource = { $in: context.customerSource };
    }

    if (Array.isArray(context.infoSource) && context.infoSource.length) {
      query.infoSource = { $in: context.infoSource };
      indexOptimizedQuery.infoSource = { $in: context.infoSource };
    }

    // 标签筛选
    if (Array.isArray(context.tagIds) && context.tagIds.length) {
      query.tagIds = { $in: context.tagIds };
    }
    
    // 添加类型筛选
    if (context.addMethod) {
      query.addMethod = context.addMethod;
      indexOptimizedQuery.addMethod = context.addMethod;
    }
    
    // 创建人类型筛选
    if (context.creators && context.creators.length) {
      query.creator = { $in: context.creators };
    }
    
    // 根据外部联系人id筛选
    if (context.externalUserId) {
      query["externalUserId"] = context.externalUserId;
    }
    if (Array.isArray(context.externalUserIds)) {
      query["externalUserId"] = { $in: context.externalUserIds };
    }
    
    // 团队筛选 - 索引优化
    if (context.teamId === "NO_TEAM") {
      query.teamId = { $in: [[], null, ""] };
    } else if (context.teamId === "HAS_TEAM") {
      query.teamId = { $nin: [[], null, ""] };
    } else if (Array.isArray(context.teamId)) {
      query.teamId = { $in: context.teamId };
      indexOptimizedQuery.teamId = { $in: context.teamId };
    } else if (context.teamId) {
      query.teamId = context.teamId;
      indexOptimizedQuery.teamId = context.teamId;
    }

    let exprList = [];
    // 筛选最近的咨询师记录 - 优化表达式
    if (context.counselors && Array.isArray(context.counselors) && context.counselors.length > 0) {
      exprList.push({
        $in: [
          { $arrayElemAt: [{ $reverseArray: "$counselorRecord.counselor" }, 0] },
          context.counselors,
        ],
      });
    }
    
    if (context.introducers && Array.isArray(context.introducers) && context.introducers.length > 0) {
      exprList.push({
        $in: [
          { $arrayElemAt: [{ $reverseArray: "$introducerRecord.introducer" }, 0] },
          context.introducers,
        ],
      });
    }
    
    // 到院时间筛选优化
    if (context.startArriveTime && context.endArriveTime) {
      exprList.push({
        $and: [
          { $isArray: "$inHospitalTimes" },
          { $gt: [{ $size: "$inHospitalTimes" }, 0] },
          {
            $let: {
              vars: { lastVisit: { $arrayElemAt: ["$inHospitalTimes", -1] } },
              in: {
                $and: [
                  context.startArriveTime ? {
                    $gte: ["$$lastVisit", dayjs(context.startArriveTime).valueOf()]
                  } : true,
                  context.endArriveTime ? {
                    $lte: ["$$lastVisit", dayjs(context.endArriveTime).valueOf()]
                  } : true,
                ],
              },
            },
          },
        ],
      });
    }
    
    if (context.reportTeamId) {
      exprList.push({
        $eq: [
          { $arrayElemAt: [{ $reverseArray: "$introducerRecord.teamId" }, 0] },
          context.reportTeamId,
        ],
      });
    }

    if (exprList.length > 0) {
      query["$expr"] = exprList.length === 1 ? exprList[0] : { $and: exprList };
    }
    
    // 筛选年龄区间 - 优化年龄计算
    const minAge = parseInt(context.minAge);
    const maxAge = parseInt(context.maxAge);
    if (!isNaN(minAge) && minAge >= 0 && !isNaN(maxAge) && maxAge >= 0 && maxAge >= minAge) {
      const currentTime = dayjs();
      // 修正年龄计算逻辑：生日戳应该在合理的范围内
      const maxAgeStamp = currentTime.subtract(maxAge, "year").startOf("year").valueOf();
      const minAgeStamp = currentTime.subtract(minAge, "year").endOf("year").valueOf();
      
      andList.push({
        $or: [
          { age: { $gte: minAge, $lte: maxAge } },
          { 
            birthdayStamp: { 
              $gte: maxAgeStamp, 
              $lte: minAgeStamp 
            } 
          },
        ],
      });
    } else if (!isNaN(minAge) && minAge >= 0) {
      const minAgeStamp = dayjs().subtract(minAge, "year").endOf("year").valueOf();
      andList.push({
        $or: [
          { age: { $gte: minAge } },
          { birthdayStamp: { $lte: minAgeStamp } },
        ],
      });
    } else if (!isNaN(maxAge) && maxAge >= 0) {
      const maxAgeStamp = dayjs().subtract(maxAge, "year").startOf("year").valueOf();
      andList.push({
        $or: [
          { age: { $lte: maxAge } },
          { birthdayStamp: { $gte: maxAgeStamp } },
        ],
      });
    }

    // 责任人筛选优化
    if (context.customerType === "myCustomer") {
      query["personResponsibles"] = {
        $elemMatch: {
          corpUserId: context.userId,
          teamId: context.teamId,
        },
      };
    } else if (Array.isArray(context.personResponsibles) && context.personResponsibles.length) {
      const personResponsibles = context.personResponsibles.filter((i) => i !== "NO");
      const noPersonResponsible = context.personResponsibles.includes("NO");
      const orList = [];
      
      if (personResponsibles.length) {
        orList.push({
          $elemMatch: {
            corpUserId: { $in: personResponsibles },
            teamId: Array.isArray(context.teamId)
              ? { $in: context.teamId }
              : context.teamId,
          },
        });
      }
      
      if (noPersonResponsible) {
        orList.push({
          $not: {
            $elemMatch: {
              teamId: Array.isArray(context.teamId)
                ? { $in: context.teamId }
                : context.teamId,
            },
          },
        });
      }
      
      if (orList.length === 1) {
        query["personResponsibles"] = orList[0];
      } else if (orList.length > 1) {
        andList.push({
          $or: orList.map((i) => ({ personResponsibles: i })),
        });
      }
    }
    
    // 网店报备责任人筛选
    if (context.reportRersonResponsibles) {
      query["personResponsibles"] = {
        $elemMatch: {
          corpUserId: { $in: context.reportRersonResponsibles },
        },
      };
    }

    // 筛选是否分组 - 优化分组查询
    if (context.hasGroup === "YES") {
      const groupIds = await getGroupIds(context.corpId, context.teamId);
      if (groupIds.length > 0) {
        query["groupIds"] = { $in: groupIds };
      } else {
        // 如果没有分组，直接返回空结果
        return { success: true, total: 0, list: [], pages: 0 };
      }
    } else if (context.hasGroup === "NO") {
      const groupIds = await getGroupIds(context.corpId, context.teamId);
      if (groupIds.length > 0) {
        query["groupIds"] = { $nin: groupIds };
      }
    }
    if (Array.isArray(context.groupIds)) {
      query.groupIds = { $in: context.groupIds };
    }

    // 创建时间筛选
    const createTimeQuery = getTimeRange(context.startCreateTime, context.endCreateTime);
    if (createTimeQuery) {
      query.createTime = createTimeQuery;
      indexOptimizedQuery.createTime = createTimeQuery;
    }

    // 最近服务时间筛选
    const serviceTimeQuery = getTimeRange(context.startServiceTime, context.endServiceTime);
    if (serviceTimeQuery) {
      query.serviceTime = serviceTimeQuery;
    }
    if (context.serviceTime === "NO") {
      query.serviceTime = { $exists: false };
    }
    
    // 入院时间筛选
    if (Array.isArray(context.inHospitalDates) && context.inHospitalDates.length === 2) {
      const inHospitalTimesQuery = getTimeRange(...context.inHospitalDates);
      if (inHospitalTimesQuery) {
        query.inHospitalTimes = inHospitalTimesQuery;
      }
    }
    
    // 根据意向项目id筛选
    if (Array.isArray(context.projectIds) && context.projectIds.length) {
      query["projectIds"] = { $in: context.projectIds };
    }

    // 检查是否需要根据预约时间筛选
    const needAppointmentTimeFilter = context.startAppointmentTime || context.endAppointmentTime;

    // 优化：使用分离查询策略
    if (needAppointmentTimeFilter) {
      return await executeAppointmentTimeQuery(context, query, andList, page, pageSize);
    } else {
      return await executeStandardQuery(context, query, andList, page, pageSize, startTime);
    }
  } catch (error) {
    console.error("[searchCorpCustomer] 查询失败:", error);
    
    // 如果是索引提示错误，提供更友好的错误信息
    if (error.message.includes("hint provided does not correspond to an existing index")) {
      console.error("[searchCorpCustomer] 索引提示错误，请检查数据库索引配置");
      return { 
        success: false, 
        message: "数据库索引配置错误，请联系管理员检查索引设置", 
        error: "INDEX_HINT_ERROR" 
      };
    }
    
    // 如果是集合不存在错误
    if (error.message.includes("ns does not exist") || error.message.includes("memberTree")) {
      console.error("[searchCorpCustomer] 集合不存在或映射错误");
      return { 
        success: false, 
        message: "数据库集合配置错误，请联系管理员", 
        error: "COLLECTION_ERROR" 
      };
    }
    
    return { success: false, message: error.message, error: error.code };
  }
};

// 优化：分离预约时间查询逻辑
async function executeAppointmentTimeQuery(context, query, andList, page, pageSize) {
  const allQuery = andList.length ? { $and: [...andList, query] } : query;

  // 创建优化的查询管道
  const pipeline = [
    { $match: allQuery },
    {
      $lookup: {
        from: "estore-appointment-record",
        let: { customerId: "$_id" },
        pipeline: [
          { $match: { $expr: { $eq: ["$customerId", "$$customerId"] } } },
          { $sort: { appointmentTime: -1 } },
          { $limit: 1 },
          { $project: { appointmentTime: 1 } }
        ],
        as: "appointmentRecords",
      },
    },
    {
      $addFields: {
        lastAppointmentTime: {
          $ifNull: [{ $arrayElemAt: ["$appointmentRecords.appointmentTime", 0] }, null]
        },
      },
    },
  ];

  // 添加预约时间范围筛选条件
  if (context.startAppointmentTime && context.endAppointmentTime) {
    pipeline.push({
      $match: {
        lastAppointmentTime: {
          $gte: dayjs(context.startAppointmentTime).startOf("day").valueOf(),
          $lte: dayjs(context.endAppointmentTime).endOf("day").valueOf(),
        }
      }
    });
  }

  // 使用 facet 同时获取总数和分页数据
  pipeline.push({
    $facet: {
      totalCount: [{ $count: "count" }],
      data: [
        { $sort: { createTime: -1 } },
        { $skip: pageSize * (page - 1) },
        { $limit: pageSize },
        { $project: { appointmentRecords: 0 } }
      ]
    }
  });

  // 添加其他查询选项
  await addComputedFields(pipeline, context);

  let result;
  try {
    result = await db.collection("member").aggregate(pipeline, {
      allowDiskUse: true,
      maxTimeMS: 60000, // 60秒超时
    }).toArray();
  } catch (error) {
    // 如果是索引提示错误，尝试不使用查询优化选项重新执行
    if (error.message.includes("hint provided does not correspond to an existing index")) {
      console.warn("[executeAppointmentTimeQuery] 索引提示错误，尝试简化查询");
      result = await db.collection("member").aggregate(pipeline).toArray();
    } else {
      throw error;
    }
  }

  const total = result[0]?.totalCount[0]?.count || 0;
  const list = result[0]?.data || [];
  const pages = Math.ceil(total / pageSize);

  // 处理项目名称
  if (context.showProjectName === "YES") {
    await enrichProjectNames(list, context.corpId);
  }

  return { success: true, total, list, pages };
}

// 优化：标准查询逻辑
async function executeStandardQuery(context, query, andList, page, pageSize, startTime) {
  const allQuery = andList.length ? { $and: [...andList, query] } : query;
  
  console.log(`[searchCorpCustomer] 查询条件:`, JSON.stringify(allQuery, null, 2));

  // 使用 facet 同时获取总数和数据，提高查询效率
  const pipeline = [
    { $match: allQuery },
    {
      $facet: {
        totalCount: [{ $count: "count" }],
        data: [
          { $sort: { createTime: -1 } },
          { $skip: pageSize * (page - 1) },
          { $limit: pageSize }
        ]
      }
    }
  ];

  // 添加计算字段
  await addComputedFields(pipeline[1].$facet.data, context);

  // 添加字段过滤
  if (context.filterField) {
    pipeline[1].$facet.data.push({ $project: context.filterField });
  }

  let result;
  try {
    result = await db.collection("member").aggregate(pipeline, {
      allowDiskUse: true,
      maxTimeMS: 60000, // 60秒超时
    }).toArray();
  } catch (error) {
    // 如果是索引提示错误，尝试不使用查询优化选项重新执行
    if (error.message.includes("hint provided does not correspond to an existing index")) {
      console.warn("[executeStandardQuery] 索引提示错误，尝试简化查询");
      result = await db.collection("member").aggregate(pipeline).toArray();
    } else {
      throw error;
    }
  }

  const total = result[0]?.totalCount[0]?.count || 0;
  const list = result[0]?.data || [];
  const pages = Math.ceil(total / pageSize);

  // 处理项目名称
  if (context.showProjectName === "YES") {
    await enrichProjectNames(list, context.corpId);
  }

  const endTime = Date.now();
  console.log(`[searchCorpCustomer] 查询完成: 耗时${endTime - startTime}ms, 返回${list.length}条记录`);

  return { success: true, total, list, pages };
}

// 优化：添加计算字段的辅助函数
async function addComputedFields(pipeline, context) {
  // 查询是否有回访计划
  if (context.showHasPlan === "YES") {
    pipeline.push(
      {
        $lookup: {
          from: "to-do-events",
          let: { customerId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$customerId", "$$customerId"] },
                    { $eq: ["$eventStatus", "untreated"] },
                    { $eq: ["$executeTeamId", context.createTeamId] }
                  ]
                }
              }
            },
            { $limit: 1 }
          ],
          as: "todoEvents",
        },
      },
      {
        $addFields: {
          hasPlan: { $gt: [{ $size: "$todoEvents" }, 0] },
        },
      },
      { $project: { todoEvents: 0 } }
    );
  }

  // 查询消费金额
  if (context.showConsumeAmount === "YES") {
    pipeline.push({
      $lookup: {
        from: "consume-record",
        let: { customerId: "$_id" },
        pipeline: [
          { $match: { $expr: { $eq: ["$customerId", "$$customerId"] } } },
          { $project: { _id: 1, amount: 1 } }
        ],
        as: "consumeRecord",
      },
    });
  }

  // 查询预约记录
  if (context.showAppointmentTime === "YES") {
    pipeline.push(
      {
        $lookup: {
          from: "estore-appointment-record",
          let: { customerId: "$_id" },
          pipeline: [
            { $match: { $expr: { $eq: ["$customerId", "$$customerId"] } } },
            { $sort: { appointmentTime: -1 } },
            { $limit: 1 },
            { $project: { appointmentTime: 1 } }
          ],
          as: "appointmentRecords",
        },
      },
      {
        $addFields: {
          lastAppointmentTime: {
            $ifNull: [{ $arrayElemAt: ["$appointmentRecords.appointmentTime", 0] }, null]
          },
        },
      },
      { $project: { appointmentRecords: 0 } }
    );
  }
}

// 优化：项目名称enrichment
async function enrichProjectNames(list, corpId) {
  if (!list || list.length === 0) return;
  
  const projectIds = list.reduce((ids, item) => {
    if (Array.isArray(item?.projectIds)) {
      ids.push(...item.projectIds);
    }
    return ids;
  }, []);

  if (projectIds.length === 0) {
    list.forEach((item) => {
      item.projectNames = [];
      item.projectDeptIds = [];
    });
    return;
  }

  try {
    const res = await api.getCorpApi({
      type: "getProjectIntentNames",
      corpId,
      ids: [...new Set(projectIds)], // 去重
    });

    if (res?.data?.length) {
      const projectMaps = res.data.reduce(
        (maps, item) => {
          if (item._id) {
            if (item.projectName) maps.nameMap.set(item._id, item.projectName);
            if (item.deptId) maps.deptMap.set(item._id, item.deptId);
          }
          return maps;
        },
        { nameMap: new Map(), deptMap: new Map() }
      );
      
      list.forEach((item) => {
        if (Array.isArray(item.projectIds)) {
          item.projectNames = item.projectIds
            .map((id) => projectMaps.nameMap.get(id))
            .filter(Boolean);
          item.projectDeptIds = item.projectIds
            .map((id) => projectMaps.deptMap.get(id))
            .filter(Boolean);
        } else {
          item.projectNames = [];
          item.projectDeptIds = [];
        }
      });
    }
  } catch (error) {
    console.error("获取项目名称失败:", error);
    list.forEach((item) => {
      item.projectNames = [];
      item.projectDeptIds = [];
    });
  }
}

// 优化：分组ID查询 - 添加缓存
const groupIdsCache = new Map();
async function getGroupIds(corpId, teamId) {
  const cacheKey = `${corpId}_${teamId}`;
  
  // 检查缓存（5分钟过期）
  if (groupIdsCache.has(cacheKey)) {
    const cached = groupIdsCache.get(cacheKey);
    if (Date.now() - cached.timestamp < 5 * 60 * 1000) {
      return cached.data;
    }
  }

  try {
    const query = { corpId, teamId };
    const result = await db
      .collection("group")
      .find(query, { projection: { _id: 1 } })
      .toArray();

    const groupIds = result.map((item) => item._id);
    
    // 缓存结果
    groupIdsCache.set(cacheKey, {
      data: groupIds,
      timestamp: Date.now()
    });

    return groupIds;
  } catch (error) {
    logger.error("Error getting group IDs:", error);
    return [];
  }
}

/**
 *  批量更新客户信息
 * 暂支持 customerSource 客户来源及关联的推荐人
 * 暂支持 tagIds 客户标签
 * 暂支持 customerStage 客户阶段
 * @param {*} context
 * @returns
 */

exports.batchUpdateCustomer = async (context) => {
  const { corpId, customerIds, modifyField } = context;
  // 1. 验证输入参数
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!Array.isArray(customerIds) || customerIds.length === 0)
    return { success: false, message: "客户id不能为空" };
  if (!["customerSource", "tagIds", "customerStage"].includes(modifyField)) {
    return { success: false, message: "修改字段不存在或者修改字段不支持" };
  }
  try {
    // 2. 批量修改客户来源
    if (modifyField === "customerSource") {
      const {
        customerSource,
        reference,
        referenceCustomerId,
        referenceType,
        referenceUserId,
      } = context;

      // 2.1. 验证客户来源相关参数
      if (
        !Array.isArray(customerSource) ||
        [reference, referenceCustomerId, referenceType, referenceUserId].some(
          (i) => typeof i !== "string"
        )
      ) {
        return { success: false, message: "客户来源参数错误" };
      }

      // 2.2. 使用MongoDB原生查询批量更新
      const updateResult = await db.collection("member").updateMany(
        { corpId, _id: { $in: customerIds } }, // 查询条件
        {
          $set: {
            customerSource,
            reference,
            referenceCustomerId,
            referenceType,
            referenceUserId,
          },
        }
      );

      if (updateResult.modifiedCount > 0) {
        return { success: true, message: "修改成功" };
      } else {
        return { success: false, message: "没有客户需要更新" };
      }
    }

    // 3. 批量修改客户标签
    else if (modifyField === "tagIds") {
      const { tagIds, tagType } = context;

      // 3.1. 验证标签参数
      if (!Array.isArray(tagIds))
        return { success: false, message: "标签类型错误" };

      if (tagType === "cover") {
        // 3.2. 覆盖原有标签
        const updateResult = await db
          .collection("member")
          .updateMany(
            { corpId, _id: { $in: customerIds } },
            { $set: { tagIds } }
          );
        return updateResult.modifiedCount > 0
          ? { success: true, message: "修改成功" }
          : { success: false, message: "没有客户需要更新" };
      } else if (tagType === "append") {
        // 3.3. 追加标签
        if (tagIds.length === 0) return { success: true, message: "修改成功" };

        const customerList = await db
          .collection("member")
          .find({ corpId, _id: { $in: customerIds } })
          // .project({ _id: 1, tagIds: 1 })
          .toArray();

        if (customerList.length === 0)
          return { success: false, message: "客户不存在" };

        console.log("customerList", customerList);
        // 合并标签并更新
        const updatedCustomerList = customerList.map((customer) => {
          const existingTagIds = Array.isArray(customer.tagIds)
            ? customer.tagIds
            : [];
          const newTagIds = Array.from(new Set([...existingTagIds, ...tagIds]));
          return { _id: customer._id, tagIds: newTagIds };
        });

        console.log("updatedCustomerList", updatedCustomerList);

        // 批量更新客户标签
        const bulkOperations = updatedCustomerList.map((customer) => ({
          updateOne: {
            filter: { _id: customer._id },
            update: { $set: { tagIds: customer.tagIds || [] } },
          },
        }));

        const bulkWriteResult = await db
          .collection("member")
          .bulkWrite(bulkOperations);

        return bulkWriteResult.modifiedCount > 0
          ? { success: true, message: "修改成功" }
          : { success: false, message: "没有客户需要更新" };
      } else {
        return { success: false, message: "标签修改类型错误" };
      }
    }

    // 4. 批量修改客户阶段
    else if (modifyField === "customerStage") {
      const { customerStage } = context;

      // 4.1. 验证客户阶段参数
      if (typeof customerStage !== "string")
        return { success: false, message: "客户阶段参数错误" };

      // 4.2. 使用MongoDB原生查询批量更新
      const updateResult = await db
        .collection("member")
        .updateMany(
          { corpId, _id: { $in: customerIds } },
          { $set: { customerStage } }
        );

      return updateResult.modifiedCount > 0
        ? { success: true, message: "修改成功" }
        : { success: false, message: "没有客户需要更新" };
    }
  } catch (error) {
    // 错误处理：如果发生错误，返回错误信息
    logger.error("批量更新客户时发生错误:", error);
    return {
      success: false,
      message: error.message,
    };
  }
};

function getTimeRange(startTime, endTime) {
  const startTimeStamp =
    startTime && dayjs(startTime).isValid()
      ? dayjs(startTime).startOf("day").valueOf()
      : 0;
  const endTimeStamp =
    endTime && dayjs(endTime).isValid()
      ? dayjs(endTime).endOf("day").valueOf()
      : 0;
  if (startTimeStamp && endTimeStamp) {
    return { $gte: startTimeStamp, $lte: endTimeStamp };
  }
  if (startTimeStamp) {
    return { $gte: startTimeStamp };
  }
  if (endTimeStamp) {
    return { $lte: endTimeStamp };
  }
  return "";
}

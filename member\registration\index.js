const dayjs = require("dayjs");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "registration":
      return await exports.registrationAction(content);
    case "getRegistrationCountByUserId":
      return await exports.getRegistrationCountByUserId(content);
    case "getRegistration":
      return await exports.getRegistration(content);
    case "getRegistrationCountByTeam":
      return await exports.getRegistrationCountByTeam(content);
  }
};

// 报到操作
exports.registrationAction = async (context) => {
  const { memberId, teamId, corpUserId } = context;
  if (!memberId) return { success: false, message: "客户ID未传" };
  if (!teamId) return { success: false, message: "团队ID未传" };
  const now = new Date();
  try {
    const record = await db.collection("registration").findOne({
      memberId,
      teamId,
      createTime: {
        $gte: dayjs(now).startOf("day").valueOf(),
        $lte: dayjs(now).endOf("day").valueOf(),
      },
    });
    if (record) return { success: false, message: "您今天已经报到过了" };
    await db.collection("registration").insertOne({
      _id: common.generateRandomString(24),
      memberId,
      teamId,
      time: now,
      corpUserId,
      createTime: now.getTime(),
    });
    return {
      success: true,
      time: now.getTime(),
      message: "报到成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 获取团队报到数量
exports.getRegistrationCountByTeam = async (context) => {
  const { teamId = "", timeType } = context;
  let query = { teamId };
  if (timeType === "LASTMONTH") {
    const startOfLastMonth = dayjs()
      .subtract(1, "month")
      .startOf("month")
      .valueOf();
    const endOfLastMonth = dayjs()
      .subtract(1, "month")
      .endOf("month")
      .valueOf();
    query.createTime = { $gte: startOfLastMonth, $lte: endOfLastMonth };
  } else if (timeType === "THISMONTH") {
    const startOfMonth = dayjs().startOf("month").valueOf();
    const endOfMonth = dayjs().endOf("month").valueOf();
    query.createTime = { $gte: startOfMonth, $lte: endOfMonth };
  }
  try {
    const total = await db.collection("registration").countDocuments(query);
    return {
      success: true,
      data: total,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 获取用户报到数量
exports.getRegistrationCountByUserId = async (context) => {
  const { teamId = "", userId, timeType } = context;
  if (!userId) {
    return {
      success: false,
      message: "成员ID未传",
    };
  }
  let query = { teamId, corpUserId: userId };
  if (timeType === "LASTMONTH") {
    const startOfLastMonth = dayjs()
      .subtract(1, "month")
      .startOf("month")
      .valueOf();
    const endOfLastMonth = dayjs()
      .subtract(1, "month")
      .endOf("month")
      .valueOf();
    query.createTime = { $gte: startOfLastMonth, $lte: endOfLastMonth };
  } else if (timeType === "THISMONTH") {
    const startOfMonth = dayjs().startOf("month").valueOf();
    const endOfMonth = dayjs().endOf("month").valueOf();
    query.createTime = { $gte: startOfMonth, $lte: endOfMonth };
  }
  try {
    const total = await db.collection("registration").countDocuments(query);
    return {
      success: true,
      data: total,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 获取成员最新报到时间
exports.getRegistration = async (context) => {
  const { memberId, teamId } = context;
  if (!memberId) return { success: false, message: "客户ID未传" };
  if (!teamId) return { success: false, message: "团队ID未传" };
  try {
    const record = await db
      .collection("registration")
      .findOne(
        { memberId, teamId },
        { sort: { createTime: -1 }, projection: { _id: 0, createTime: 1 } }
      );
    return {
      success: true,
      lastReportTime: record ? record.createTime : 0,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

const dayjs = require("dayjs");
const common = require("../../common");
let db = null;
let eventsDB = null;

exports.main = async (event, DB) => {
  db = DB;
  eventsDB = db.collection("to-do-events");
  switch (event.type) {
    case "statisticsEventsByTeamId":
      return await this.statisticsEventsByTeamId(event);
    case "statisticsUserEventsByTeamId":
      return await this.statisticsUserEventsByTeamId(event);
    case "statisticsEvents":
      return await this.statisticsEvents(event);
  }
};

// 统计团队事件
exports.statisticsEventsByTeamId = async (context) => {
  const { corpId, teamId } = context;
  try {
    let res = await eventsDB
      .aggregate([
        {
          $match: {
            corpId,
            executeTeamId: teamId,
          },
        },
        {
          $group: {
            _id: "$eventStatus",
            num: { $sum: 1 },
          },
        },
      ])
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 统计用户事件
exports.statisticsUserEventsByTeamId = async (context) => {
  const { corpId, teamId } = context;
  try {
    let res = await eventsDB
      .aggregate([
        {
          $match: {
            corpId,
            executeTeamId: teamId,
          },
        },
        {
          $group: {
            _id: "$executorUserId",
            expireCount: {
              $sum: {
                $cond: [{ $eq: ["$eventStatus", "expire"] }, 1, 0],
              },
            },
            closedCount: {
              $sum: {
                $cond: [{ $eq: ["$eventStatus", "closed"] }, 1, 0],
              },
            },
            treatedCount: {
              $sum: {
                $cond: [{ $eq: ["$eventStatus", "treated"] }, 1, 0],
              },
            },
            untreatedCount: {
              $sum: {
                $cond: [{ $eq: ["$eventStatus", "untreated"] }, 1, 0],
              },
            },
          },
        },
      ])
      .toArray();

    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 统计事件
exports.statisticsEvents = async (context) => {
  const { params = {}, corpId } = context;
  const { executorUserId, teamId, teamIds } = params;
  let executeTeamId = "";
  if (teamId) {
    executeTeamId = teamId;
  } else if (teamIds && teamIds.length > 0) {
    executeTeamId = { $in: teamIds };
  }
  const todayStartTime = dayjs().startOf("day").valueOf();
  const todayEndTime = dayjs().endOf("day").valueOf();
  let query = {
    corpId,
  };
  if (executeTeamId) query["executeTeamId"] = executeTeamId;
  if (executorUserId) query["executorUserId"] = executorUserId;

  // 未办结
  async function getUntreatedCount() {
    return await eventsDB.countDocuments({
      ...query,
      plannedExecutionTime: { $lte: todayEndTime },
      expireTime: { $gte: todayStartTime },
      eventStatus: "untreated",
    });
  }

  // 已办结
  async function getTreatedCount() {
    return await eventsDB.countDocuments({
      ...query,
      eventStatus: { $in: ["treated", "expire", "closed"] },
    });
  }

  // 公共待办
  async function getCommonEventsCount() {
    return await eventsDB.countDocuments({
      executeTeamId,
      createTime: { $lte: todayEndTime },
      eventStatus: "untreated",
      expireTime: { $gte: todayStartTime },
      executorUserId: "",
      corpId,
    });
  }

  // 今日未完成待办
  async function getUntreatedCountToday() {
    return await eventsDB.countDocuments({
      ...query,
      eventStatus: "untreated",
      plannedExecutionTime: { $gte: todayStartTime, $lte: todayEndTime },
    });
  }

  // 今日已办
  async function getTreatedCountToday() {
    return await eventsDB.countDocuments({
      ...query,
      endTime: { $gte: todayStartTime, $lte: todayEndTime },
      eventStatus: { $in: ["treated", "expire", "closed"] },
    });
  }

  let result = await Promise.all([
    getUntreatedCount(),
    getTreatedCount(),
    getCommonEventsCount(),
    getUntreatedCountToday(),
    getTreatedCountToday(),
  ]);

  let [
    untreatedCount,
    treatedCount,
    commonEventsCount,
    untreatedCountToday,
    treatedCountToday,
  ] = result;

  return {
    success: true,
    message: "获取成功",
    data: {
      untreatedCount,
      treatedCount,
      commonEventsCount,
      untreatedCountToday,
      treatedCountToday,
      todayStartTime,
      todayEndTime,
    },
  };
};

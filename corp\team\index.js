const utils = require("../utils");
const common = require("../../common");
let db = null;
exports.main = async (event, mongodb) => {
  db = mongodb;
  return await getTeamData(event, mongodb);
};
async function getTeamData(event) {
  const { teamId } = event;
  try {
    // 获取团队信息
    const team = await db.collection("team").findOne({ teamId });
    if (team) {
      // 获取团队成员信息
      const corpMember = await getMemberList(team.corpId);
      let { memberList } = team;
      team.memberList = memberList
        .map((userid) => {
          return corpMember.find((item) => item.userid === userid);
        })
        .filter((i) => i);
      return {
        success: true,
        message: "获取团队信息成功",
        data: team,
      };
    }
    return {
      success: false,
      message: "未查询到团队信息",
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
}

// 获取机构信息
async function getCorpData(item) {
  return await db.collection("corp").findOne({ corpId: item.corpId });
}

// 获取团队成员信息
async function getMemberList(corpId) {
  return await db.collection("corp-member").find({ corpId }).toArray();
}

exports.getAllTeamByCorp = async (event) => {
  let { corpId } = event;
  try {
    let res = await db.collection("team").find({ corpId }).toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

exports.getTeamBymember = async (event) => {
  const { corpUserId, corpId } = event;
  try {
    let res = await db
      .collection("team")
      .find({ memberList: corpUserId, corpId })
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

exports.getTeamByMainLeaderUserId = async (context) => {
  const { userId, corpId } = context;
  try {
    let res = await db
      .collection("team")
      .find({ corpId, memberLeaderList: userId })
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

exports.getCorpTeams = async (context) => {
  const { page, pageSize, corpId, name = "", userId = "" } = context;
  const query = { corpId };
  if (typeof name === "string" && name.trim()) {
    query.name = { $regex: ".*" + name.trim() + ".*", $options: "i" };
  }
  if (userId) query.memberList = userId;
  const teamDB = db.collection("team");
  const total = await teamDB.countDocuments(query); // 总数
  const pages = Math.ceil(total / pageSize);
  const teams = await teamDB
    .find(query)
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .toArray();
  const handler = async (item) => {
    let count = await db
      .collection("member")
      .countDocuments({ corpId, teamId: item.teamId });
    item.customerCount = count;
    return item;
  };
  let list = await utils.processInBatches(teams, handler, 10);
  return {
    success: true,
    message: "获取成功",
    list: list.map((i) => ({
      ...i,
      memberList: Array.isArray(i.memberList) ? i.memberList : [],
    })),
    total: total,
    pages: pages,
    size: pageSize,
  };
};

exports.getTeamById = async (context) => {
  try {
    const { id: teamId, teamIds = [] } = context;
    if (!teamId && (!Array.isArray(teamIds) || teamIds.length === 0)) {
      return { success: false, message: "团队id不能为空" };
    }
    const query = {};
    if (teamId) query.teamId = teamId;
    else {
      query.teamId = { $in: teamIds };
    }
    const teamDB = db.collection("team");
    const res = await teamDB.find(query).toArray();
    const list = res.map((i) => ({
      ...i,
      memberList: Array.isArray(i.memberList) ? i.memberList : [],
    }));
    return {
      success: true,
      message: "获取成功",
      data: list,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

exports.updateTeam = async (context) => {
  try {
    const { teamId, _id, type, ...payload } = context;
    if (!teamId) {
      return {
        success: false,
        message: "新增团队失败(缺少teamId)",
      };
    }
    const query = { teamId };
    const teamDB = db.collection("team");
    const team = await teamDB.findOne(query);
    const isUpdate = Boolean(team);
    if (isUpdate) {
      await teamDB.updateOne(query, { $set: payload });
    } else {
      await teamDB.insertOne({
        ...payload,
        teamId,
        _id: common.generateRandomString(24),
      });
    }
    const { memberList } = context;
    await addTeamIdtoMember(memberList, teamId);
    return {
      success: true,
      message: isUpdate ? "团队信息更新成功" : "新增团队成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

// 机构分组新建团队
async function addGroupToTeam(context) {
  const { corpId, teamId } = context;
  const corpGroupList = await db
    .collection("group")
    .find({ corpId, groupType: "corp" })
    .toArray();
  const groupList = corpGroupList.map((item) => item.groupId);
}

// 判断所加成员是否含有teamId
async function addTeamIdtoMember(memberList, teamId) {
  const memberDB = db.collection("corp-member");
  const members = await memberDB
    .find({ userid: { $in: memberList } })
    .toArray();
  const noTeamIdMemberList = members
    .filter((item) => !item.teamId)
    .map((item) => item.userid);
  if (noTeamIdMemberList.length > 0) {
    await memberDB.updateMany(
      { userid: { $in: memberList } },
      { $set: { teamId } }
    );
  }
}

exports.getTeamLeaderNumByUserId = async (context) => {
  const { userId, corpId } = context;
  try {
    const count = await db
      .collection("team")
      .countDocuments({ corpId, memberLeaderList: userId });
    return {
      success: true,
      message: "获取成功",
      data: count,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.updateMemberRolesByTeam = async (context) => {
  const { corpId, params, teamId } = context;
  const { addLeaders, removeLeaders, addTeamMember, removeTeamMember } = params;
  try {
    // 1.获取团队负责人角色id 和 团队成员角色id
    const { memberRoleId, memberLeaderRoleId } = await getRolesId(corpId);
    if (addLeaders.length > 0) {
      for (let userid of addLeaders) {
        await getTeamLeaderRoleIdByUserId(
          userid,
          teamId,
          corpId,
          "add",
          memberLeaderRoleId
        );
      }
    }
    if (removeLeaders.length > 0) {
      for (let userid of removeLeaders) {
        await getTeamLeaderRoleIdByUserId(
          userid,
          teamId,
          corpId,
          "remove",
          memberLeaderRoleId
        );
      }
    }
    if (addTeamMember.length > 0) {
      for (let userid of addTeamMember) {
        await getTeamMemberRoleIdByUserId(
          userid,
          teamId,
          corpId,
          "add",
          memberRoleId
        );
      }
    }
    if (removeTeamMember.length > 0) {
      for (let userid of removeTeamMember) {
        await getTeamMemberRoleIdByUserId(
          userid,
          teamId,
          corpId,
          "remove",
          memberRoleId
        );
      }
    }
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

async function getTeamMemberRoleIdByUserId(
  userId,
  teamId,
  corpId,
  type,
  memberRoleId
) {
  // 除了 teamId 之外的其他团队 的数量
  const count = await db.collection("team").countDocuments({
    corpId,
    memberList: userId,
    teamId: { $ne: teamId },
  });
  if (count === 0 && type === "add") {
    // 添加团队成员
    await db
      .collection("corp-member")
      .updateOne(
        { corpId, userid: userId },
        { $addToSet: { roleIds: memberRoleId } }
      );
  }
  if (count === 0 && type === "remove") {
    // 删除团队成员
    await db
      .collection("corp-member")
      .updateOne(
        { corpId, userid: userId },
        { $pull: { roleIds: memberRoleId } }
      );
  }
}

async function getTeamLeaderRoleIdByUserId(
  userId,
  teamId,
  corpId,
  type,
  memberLeaderRoleId
) {
  const count = await db.collection("team").countDocuments({
    corpId,
    memberLeaderList: userId,
    teamId: { $ne: teamId },
  });
  if (count === 0 && type === "add") {
    // 添加团队负责人
    await db
      .collection("corp-member")
      .updateOne(
        { corpId, userid: userId },
        { $addToSet: { roleIds: memberLeaderRoleId } }
      );
  }
  if (count === 0 && type === "remove") {
    // 删除团队负责人
    await db
      .collection("corp-member")
      .updateOne(
        { corpId, userid: userId },
        { $pull: { roleIds: memberLeaderRoleId } }
      );
  }
}

async function getRolesId(corpId) {
  const roles = await db.collection("sys-role").find({ corpId }).toArray();
  const memberRoleId = roles.find((item) => item.roleId === "member")._id;
  const memberLeaderRoleId = roles.find(
    (item) => item.roleId === "memberLeader"
  )._id;
  return {
    memberRoleId,
    memberLeaderRoleId,
  };
}

exports.deleteCorpTeam = async function (context) {
  const { corpId, teamId, _id } = context;
  if (teamId && corpId && _id) {
    try {
      const team = await db.collection("team").findOne({ corpId, teamId, _id });
      if (team) {
        const total = await db
          .collection("member")
          .countDocuments({ corpId, teamId });
        if (total === 0) {
          await db.collection("team").deleteOne({ _id });
          return { success: true, message: "删除成功" };
        }
        return { success: false, message: "该团队下存在客户，无法删除" };
      }
      return { success: false, message: "团队不存在" };
    } catch (e) {
      return { success: false, message: e.message };
    }
  }
  return { success: false, message: "参数错误" };
};

exports.getCustomTeamData = async function (context) {
  const { corpUserId, corpId, fields, teamIds } = context;
  if (!corpId || !Array.isArray(fields) || fields.length === 0) {
    return { success: false, message: "参数错误" };
  }
  // 可查询多个团队
  try {
    const query = { corpId };
    if (teamIds && teamIds.length) {
      query.teamId = { $in: teamIds };
    }
    if (corpUserId) {
      query.memberList = { $in: [corpUserId] };
    }
    const project = fields.reduce(
      (acc, val) => {
        if (typeof val === "string" && val.trim()) {
          acc[val] = 1;
        }
        return acc;
      },
      { teamId: 1, _id: 0 }
    );
    // 获取角色信息
    const res = await db
      .collection("team")
      .find(query)
      .project(project)
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

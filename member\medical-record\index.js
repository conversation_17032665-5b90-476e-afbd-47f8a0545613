const dayjs = require("dayjs");
const sop = require("../sop");
const common = require("../../common");
const appFunction = require("../app-function");
const customer = require("../customer");
const customerHisSyncAppFunction = require("../app-function/customerHisSync.js");
let db = null;
let recordDB = null;
exports.main = async (content, DB) => {
  db = DB;
  recordDB = db.collection("medical-record");
  switch (content.type) {
    case "addMedicalRecord":
      return await exports.add(content);
    case "getCustomerMedicalRecord":
      return await exports.getList(content);
    case "getMedicalRecordById":
      return await exports.getById(content);
    case "updateMedicalRecord":
      return await exports.update(content);
    case "removeMedicalRecord":
      return await exports.remove(content);
    case "mergeMedicalHisRecord":
      return await mergeMedicalHisRecord(content);
    case "getArriveTime":
      return await exports.getArriveTime(content);
    case "getMedicalFiledsTrend":
      return await exports.getMedicalFiledsTrend(content);
    case "addHealthIndicators":
      return await addHealthIndicators(content);
    case "getHealthIndicators":
      return await getHealthIndicators(content);
    case "getHealthIndicatorsTemplate":
      return await exports.getHealthIndicatorsTemplate(content);
  }
};

async function getHealthIndicators({ corpId, memberId }) {
  if (!memberId) {
    return { success: false, message: "客户id不能为空" };
  }
  if (!corpId) {
    return { success: false, message: "机构id不能为空" };
  }
  try {
    const list = await db
      .collection("corp-health-indicators")
      .find({ memberId, corpId })
      .limit(1000)
      .toArray();
    return { success: true, message: "获取客户信息成功", list };
  } catch (e) {
    return { success: false, message: "获取客户信息失败" };
  }
}

exports.getHealthIndicatorsTemplate = async function (context) {
  const { corpId } = context;
  try {
    let list = await db
      .collection("health-indicators-template")
      .find({ corpId })
      .toArray();
    if (list.length === 0) {
      list = await db
        .collection("health-indicators-template")
        .find({ corpId: { $exists: false } })
        .toArray();
    }
    const templateList =
      Array.isArray(list) && list[0] ? list[0].templateList : [];
    return { success: true, message: "获取客户信息成功", templateList };
  } catch (e) {
    return { success: false, message: "获取客户信息失败" };
  }
};

exports.getMedicalFiledsTrend = async function (context) {
  const { corpId, memberId, field, dates } = context;
  const { startTime, lastTime } = getStartAndEndTime(dates);
  if (!memberId) {
    return { success: false, message: "客户id不能为空" };
  }
  if (!corpId) {
    return { success: false, message: "机构id不能为空" };
  }
  if (!field) {
    return { success: false, message: "字段不能为空" };
  }
  let query = { corpId, memberId, [field]: { $exists: true } };
  if (startTime && lastTime) {
    query.createTime = { $gte: startTime, $lte: lastTime };
  }
  try {
    const list = await recordDB
      .find(query)
      .project({ [field]: 1, createTime: 1, _id: 0 })
      .sort({ createTime: 1 })
      .toArray();
    return { success: true, message: "获取客户信息成功", list };
  } catch (e) {
    return { success: false, message: "获取客户信息失败" };
  }
};

function getStartAndEndTime(dates) {
  if (!dates || dates.length === 0) {
    return {
      startTime: "",
      lastTime: "",
    };
  }
  let t_1 = dates[0];
  let t_2 = dates[1];
  return {
    startTime: t_1 && new Date(t_1).setHours(0, 0, 0, 0),
    lastTime: t_2 && new Date(t_2).setHours(23, 59, 59, 999),
  };
}

exports.add = async function (context) {
  const {
    memberId,
    corpId,
    personResponsibles,
    customerUserId,
    createTeamId,
    createTeamName,
    customerName = "",
    ...rest
  } = context;
  if (!memberId) {
    return { success: false, message: "客户id不能为空" };
  }
  if (!corpId) {
    return { success: false, message: "机构id不能为空" };
  }
  try {
    const id = common.generateRandomString(24);
    await recordDB.insertOne({
      _id: id,
      ...rest,
      memberId,
      corpId,
      createTime: new Date().getTime(),
    });

    // 更新健康指标
    const healthIndicatorsActtionTask = healthIndicatorsActtion({
      corpId,
      memberId,
      rest,
      type: "add",
      recordId: id,
    });
    const time = rest.sortTime && dayjs(rest.sortTime).isValid();
    const case1 =
      ["outpatient", "inhospital"].includes(rest.medicalType) &&
      rest.corp === "本院";
    const case2 = [
      "healthProjectTemplate",
      "physicalExaminationTemplate",
    ].includes(rest.medicalType);
    //更新入院时间到客户档案
    let updateCustomerTimeFieldTask = null;
    if (time && (case1 || case2)) {
      updateCustomerTimeFieldTask = customer.main(
        {
          corpId,
          customerId: memberId,
          inHospitalTime: rest.sortTime,
          type: "updateCustomerInHospitalTime",
        },
        db
      );
    }
    let triggerSopTask = null;
    if (rest.medicalType !== "healthRecord") {
      triggerSopTask = triggerSop({
        medicalRecord: rest,
        memberId,
        corpId,
        medicalRecordId: id,
      });
    }
    const params = {
      eventType: "customerUpdate",
      customerId: memberId,
      executorUserId: rest.creator,
      executeTeamId: createTeamId,
      createTeamName,
      creatorUserId: rest.creator,
      corpId,
      customerName,
      createTime: dayjs().valueOf(),
      taskContent: `更新${customerName}客户健康档案`,
      executionTime: dayjs().valueOf(),
    };
    const addServiceRecordTask = rest.creator
      ? appFunction.addServiceRecord(params)
      : null;
    await Promise.all([
      healthIndicatorsActtionTask,
      updateCustomerTimeFieldTask,
      triggerSopTask,
      addServiceRecordTask,
    ]);
    return {
      success: true,
      message: "新增成功",
      data: id,
    };
  } catch (e) {
    return { success: false, message: "新增失败" };
  }
};

updateCustomerVisitTime = async function (context) {
  const { memberId, visitTime } = context;
  if (!memberId) {
    return { success: false, message: "客户id不能为空" };
  }
  if (!visitTime) {
    return { success: false, message: "到院时间不能为空" };
  }
  try {
    await db
      .collection("member")
      .updateOne(
        { _id: memberId, visitTimes: { $nin: [visitTime] } },
        { $push: { visitTimes: visitTime } }
      );
    return { success: true, message: "更新成功" };
  } catch (e) {
    return { success: false, message: "更新失败" };
  }
};

// 执行sop
async function triggerSop({
  medicalRecord,
  memberId,
  corpId,
  medicalRecordId,
  healthIndicators,
}) {
  if (!memberId) return;
  if (
    medicalRecord &&
    medicalRecord.medicalType === "outpatient" &&
    medicalRecord.diagnosisName
  ) {
    medicalRecord["outpatientDiagnosis"] = medicalRecord.diagnosisName;
  }
  if (
    medicalRecord &&
    medicalRecord.medicalType === "inhospital" &&
    medicalRecord.diagnosisName
  ) {
    medicalRecord["inhospitalDiagnosis"] = medicalRecord.diagnosisName;
  }
  const query = {
    corpId,
    customerId: memberId,
    medicalRecord,
    medicalRecordId,
    healthIndicators,
  };
  let res = await sop.main(
    {
      type: "triggerSopTask",
      ...query,
    },
    db
  );
  console.log("sop", res);
}

// 更改健康指标操作
async function healthIndicatorsActtion({
  corpId,
  memberId,
  recordId,
  rest,
  type,
}) {
  try {
    let healthIndicators = {};
    let { templateList, success } = await exports.getHealthIndicatorsTemplate({
      corpId,
    });
    if (success) {
      for (let item of templateList) {
        healthIndicators[item.title] = item.name;
      }
    }
    //遍历 rest 的所有字段
    let isMatch = false;
    for (let key in rest) {
      if (healthIndicators[key]) {
        isMatch = true;
        if (type === "update" || type === "add") {
          let value = rest[key];
          if (typeof value === "string" && !isNaN(value)) value = Number(value);
          await addHealthIndicators({
            corpId,
            memberId,
            field: key,
            value,
            recordId,
            type,
          });
        } else if (type === "remove") {
          await removeRecordMerageHealthIndicators({
            corpId,
            memberId,
            recordId,
            field: key,
          });
        }
      }
    }
    if (isMatch) {
      // 触发sop
      console.log("触发sop");
      const { success, list } = await getHealthIndicators({ corpId, memberId });
      let healthIndicators = {};
      if (success && list) {
        for (let item of list) {
          healthIndicators[item.type] = item.value;
        }
      }
      await triggerSop({ memberId, corpId, healthIndicators });
    }
  } catch (e) {
    console.error("healthIndicatorsActtion error:", e);
  }
}

async function addHealthIndicators({
  corpId,
  memberId,
  field,
  value,
  name,
  recordId,
  type,
}) {
  if (!memberId) {
    return { success: false, message: "客户id不能为空" };
  }
  if (!corpId) {
    return { success: false, message: "机构id不能为空" };
  }
  try {
    const params = { type: field, memberId, corpId };
    const query = type === "update" ? { ...params, recordId } : params;
    const count = await db
      .collection("corp-health-indicators")
      .find(query)
      .count();
    if (count > 0) {
      await db.collection("corp-health-indicators").updateOne(params, {
        $set: { value, updateTime: new Date().getTime(), recordId },
      });
    } else {
      type === "add" &&
        (await db.collection("corp-health-indicators").insertOne({
          _id: common.generateRandomString(24),
          ...params,
          createTime: new Date().getTime(),
          value,
          name,
          recordId,
        }));
    }
    return { success: true, message: "新增成功" };
  } catch (e) {
    return { success: false, message: "新增失败" };
  }
}
// 删除健康档案后同步健康指标
async function removeRecordMerageHealthIndicators({
  corpId,
  memberId,
  recordId,
  field,
}) {
  // 获取到档案中最新一条field的值
  const record = await recordDB
    .find({ corpId, memberId, [field]: { $exists: true } })
    .sort({ createTime: -1 })
    .limit(1)
    .toArray();
  let value = record.length ? record[0][field] : "";
  const newRecordId = record.length ? record[0]._id : "";
  const params = { type: field, memberId, corpId };
  if (typeof value === "string" && !isNaN(value)) value = Number(value);
  const count = await db
    .collection("corp-health-indicators")
    .find({ ...params, recordId })
    .count();
  if (count > 0) {
    await db.collection("corp-health-indicators").updateOne(
      { ...params, recordId },
      {
        $set: {
          value,
          updateTime: new Date().getTime(),
          recordId: newRecordId,
        },
      }
    );
  } else {
    await db.collection("corp-health-indicators").insertOne({
      _id: common.generateRandomString(24),
      ...params,
      createTime: new Date().getTime(),
      value,
      recordId: newRecordId,
    });
  }
}

exports.getById = async function (context) {
  const { corpId, _id, memberId, medicalType } = context;
  if (!_id) {
    return { success: false, message: "id不能为空" };
  }
  if (!medicalType) {
    return { success: false, message: "类型不能为空" };
  }
  if (!memberId) {
    return { success: false, message: "客户id不能为空" };
  }
  if (!corpId) {
    return { success: false, message: "机构id不能为空" };
  }
  try {
    const record = await recordDB.findOne({
      _id,
      corpId,
      memberId,
      medicalType,
    });
    if (record) {
      return { success: true, message: "查询信息成功", record };
    }
    return { success: false, message: "未查询到信息" };
  } catch (e) {
    return { success: false, message: "查询信息失败" };
  }
};

exports.getList = async function (context) {
  let { memberId, corpId, startTime, endTime, medicalType, dataSource } =
    context;
  if (!memberId) {
    return { success: false, message: "客户id不能为空" };
  }
  if (!corpId) {
    return { success: false, message: "机构id不能为空" };
  }
  // 判断medicalType 是否是字符串
  if (typeof medicalType === "string") {
    medicalType = [medicalType];
  }
  try {
    const query = { memberId, corpId };
    if (dataSource) query.dataSource = dataSource;
    if (medicalType) query.medicalType = { $in: medicalType };
    const dateFilter = {};
    if (startTime) dateFilter.$gte = startTime;
    if (endTime) dateFilter.$lte = endTime;

    // 如果日期筛选条件不为空，添加到 sortTime 字段
    if (Object.keys(dateFilter).length) {
      query.sortTime = dateFilter;
    }
    const list = await recordDB.find(query).sort({ sortTime: -1 }).toArray();
    // hisList 根据sortTime排序
    list.sort((a, b) => b.sortTime - a.sortTime);
    return { success: true, message: "获取客户信息成功", list };
  } catch (e) {
    return { success: false, message: e.message || "获取客户信息失败" };
  }
};
async function mergeMedicalHisRecord(context) {
  const { corpId, customerNumber, memberId, recordDates } = context;
  let startTime = "",
    endTime = "";
  if (recordDates && Array.isArray(recordDates)) {
    startTime = recordDates[0];
    endTime = recordDates[1];
  }

  if (!customerNumber) {
    return { success: false, message: "客户编号不能为空" };
  }

  try {
    // 获取现有的医疗记录，仅查询ID和visitId、hospitalizationId等必要字段
    const customerMedicalRecord = await recordDB
      .find({ corpId, memberId })
      .toArray();
    const existingVisitIds = new Set(
      customerMedicalRecord.map((item) => item.visitId)
    );
    const existingHospitalizationIds = new Set(
      customerMedicalRecord.map((item) => item.hospitalizationId)
    );

    let medicalTypes = ["outpatient", "inhospital"];

    const tasks = medicalTypes.map(async (medicalType) => {
      const query = { corpId, customerNumber, startTime, endTime };
      let { list, success } =
        medicalType === "outpatient"
          ? await customerHisSyncAppFunction.getHisOutHospitalRecord(query)
          : await customerHisSyncAppFunction.getHisInHospitalRecord(query);
      console.log(list);
      if (!success || !list) return;
      const recordsToAdd = list
        .filter((i) => {
          i.corp = "本院";
          i.corpId = corpId;
          i.memberId = memberId;
          i.medicalType = medicalType;
          if (medicalType === "outpatient") {
            i.sortTime = i.visitTime ? dayjs(i.visitTime).valueOf() : "";
            return !existingVisitIds.has(i.visitId); // 检查是否已存在
          } else if (medicalType === "inhospital") {
            i.sortTime = i.inhosDate ? dayjs(i.inhosDate).valueOf() : "";
            return !existingHospitalizationIds.has(i.hospitalizationId); // 检查是否已存在
          }
          return false;
        })
        .map((i) => {
          i.visitTime = i.visitTime ? dayjs(i.visitTime).valueOf() : "";
          return i;
        });

      if (recordsToAdd.length > 0) {
        await Promise.all(recordsToAdd.map((record) => exports.add(record)));
      }
    });

    // 执行所有异步任务并等待完成
    await Promise.all(tasks);

    return { success: true, message: "合并成功" };
  } catch (e) {
    console.error("mergeMedicalHisRecord error:", e);
    return { success: false, message: "合并失败" };
  }
}

exports.update = async function (context) {
  const { memberId, corpId, _id, userId, creator, ...rest } = context;
  if (!_id) {
    return { success: false, message: "id不能为空" };
  }
  if (!corpId) {
    return { success: false, message: "机构id不能为空" };
  }
  try {
    const record = await exports.getById({
      corpId,
      _id,
      memberId,
      medicalType: rest.medicalType,
    });
    if (record.success) {
      if (record.record.dataSource !== "customerReport" && !userId) {
        return { success: false, message: "员工id不能为空" };
      }
      await recordDB.updateOne(
        { _id },
        {
          $set: {
            ...rest,
            memberId,
            corpId,
            updateBy: userId,
            updateTime: new Date().getTime(),
          },
        }
      );
      // 更新健康指标
      await healthIndicatorsActtion({
        corpId,
        memberId,
        recordId: _id,
        rest,
        type: "update",
      });
      if (rest.medicalType !== "healthRecord") {
        await triggerSop({
          medicalRecord: rest,
          memberId,
          corpId,
          medicalRecordId: _id,
        });
      }
      return { success: true, message: "更新成功" };
    }
    return { success: false, message: record.message };
  } catch (e) {
    console.log(e);
    return { success: false, message: "更新失败" };
  }
};

exports.remove = async function (context) {
  const { memberId, corpId, _id, medicalType } = context;
  if (!_id) {
    return { success: false, message: "id不能为空" };
  }
  if (!corpId) {
    return { success: false, message: "机构id不能为空" };
  }
  if (!memberId) {
    return { success: false, message: "客户id不能为空" };
  }
  if (!medicalType) {
    return { success: false, message: "类型不能为空" };
  }

  try {
    const record = await exports.getById({
      corpId,
      _id,
      memberId,
      medicalType,
    });
    if (record.success) {
      await recordDB.deleteOne({ _id });
      await healthIndicatorsActtion({
        corpId,
        memberId,
        recordId: _id,
        rest: record.record,
        type: "remove",
      });
      return { success: true, message: "删除成功" };
    }
    return { success: false, message: record.message };
  } catch (e) {
    console.log(e);
    return { success: false, message: "删除失败" };
  }
};

exports.getArriveTime = async (context) => {
  try {
    const { corpId, memberId } = context;
    if (corpId && memberId) {
      const query = await recordDB
        .find({ corpId, memberId, corp: { $ne: "其他" } })
        .sort({ sortTime: -1 })
        .limit(1)
        .project({ sortTime: 1, _id: 0 })
        .toArray();
      const time =
        query && query.length && query[0].sortTime > 0 ? query[0].sortTime : 0;
      if (time > 0) {
        return { success: true, message: "查询到最近到院时间成功", time };
      }
    }
    return { success: false, message: "未查询到最近到院时间" };
  } catch (e) {
    return { success: false, message: "查询到最近到院时间失败" };
  }
};

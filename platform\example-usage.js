const { connectToMongoDB, getDatabase } = require('../mongodb');
const platform = require('./index');

/**
 * 使用改造后的 platform 模块的示例
 */
async function exampleUsage() {
  try {
    // 连接 MongoDB
    await connectToMongoDB();
    
    // 获取数据库实例 (请根据实际数据库名称修改)
    const db = await getDatabase('your-database-name'); 
    
    // 示例：登录
    const loginEvent = {
      type: 'login',
      params: {
        userName: 'testuser',
        password: 'testpass',
        env: 'production'
      }
    };
    
    const loginResult = await platform.main(loginEvent, db);
    console.log('Login result:', loginResult);
    
    // 示例：获取企业列表
    const getCorpListEvent = {
      type: 'getCorpList',
      env: 'production',
      page: 1,
      pageSize: 10,
      params: {
        corpName: '',
        corpType: ''
      }
    };
    
    const corpListResult = await platform.main(getCorpListEvent, db);
    console.log('Corp list result:', corpListResult);
    
    // 示例：添加企业
    const addCorpEvent = {
      type: 'addCorp',
      env: 'production',
      params: {
        corpId: 'test-corp-001',
        corpName: '测试企业',
        corpType: 'medical'
      }
    };
    
    const addCorpResult = await platform.main(addCorpEvent, db);
    console.log('Add corp result:', addCorpResult);
    
  } catch (error) {
    console.error('Error in platform usage:', error);
  }
}

module.exports = { exampleUsage }; 
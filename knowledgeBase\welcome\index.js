const { generateRandomString } = require('../../common');

let db = null;
let welcomeDB = null;

exports.main = async (content, DB) => {
  db = DB;
  welcomeDB = db.collection("welcome-words");

  switch (content.type) {
    case "getWelcomeList":
      return await exports.getList(content);
    case "getWelcome":
      return await exports.getWords(content);
    case "updateWelcome":
      return await exports.update(content);
    case "removeWelcome":
      return await exports.deleteData(content);
  }
};

function log(fn, err) {
  console.log(`invoke ${fn} fail: `, err);
}

exports.getWords = async (context) => {
  const { id: _id, corpId } = context;
  try {
    const words = await welcomeDB.findOne({ _id, corpId, disabled: false });
    if (words) {
      return {
        success: true,
        message: "获取欢迎语成功",
        data: words,
      };
    }
    return {
      success: false,
      message: "获取欢迎语失败",
    };
  } catch (e) {
    log("getWords", e);
    return {
      success: false,
      message: "获取欢迎语失败",
    };
  }
};

exports.getList = async (context) => {
  const { pageSize = 10, page = 1, corpId = "" } = context;
  try {
    const query = { disabled: false, corpId };
    const total = await welcomeDB.countDocuments(query);
    const pages = Math.ceil(total / pageSize);
    const list = await welcomeDB
      .find(query)
      .sort({ createTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    return {
      success: true,
      message: "获取成功",
      list,
      total,
      pages,
    };
  } catch (e) {
    log("getList", e);
    return {
      success: false,
      message: "获取欢迎语列表数据失败",
    };
  }
};

exports.update = async (context) => {
  const { _id, content = "", memberList = [], files = [], corpId } = context;
  try {
    const data = { content, memberList, files, corpId };
    const repeatRes = await ifMemberRepeat(data.memberList, _id);
    if (repeatRes.repeat) {
      return {
        success: true,
        code: "REPEATMEMBER",
        members: repeatRes.members,
      };
    }
    const res = await (_id ? updateWords(_id, data) : addWords(data));
    return res;
  } catch (e) {
    log("update", e);
    return { success: false, message: `${_id ? "更新" : "新增"}欢迎语失败` };
  }
};

async function addWords(data) {
  const createTime = new Date().getTime();
  try {
    const _id = generateRandomString(24);
    await welcomeDB.insertOne({ ...data, _id, createTime, disabled: false });
    return {
      success: true,
      message: "欢迎语新增成功",
      id: _id,
    };
  } catch (e) {
    return {
      success: false,
      message: "新增欢迎语失败",
    };
  }
}

async function ifMemberRepeat(memberList, _id) {
  const query = { memberList: { $in: memberList } };
  if (_id) query._id = { $ne: _id };
  const data = await welcomeDB.find(query).toArray();
  const members = Array.isArray(data)
    ? data.reduce((val, item) => {
        const userIds = Array.isArray(item.memberList)
          ? item.memberList.filter(
              (userId) =>
                memberList.some((i) => i == userId) &&
                !val.some((i) => i == userId)
            )
          : [];
        return [...val, ...userIds];
      }, [])
    : [];
  return { repeat: members.length > 0, members };
}

async function updateWords(_id, data) {
  const updateTime = new Date().getTime();
  try {
    const words = await welcomeDB.findOne({ _id, corpId: data.corpId, disabled: false });
    if (words) {
      await welcomeDB.updateOne({ _id }, { $set: { ...data, updateTime } });
      return {
        success: true,
        message: "欢迎语更新成功",
      };
    } else {
      return {
        success: false,
        message: "欢迎语不存在",
      };
    }
  } catch (e) {
    log("updateWords", e);
    return {
      success: false,
      message: e,
    };
  }
}

exports.deleteData = async (event) => {
  try {
    const updateTime = new Date().getTime();
    await welcomeDB.updateOne(
      { _id: event.id },
      { $set: { disabled: true, updateTime, deleteBy: event.operator } }
    );
    return {
      success: true,
      message: "删除欢迎语成功",
    };
  } catch (error) {
    log("deleteData", error);
    return {
      success: false,
      message: "删除欢迎语失败",
    };
  }
};

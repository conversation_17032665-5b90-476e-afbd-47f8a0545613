const utils = require("../utils");
const api = require("../../api");
const common = require("../../common");

let db = null;
exports.main = async (event, mongodb) => {
  db = mongodb;
  switch (event.type) {
    case "getTeamData":
      return await getTeamData(event);
    case "getAllTeamByCorp":
      return await exports.getAllTeamByCorp(event);
    case "getTeamBymember":
      return await exports.getTeamBymember(event);
    case "getTeamByMainLeaderUserId":
      return await exports.getTeamByMainLeaderUserId(event);
    case "getCorpTeams":
      return await exports.getCorpTeams(event);
    case "getTeamById":
      return await exports.getTeamById(event);
    case "updateMemberRolesByTeam":
      return await exports.updateMemberRolesByTeam(event);
    case "getTeamLeaderNumByUserId":
      return await exports.getTeamLeaderNumByUserId(event);
    case "updateTeam":
      return await exports.updateTeam(event);
    case "getCustomTeamData":
      return await exports.getCustomTeamData(event);
    case "deleteCorpTeam":
      return await exports.deleteCorpTeam(event);
    case "getTeamNamesByTeamIds":
      return await getTeamNamesByTeamIds(event);
  }
};

async function getTeamNamesByTeamIds({ corpId, teamIds }) {
  // 查询团队名称
  const { data: teams } = await db
    .collection("team")
    .find({ corpId, teamId: { $in: teamIds } })
    .toArray();
  const teamNames = teams.map((item) => item.name);
  return {
    success: true,
    data: teamNames,
    message: "获取团队名称成功",
  };
}

async function getTeamData(event) {
  const { env, teamId } = event;
  try {
    // 获取团队信息
    const team = await db.collection("team").findOne({ teamId });
    if (team) {
      // 获取团队成员信息
      const corpMember = await getMemberList(team.corpId);
      let { memberList } = team;
      team.memberList = memberList
        .map((userid) => {
          return corpMember.find((item) => item.userid === userid);
        })
        .filter((i) => i);
      return {
        success: true,
        message: "获取团队信息成功",
        data: team,
      };
    }
    return {
      success: false,
      message: "未查询到团队信息",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
}

// 获取团队成员信息
async function getMemberList(corpId) {
  return await db.collection("corp-member").find({ corpId }).toArray();
}

exports.getAllTeamByCorp = async (event) => {
  const { env, corpId } = event;
  try {
    const teams = await db.collection("team").find({ corpId }).toArray();
    return {
      success: true,
      message: "获取成功",
      data: teams,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

exports.getTeamBymember = async (event) => {
  const { corpUserId, corpId } = event;
  try {
    const teams = await db
      .collection("team")
      .find({ memberList: corpUserId, corpId })
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data: teams,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

exports.getTeamByMainLeaderUserId = async (context) => {
  const { env, userId, corpId } = context;
  try {
    const teams = await db
      .collection("team")
      .find({ corpId, memberLeaderList: userId })
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data: teams,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

exports.getCorpTeams = async (context) => {
  const { env, page, pageSize, corpId, name = "", userId = "", fields, showCustomerCount = true } = context;
  const query = { corpId };
  if (typeof name === "string" && name.trim()) {
    query.name = { $regex: ".*" + name.trim() + ".*", $options: "i" };
  }
  if (userId) query.memberList = userId;
  const teamDB = db.collection("team");
  const total = await teamDB.countDocuments(query);
  const pages = Math.ceil(total / pageSize);
  let list = [];
  const teams = await db.collection("team")
    .find(query, Object.prototype.toString.call(fields) === "[object Object]" ? fields : {})
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .toArray();

  if (showCustomerCount) {
    // 然后，对于每个 team，查询成员数量
    const teamIds = teams.map(team => team.teamId);
    const customerCounts = await api.getMemberApi({
      type: "getTeamCustmerCount",
      corpId,
      teamIds
    })

    // 将查询结果合并到原来的 team 数据中
    list = teams.map(team => {
      const customerCount = customerCounts.find(count => count._id === team.teamId);
      team.customerCount = customerCount ? customerCount.count : 0;
      return team;
    });
  } else {
    list = teams
  }

  return {
    success: true,
    message: "获取成功",
    list: list.map((i) => ({
      ...i,
      memberList: Array.isArray(i.memberList) ? i.memberList : [],
    })),
    total,
    pages,
    size: pageSize,
  };
};

exports.getTeamById = async (context) => {
  try {
    const { env, id: teamId, teamIds = [] } = context;
    if (!teamId && (!Array.isArray(teamIds) || teamIds.length === 0)) {
      return { success: false, message: "团队id不能为空" };
    }
    const query = teamId ? { teamId } : { teamId: { $in: teamIds } };
    const teams = await db.collection("team").find(query).toArray();
    const list = teams.map((i) => ({
      ...i,
      memberList: Array.isArray(i.memberList) ? i.memberList : [],
    }));
    return {
      success: true,
      message: "获取成功",
      data: list,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

exports.updateTeam = async (context) => {
  const { corpId, teamId, _id, type, ...payload } = context;
  if (!teamId) {
    return {
      success: false,
      message: "新增团队失败(缺少teamId)",
    };
  }
  const query = { teamId };
  const teamDB = db.collection("team");
  const team = await teamDB.findOne(query);
  const isUpdate = Boolean(team);
  if (isUpdate) {
    await teamDB.updateOne(query, { $set: payload });
  } else {
    await teamDB.insertOne({ ...payload, teamId, corpId, _id: common.generateRandomString(24) });
  }
  const { memberList } = context;
  await addTeamIdtoMember(memberList, teamId);
  let res = null;
  if (isUpdate) {
    res = await api.getMemberApi({
      teamId,
      corpId,
      type: "updateSopTaskExecuteUser",
    });
  }
  return {
    success: true,
    message: isUpdate ? "团队信息更新成功" : "新增团队成功",
    res,
  };
};

// 判断所加成员是否含有teamId
async function addTeamIdtoMember(memberList, teamId) {
  const memberDB = db.collection("corp-member");
  const members = await memberDB
    .find({ userid: { $in: memberList } })
    .toArray();
  const noTeamIdMemberList = members
    .filter((item) => !item.teamId)
    .map((item) => item.userid);
  if (noTeamIdMemberList.length > 0) {
    await memberDB.updateMany(
      { userid: { $in: noTeamIdMemberList } },
      { $set: { teamId } }
    );
  }
}

exports.getTeamLeaderNumByUserId = async (context) => {
  const { env, userId, corpId } = context;
  try {
    const count = await db
      .collection("team")
      .countDocuments({ corpId, memberLeaderList: userId });
    return {
      success: true,
      message: "获取成功",
      data: count,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.updateMemberRolesByTeam = async (context) => {
  const { env, corpId, params, teamId } = context;
  const { addLeaders, removeLeaders, addTeamMember, removeTeamMember } = params;
  try {
    const { memberRoleId, memberLeaderRoleId } = await getRolesId(env, corpId);
    if (addLeaders.length > 0) {
      for (const userid of addLeaders) {
        await getTeamLeaderRoleIdByUserId(
          env,
          userid,
          teamId,
          corpId,
          "add",
          memberLeaderRoleId
        );
      }
    }
    if (removeLeaders.length > 0) {
      for (const userid of removeLeaders) {
        await getTeamLeaderRoleIdByUserId(
          env,
          userid,
          teamId,
          corpId,
          "remove",
          memberLeaderRoleId
        );
      }
    }
    if (addTeamMember.length > 0) {
      for (const userid of addTeamMember) {
        await getTeamMemberRoleIdByUserId(
          env,
          userid,
          teamId,
          corpId,
          "add",
          memberRoleId
        );
      }
    }
    if (removeTeamMember.length > 0) {
      for (const userid of removeTeamMember) {
        await getTeamMemberRoleIdByUserId(
          env,
          userid,
          teamId,
          corpId,
          "remove",
          memberRoleId
        );
      }
    }
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

async function getTeamMemberRoleIdByUserId(
  env,
  userId,
  teamId,
  corpId,
  type,
  memberRoleId
) {
  const count = await db.collection("team").countDocuments({
    corpId,
    memberList: userId,
    teamId: { $ne: teamId },
  });
  if (count === 0 && type === "add") {
    await db
      .collection("corp-member")
      .updateOne(
        { corpId, userid: userId },
        { $addToSet: { roleIds: memberRoleId } }
      );
  }
  if (count === 0 && type === "remove") {
    await db
      .collection("corp-member")
      .updateOne(
        { corpId, userid: userId },
        { $pull: { roleIds: memberRoleId } }
      );
  }
}

async function getTeamLeaderRoleIdByUserId(
  env,
  userId,
  teamId,
  corpId,
  type,
  memberLeaderRoleId
) {
  const count = await db.collection("team").countDocuments({
    corpId,
    memberLeaderList: userId,
    teamId: { $ne: teamId },
  });
  if (count === 0 && type === "add") {
    await db
      .collection("corp-member")
      .updateOne(
        { corpId, userid: userId },
        { $addToSet: { roleIds: memberLeaderRoleId } }
      );
  }
  if (count === 0 && type === "remove") {
    await db
      .collection("corp-member")
      .updateOne(
        { corpId, userid: userId },
        { $pull: { roleIds: memberLeaderRoleId } }
      );
  }
}

async function getRolesId(env, corpId) {
  const roles = await db.collection("sys-role").find({ corpId }).toArray();
  const memberRoleId = roles.find((item) => item.roleId === "member")._id;
  const memberLeaderRoleId = roles.find(
    (item) => item.roleId === "memberLeader"
  )._id;
  return {
    memberRoleId,
    memberLeaderRoleId,
  };
}

exports.deleteCorpTeam = async function (context) {
  const { env, corpId, teamId, _id } = context;
  if (teamId && corpId && _id) {
    try {
      const team = await db.collection("team").findOne({ corpId, teamId, _id });
      if (team) {
        const total = await db
          .collection("member")
          .countDocuments({ corpId, teamId });
        if (total === 0) {
          await db.collection("team").deleteOne({ _id });
          return { success: true, message: "删除成功" };
        }
        return { success: false, message: "该团队下存在客户，无法删除" };
      }
      return { success: false, message: "团队不存在" };
    } catch (e) {
      return { success: false, message: e.message };
    }
  }
  return { success: false, message: "参数错误" };
};

exports.getCustomTeamData = async function (context) {
  const { env, corpUserId, corpId, fields, teamIds } = context;
  if (!corpId || !Array.isArray(fields) || fields.length === 0) {
    return { success: false, message: "参数错误" };
  }
  try {
    const query = { corpId };
    if (teamIds && teamIds.length) {
      query.teamId = { $in: teamIds };
    }
    if (corpUserId) {
      query.memberList = { $in: [corpUserId] };
    }
    const projection = fields.reduce(
      (acc, val) => {
        if (typeof val === "string" && val.trim()) {
          acc[val] = 1;
        }
        return acc;
      },
      { teamId: 1, _id: 0 }
    );
    const teams = await db
      .collection("team")
      .find(query, { projection })
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data: teams,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

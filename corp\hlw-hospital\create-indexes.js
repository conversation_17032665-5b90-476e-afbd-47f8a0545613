/**
 * 创建hlw-hospital集合的索引以优化性能
 * MongoDB索引创建脚本
 */

const createHlwHospitalIndexes = async (db) => {
  try {
    const collection = db.collection("hlw-hospital");
    
    // 创建复合索引
    await collection.createIndex(
      { corpId: 1, workId: 1 },
      { unique: true, name: "corpId_workId_unique" }
    );
    
    // 创建corpId索引
    await collection.createIndex(
      { corpId: 1 },
      { name: "corpId_index" }
    );
    
    // 创建院内工号索引
    await collection.createIndex(
      { workId: 1 },
      { name: "workId_index" }
    );
    
    // 创建姓名索引（支持模糊搜索）
    await collection.createIndex(
      { name: "text" },
      { name: "name_text_index" }
    );
    
    // 创建部门索引
    await collection.createIndex(
      { dept: 1 },
      { name: "dept_index" }
    );
    
    // 创建岗位索引
    await collection.createIndex(
      { job: 1 },
      { name: "job_index" }
    );
    
    // 创建门诊科室索引
    await collection.createIndex(
      { outpatientDept: 1 },
      { name: "outpatientDept_index" }
    );
    
    // 创建推荐状态索引
    await collection.createIndex(
      { recommend: 1 },
      { name: "recommend_index" }
    );
    
    // 创建排序索引
    await collection.createIndex(
      { sortOrder: 1 },
      { name: "sortOrder_index" }
    );
    
    // 创建创建时间索引
    await collection.createIndex(
      { createTime: -1 },
      { name: "createTime_desc_index" }
    );
    
    // 创建更新时间索引
    await collection.createIndex(
      { updateTime: -1 },
      { name: "updateTime_desc_index" }
    );
    
    // 创建复合索引用于常用查询
    await collection.createIndex(
      { corpId: 1, dept: 1, job: 1 },
      { name: "corpId_dept_job_index" }
    );
    
    // 创建推荐人员查询索引
    await collection.createIndex(
      { corpId: 1, recommend: 1, sortOrder: 1 },
      { name: "corpId_recommend_sortOrder_index" }
    );
    
    // 创建全文搜索索引
    await collection.createIndex(
      {
        name: "text",
        dept: "text",
        job: "text",
        title: "text",
        outpatientDept: "text",
        expertise: "text",
        workId: "text"
      },
      { 
        name: "full_text_search_index",
        weights: {
          name: 10,
          workId: 8,
          dept: 5,
          job: 5,
          title: 3,
          outpatientDept: 3,
          expertise: 1
        }
      }
    );
    
    console.log("hlw-hospital集合索引创建完成");
    return true;
  } catch (error) {
    console.error("创建hlw-hospital集合索引失败:", error);
    return false;
  }
};

module.exports = {
  createHlwHospitalIndexes
};

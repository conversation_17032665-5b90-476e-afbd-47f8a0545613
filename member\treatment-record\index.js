const dayjs = require("dayjs");
const billdRecord = require("../bill-record/index.js");
const common = require("../../common.js");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getTreatmentRecord":
      return getTreatmentRecord(content);
    case "addTreatmentRecord":
      return addTreatmentRecord(content);
    case "removeTreatmentRecord":
      return removeTreatmentRecord(content);
    case "updateTreatmentRecord":
      return updateTreatmentRecord(content);
    case "treatmentRecordStatistic":
      return treatmentRecordStatistic(content);
    case "addDeductRecord":
      return addDeductRecord(content);
    case "updateDeductRecord":
      return updateDeductRecord(content);
    case "getDeductRecord":
      return getDeductRecord(content);
    case "treatmentRecordStatisticByStatus":
      return treatmentRecordStatisticByStatus(content);
    case "deductRecordStatistic":
      return deductRecordStatistic(content);
    case "removeDeductRecord":
      return removeDeductRecord(content);
    case "getYizhipaiCustomPhotos":
      return getYizhipaiCustomPhotos(content);
  }
};

/**
 * 获取账单记录
 * @param {string} corpId 机构id
 * @returns
 */

async function getTreatmentRecord(content) {
  const {
    corpId,
    name,
    mobile,
    page = 1,
    pageSize = 100,
    teamId,
    restUsageCount,
    treatmentOrderId,
    treatmentTimeDates,
    reateTreatementTimeDates,
    billTimeDates,
    deductStatus,
    creators,
    projectIds,
    treatmentDoctorUserIds,
    treatmentStatus,
    treatmentDept_ids,
    customerId,
    packageIds,
    haveValid,
  } = content;

  if (!corpId) return { success: false, message: "机构id不能为空" };
  const matchConditions = { corpId };
  const memberMatchConditions = {};
  if (haveValid) {
    matchConditions.$or = [
      { validTime: { $exists: false } },
      { validTime: { $gte: dayjs().endOf("day").valueOf() } },
      { validTime: null },
    ];
  }
  if (name) memberMatchConditions["customerInfo.name"] = new RegExp(name, "i"); // 使用正则表达式进行模糊查询
  if (mobile) {
    memberMatchConditions["$or"] = [
      { "customerInfo.mobile": mobile },
      { "customerInfo.phone1": mobile },
      { "customerInfo.phone2": mobile },
      { "customerInfo.phone3": mobile },
    ];
  }
  if (teamId) matchConditions.belongTeamId = teamId;
  if (treatmentOrderId) matchConditions.treatmentOrderId = treatmentOrderId;
  if (restUsageCount)
    matchConditions.restUsageCount = { $gte: 0, $exists: true };
  if (deductStatus) matchConditions.deductStatus = deductStatus;
  if (
    treatmentTimeDates &&
    Array.isArray(treatmentTimeDates) &&
    treatmentTimeDates.length === 2
  ) {
    matchConditions.treatmentTime = {
      $gte: dayjs(treatmentTimeDates[0]).startOf("day").valueOf(),
      $lte: dayjs(treatmentTimeDates[1]).endOf("day").valueOf(),
    };
  }
  if (
    billTimeDates &&
    Array.isArray(billTimeDates) &&
    billTimeDates.length === 2
  ) {
    matchConditions.billTime = {
      $gte: dayjs(billTimeDates[0]).startOf("day").valueOf(),
      $lte: dayjs(billTimeDates[1]).endOf("day").valueOf(),
    };
  }
  if (
    reateTreatementTimeDates &&
    Array.isArray(reateTreatementTimeDates) &&
    reateTreatementTimeDates.length === 2
  ) {
    matchConditions.createTime = {
      $gte: dayjs(reateTreatementTimeDates[0]).startOf("day").valueOf(),
      $lte: dayjs(reateTreatementTimeDates[1]).endOf("day").valueOf(),
    };
  }
  if (Array.isArray(projectIds) && projectIds.length > 0) {
    matchConditions.projectId = { $in: projectIds };
  }
  if (Array.isArray(packageIds) && packageIds.length > 0) {
    matchConditions.packageId = { $in: packageIds };
  }
  if (creators && Array.isArray(creators) && creators.length > 0) {
    matchConditions.creator = { $in: creators };
  }
  if (
    treatmentDoctorUserIds &&
    Array.isArray(treatmentDoctorUserIds) &&
    treatmentDoctorUserIds.length > 0
  ) {
    matchConditions.treatmentDoctorUserId = { $in: treatmentDoctorUserIds };
  }
  if (Array.isArray(treatmentDept_ids)) {
    matchConditions.treatmentDept_id = { $in: treatmentDept_ids };
  }
  if (treatmentStatus && Array.isArray(treatmentStatus)) {
    matchConditions.treatmentStatus = { $in: treatmentStatus };
  }
  if (customerId) matchConditions.customerId = customerId;

  try {
    const collection = db.collection("treatment-record");
    const totalResult = await collection
      .aggregate([
        { $match: matchConditions },
        {
          $lookup: {
            from: "member",
            localField: "customerId",
            foreignField: "_id",
            as: "customerInfo",
          },
        },
        { $unwind: "$customerInfo" },
        { $match: memberMatchConditions },
        { $count: "totalCount" },
      ])
      .toArray();
    const total = totalResult.length > 0 ? totalResult[0].totalCount : 0;
    const pages = Math.ceil(total / pageSize);
    const result = await collection
      .aggregate([
        { $match: matchConditions },
        { $sort: { createTime: -1 } },
        {
          $lookup: {
            from: "member",
            localField: "customerId",
            foreignField: "_id",
            as: "customerInfo",
          },
        },
        { $unwind: "$customerInfo" },
        { $match: memberMatchConditions },
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
      ])
      .toArray();
    return {
      success: true,
      list: result,
      total,
      pages,
      message: "查询成功",
      matchConditions,
    };
  } catch (error) {
    return { success: false, message: "查询失败", error: error.message };
  }
}

async function addTreatmentRecord(content) {
  const { corpId, params } = content;
  if (!corpId) {
    return { success: false, message: "机构id不能为空" };
  }
  try {
    let { id } = await db.collection("treatment-record").insertOne({
      _id: common.generateRandomString(24),
      ...params,
      corpId,
      createTime: dayjs().valueOf(),
      deductStatus: "pending",
    });
    return {
      success: true,
      message: "新增成功",
      data: id,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
}

async function updateTreatmentRecord(content) {
  const { corpId, id, params } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!id) return { success: false, message: "治疗记录id不能为空" };
  try {
    await db
      .collection("treatment-record")
      .updateOne({ corpId, _id: id }, { $set: { ...params } });

    return { success: true, message: "更新成功" };
  } catch (error) {
    return { success: false, message: "更新失败" };
  }
}

// 新增划扣记录
async function addDeductRecord(content) {
  const { corpId, params } = content;
  if (!corpId) {
    return { success: false, message: "机构id不能为空" };
  }
  try {
    let { id } = await db.collection("deduct-record").insertOne({
      _id: common.generateRandomString(24),
      ...params,
      corpId,
      createTime: dayjs().valueOf(),
    });
    // 更新客户信息到当前团队 teamId 是一个数组 把belongTeamId 加入若存在则不加入
    const { customerId, belongTeamId } = params;
    if (customerId && belongTeamId) {
      await db.collection("member").updateOne(
        { _id: customerId },
        {
          $addToSet: { teamId: belongTeamId },
        }
      );
    }
    return {
      success: true,
      message: "新增成功",
      data: id,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
}

async function updateDeductRecord(content) {
  const { corpId, id, params, operateType, billId } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!id) return { success: false, message: "治疗记录id不能为空" };
  try {
    await db
      .collection("deduct-record")
      .updateOne({ corpId, _id: id }, { $set: { ...params } });
    if (operateType === "deduct") {
      const { deductUsageCount = 0 } = params;
      await billdRecord.main({
        type: "deductPorjectUsageCount",
        billId,
        corpId,
        deductUsageCount,
      });
    }
    return { success: true, message: "更新成功" };
  } catch (error) {
    return { success: false, message: error.message || "更新失败" };
  }
}
async function getDeductRecord(content) {
  const {
    corpId,
    name,
    mobile,
    page = 1,
    pageSize = 100,
    teamId,
    restUsageCount,
    treatmentOrderId,
    treatmentTimeDates,
    reateTreatementTimeDates,
    billTimeDates,
    deductStatus,
    creators,
    projectIds,
    treatmentDoctorUserIds,
    treatmentDept_ids,
    customerId,
    packageIds,
    treatmentId,
    haveValid,
  } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  let matchConditions = { corpId };
  let memberMatchConditions = {};

  if (haveValid) {
    matchConditions.validTime = {
      $or: [{ $exists: false }, { $gte: dayjs().endOf("day").valueOf() }],
    };
  }
  if (name) memberMatchConditions["customerInfo.name"] = new RegExp(name, "i");
  if (mobile) {
    memberMatchConditions["$or"] = [
      { "customerInfo.mobile": mobile },
      { "customerInfo.phone1": mobile },
      { "customerInfo.phone2": mobile },
      { "customerInfo.phone3": mobile },
    ];
  }
  if (teamId) matchConditions.belongTeamId = teamId;
  if (treatmentOrderId) matchConditions.treatmentOrderId = treatmentOrderId;
  if (treatmentId) matchConditions.treatmentId = treatmentId;
  if (restUsageCount)
    matchConditions.restUsageCount = { $gte: 0, $exists: true };
  if (treatmentTimeDates && treatmentTimeDates.length === 2) {
    matchConditions.treatmentTime = {
      $gte: dayjs(treatmentTimeDates[0]).valueOf(),
      $lte: dayjs(treatmentTimeDates[1]).valueOf(),
    };
  }
  if (billTimeDates && billTimeDates.length === 2) {
    matchConditions.billTime = {
      $gte: dayjs(billTimeDates[0]).valueOf(),
      $lte: dayjs(billTimeDates[1]).valueOf(),
    };
  }
  if (reateTreatementTimeDates && reateTreatementTimeDates.length === 2) {
    matchConditions.createTime = {
      $gte: dayjs(reateTreatementTimeDates[0]).startOf("day").valueOf(),
      $lte: dayjs(reateTreatementTimeDates[1]).endOf("day").valueOf(),
    };
  }
  if (projectIds && projectIds.length > 0) {
    matchConditions.projectId = { $in: projectIds };
  }
  if (packageIds && packageIds.length > 0) {
    matchConditions.packageId = { $in: packageIds };
  }
  if (creators && creators.length > 0) {
    matchConditions.creator = { $in: creators };
  }
  if (treatmentDoctorUserIds && treatmentDoctorUserIds.length > 0) {
    matchConditions.treatmentDoctorUserId = { $in: treatmentDoctorUserIds };
  }
  if (Array.isArray(treatmentDept_ids)) {
    matchConditions.treatmentDept_id = { $in: treatmentDept_ids };
  }
  if (deductStatus && deductStatus.length > 0) {
    matchConditions.deductStatus = { $in: deductStatus };
  }
  if (customerId) matchConditions.customerId = customerId;

  try {
    const totalResult = await db
      .collection("deduct-record")
      .aggregate([
        { $match: matchConditions },
        {
          $lookup: {
            from: "member",
            localField: "customerId",
            foreignField: "_id",
            as: "customerInfo",
          },
        },
        { $unwind: "$customerInfo" },
        { $match: memberMatchConditions },
        { $count: "totalCount" },
      ])
      .toArray();

    const result = await db
      .collection("deduct-record")
      .aggregate([
        { $match: matchConditions },
        { $sort: { createTime: -1 } },
        {
          $lookup: {
            from: "member",
            localField: "customerId",
            foreignField: "_id",
            as: "customerInfo",
          },
        },
        { $unwind: "$customerInfo" },
        { $match: memberMatchConditions },
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
      ])
      .toArray();

    const list = result;
    const total = totalResult.length > 0 ? totalResult[0].totalCount : 0;
    const pages = Math.ceil(total / pageSize);

    return { success: true, list, total, pages, message: "查询成功" };
  } catch (error) {
    return { success: false, message: "查询失败", error: error.message };
  }
}

async function removeTreatmentRecord(content) {
  const { corpId, id } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!id) return { success: false, message: "治疗记录id不能为空" };
  try {
    await db.collection("treatment-record").deleteOne({ corpId, _id: id });
    return { success: true, message: "删除成功" };
  } catch (e) {
    return { success: false, message: "删除失败" };
  }
}

async function removeDeductRecord(content) {
  const { corpId, id } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!id) return { success: false, message: "治疗记录id不能为空" };
  try {
    await db.collection("deduct-record").deleteOne({ corpId, _id: id });
    return { success: true, message: "删除成功" };
  } catch (e) {
    return { success: false, message: "删除失败" };
  }
}

async function treatmentRecordStatistic(content) {
  const { corpId, params = {} } = content;
  const { dates, treatmentDoctorUserIds, treatmentDept_ids, teamId } = params;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  let query = { corpId };
  if (dates && dates.length === 2) {
    query.treatmentTime = {
      $gte: dayjs(dates[0]).startOf("day").valueOf(),
      $lt: dayjs(dates[1]).endOf("day").valueOf(),
    };
  }
  if (treatmentDoctorUserIds && treatmentDoctorUserIds.length > 0) {
    query.treatmentDoctorUserId = { $in: treatmentDoctorUserIds };
  }
  if (Array.isArray(treatmentDept_ids)) {
    query.treatmentDept_id = { $in: treatmentDept_ids };
  }
  if (teamId) query.belongTeamId = teamId;
  try {
    const result = await db
      .collection("treatment-record")
      .aggregate([
        { $match: query },
        {
          $lookup: {
            from: "member",
            localField: "customerId",
            foreignField: "_id",
            as: "customerInfo",
          },
        },
        { $unwind: "$customerInfo" },
        {
          $group: {
            _id: null,
            treatedCount: { $addToSet: "$treatmentOrderId" },
            treatedProjectCount: { $sum: 1 },
            totalDiscountedPrice: { $sum: "$deductedPrice" },
          },
        },
        {
          $project: {
            treatedCount: { $size: "$treatedCount" },
            treatedProjectCount: 1,
            totalDiscountedPrice: 1,
          },
        },
      ])
      .toArray();
    const data =
      result.length > 0
        ? result[0]
        : { treatedCount: 0, treatedProjectCount: 0, totalDiscountedPrice: 0 };
    return { success: true, data, message: "统计成功" };
  } catch (error) {
    return { success: false, message: "统计失败", error: error.message };
  }
}

async function deductRecordStatistic(content) {
  const { corpId, params = {} } = content;
  const { dates, treatmentDoctorUserIds, treatmentDept_ids, teamId } = params;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  let query = { corpId };
  if (dates && dates.length === 2) {
    query.createTime = {
      $gte: dayjs(dates[0]).startOf("day").valueOf(),
      $lt: dayjs(dates[1]).endOf("day").valueOf(),
    };
  }
  if (treatmentDoctorUserIds && treatmentDoctorUserIds.length > 0) {
    query.treatmentDoctorUserId = { $in: treatmentDoctorUserIds };
  }
  if (treatmentDept_ids && treatmentDept_ids.length > 0) {
    query.treatmentDept_id = { $in: treatmentDept_ids };
  }
  if (teamId) query.belongTeamId = teamId;
  try {
    const result = await db
      .collection("deduct-record")
      .aggregate([
        { $match: query },
        {
          $lookup: {
            from: "member",
            localField: "customerId",
            foreignField: "_id",
            as: "customerInfo",
          },
        },
        { $unwind: "$customerInfo" },
        {
          $group: {
            _id: null,
            deductCount: { $addToSet: "$treatmentOrderId" },
            deductProjectCount: { $sum: 1 },
            totalDiscountedPrice: { $sum: "$deductedPrice" },
          },
        },
        {
          $project: {
            deductCount: { $size: "$deductCount" },
            deductProjectCount: 1,
            totalDiscountedPrice: 1,
          },
        },
      ])
      .toArray();
    const data =
      result.length > 0
        ? result[0]
        : { deductCount: 0, deductProjectCount: 0, totalDiscountedPrice: 0 };
    return { success: true, data, message: "统计成功" };
  } catch (error) {
    return { success: false, message: "统计失败", error: error.message };
  }
}

async function treatmentRecordStatisticByStatus(content) {
  const { corpId, params = {} } = content;
  const { dates, treatmentDoctorUserIds, treatmentDept_ids, teamId } = params;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  let query = { corpId };
  if (dates && dates.length === 2) {
    query.billTime = {
      $gte: dayjs(dates[0]).startOf("day").valueOf(),
      $lt: dayjs(dates[1]).endOf("day").valueOf(),
    };
  }
  if (treatmentDoctorUserIds && treatmentDoctorUserIds.length > 0) {
    query.treatmentDoctorUserId = { $in: treatmentDoctorUserIds };
  }
  if (treatmentDept_ids && treatmentDept_ids.length > 0) {
    query.treatmentDept_id = { $in: treatmentDept_ids };
  }
  if (teamId) query.belongTeamId = teamId;
  try {
    const result = await db
      .collection("treatment-record")
      .aggregate([
        { $match: query },
        {
          $lookup: {
            from: "member",
            localField: "customerId",
            foreignField: "_id",
            as: "customerInfo",
          },
        },
        { $unwind: "$customerInfo" },
        {
          $group: {
            _id: "$treatmentStatus",
            count: { $sum: 1 },
          },
        },
      ])
      .toArray();
    const data = result.reduce(
      (acc, item) => {
        acc[item._id] = item.count || 0;
        acc.totalCount += item.count || 0;
        return acc;
      },
      { totalCount: 0 }
    );
    return { success: true, data, message: "统计成功" };
  } catch (error) {
    return { success: false, message: "统计失败", error: error.message };
  }
}

/**
 * 根据customerId查询yizhipai-custom-photos照片记录
 * @param {string} corpId 机构id
 * @param {string} customerId 客户id
 * @param {number} page 页码
 * @param {number} pageSize 每页数量
 * @returns
 */
async function getYizhipaiCustomPhotos(content) {
  const { customerId } = content;

  if (!customerId) return { success: false, message: "客户id不能为空" };
  const matchConditions = { customerId };

  try {
    const collection = db.collection("yizhipai-custom-photos");

    // 获取分页数据
    const result = await collection
      .aggregate([{ $match: matchConditions }])
      .toArray();

    let arr = result.map((i) => {
      const extraInfos = i.extraInfos && JSON.parse(i.extraInfos);
      const files = Array.isArray(i.files)
        ? i.files.map((i) => i.path).filter((i) => i)
        : [];
      return {
        files,
        timeInterval: extraInfos,
      };
    });
    return {
      success: true,
      list: arr,
      message: "查询成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "查询失败",
      error: error.message,
    };
  }
}

const dayjs = require("dayjs");
const common = require("../../common");
let db = null;
// 自定义默认密码
const defaultPassword = process.env.CONFIG_DEFALUT_PASSWORD;
exports.main = async (event, mongodb) => {
  db = mongodb;
  switch (event.type) {
    case "updatePassword":
      return await exports.updatePassword(event);
    case "login":
      return await exports.login(event);
  }
};
// 修改密码
exports.updatePassword = async (event) => {
  const { corpId, username, oldPassword, newPassword } = event;
  try {
    // 查询员工账户里的account是否存在
    const user = await db.collection("corp-member").findOne(
      { corpId, mobile: username } // 查询条件
    );
    // 如果不存在,则提示账户不存在
    if (!user) {
      return {
        success: false,
        message: "账户不存在",
      };
    }
    const { password = "" } = user || {};
    if (
      (password && password !== oldPassword) ||
      (!password && oldPassword !== defaultPassword)
    ) {
      return {
        success: false,
        message: "原密码错误",
      };
    }
    // 更新密码
    await db.collection("corp-member").updateOne(
      { corpId, mobile: username }, // 查询条件
      { $set: { password: newPassword, updateTime: Date.now() } } // 更新内容
    );
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};
// 登陆账号
exports.login = async (event) => {
  const { corpId, username, password } = event;
  try {
    // 查询员工账户里的account是否存在
    const user = await db.collection("corp-member").findOne(
      { corpId, mobile: username } // 查询条件
    );
    // 如果不存在,则提示账户不存在
    if (!user) {
      return {
        success: false,
        message: "账户不存在或者密码不正确",
      };
    }
    const { password: dbPassword = "" } = user || {};
    if (!dbPassword) {
      if (password === defaultPassword) {
        return {
          success: false,
          message: "密码未设置",
        };
      } else {
        return {
          success: false,
          message: "默认密码不正确",
        };
      }
    }
    if (password !== dbPassword) {
      return {
        success: false,
        message: "账户不存在或者密码不正确",
      };
    }
    return {
      success: true,
      user,
      message: "登录成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

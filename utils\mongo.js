const utils = require("./utils");
const dayjs = require("dayjs");
const common = require("../common");

exports.find = (db, dbName, query = {}, options = {}) => {
  if (!db || !dbName) return [];
  if (!options.skip) options.skip = 0;
  if (!options.limit) options.limit = 1000;
  return db.collection(dbName).find(query, options).toArray();
};
exports.insertOne = (db, dbName, params = {}) => {
  if (!db || !dbName) return;
  if (!params._id) params._id = common.generateRandomString(24);
  if (!params.createTime) params.createTime = dayjs().valueOf();
  return db.collection(dbName).insertOne(params);
};
exports.insertMany = (db, dbName, params = {}) => {
  if (!db || !dbName) return;
  if (!params._id) params._id = common.generateRandomString(24);
  if (!params.createTime) params.createTime = dayjs().valueOf();
  return db.collection(dbName).insertMany(params);
};
exports.updateOne = (db, dbName, query = {}, params = {}) => {
  if (!db || !dbName) return;
  if (!params.updateTime) params.updateTime = dayjs().valueOf();
  return db.collection(dbName).updateOne(query, {
    $set: params,
  });
};
exports.updateMany = (
  db,
  dbName,
  query = {},
  params = {},
  deteleParams = {}
) => {
  if (!db || !dbName) return;
  if (!params.updateTime) params.updateTime = dayjs().valueOf();
  return db.collection(dbName).updateMany(query, {
    $set: params,
    $unset: deteleParams,
  });
};
exports.countDocuments = (db, dbName, query = {}) => {
  if (!db || !dbName) return;
  return db.collection(dbName).countDocuments(query);
};
exports.deleteMany = (db, dbName, query = {}) => {
  if (!db || !dbName) return;
  return db.collection(dbName).deleteMany(query);
};
exports.deleteOne = (db, dbName, query = {}) => {
  if (!db || !dbName) return;
  return db.collection(dbName).deleteOne(query);
};
// 聚合查询
exports.aggregate = (db, dbName, query = {}) => {
  if (!db || !dbName) return;
  return db.collection(dbName).aggregate(query).toArray();
};

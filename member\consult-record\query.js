const utils = require("../utils");
const dayjs = require("dayjs");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getConsultRecordCount":
      return await getConsultRecordCount(content);
    case "getConsultStageCount":
      return await getConsultStageCount(content);
  }
};

// 获取咨询总数
async function getConsultRecordCount(content) {
  let {
    corpId,
    params = {},
    triagePersonUserIds,
    receptionPersonUserIds,
    teamId,
    createDates,
  } = content;

  // 处理分诊人员ID
  if (triagePersonUserIds && Array.isArray(triagePersonUserIds) && triagePersonUserIds.length > 0) {
    params["triagePersonUserId"] = { $in: triagePersonUserIds };
  }

  // 处理接待人员ID
  if (receptionPersonUserIds && Array.isArray(receptionPersonUserIds) && receptionPersonUserIds.length > 0) {
    params["receptionPersonUserId"] = { $in: receptionPersonUserIds };
  }

  // 处理创建日期
  if (createDates && Array.isArray(createDates) && createDates.length) {
    params["createTime"] = {
      $gte: dayjs(createDates[0]).startOf("day").valueOf(),
      $lte: dayjs(createDates[1]).endOf("day").valueOf(),
    };
  }

  if (!corpId) return { success: false, message: "机构id不能为空" };

  let query = { corpId, ...params };
  if (teamId) query.teamId = teamId;

  // 查询总交易金额和交易数量
  const totalTrade = await db.collection("consult-record").aggregate([
    { $match: { ...query, tradeAmount: { $exists: true } } },
    {
      $group: {
        _id: null,
        totalTradeAmount: { $sum: "$tradeAmount" },
        tradeCount: { $sum: 1 },
      },
    },
  ]).toArray();

  // 查询总记录数
  const totalResult = await db.collection("consult-record").aggregate([
    { $match: query },
    {
      $lookup: {
        from: "member",
        localField: "customerId",
        foreignField: "_id",
        as: "customerInfo",
      },
    },
    { $unwind: "$customerInfo" },
    { $count: "totalCount" },
  ]).toArray();

  const total = totalResult.length > 0 ? totalResult[0].totalCount : 0;
  const totalTradeAmount = totalTrade.length > 0 ? totalTrade[0].totalTradeAmount : 0;
  const tradeCount = totalTrade.length > 0 ? totalTrade[0].tradeCount : 0;

  return {
    success: true,
    message: "查询成功",
    count: total,
    totalTradeAmount,
    tradeCount,
  };
}

// 获取咨询阶段统计
async function getConsultStageCount(content) {
  const { 
    corpId, 
    createDates, 
    receptionPersonUserIds, 
    teamId, 
    visitStatus,
    // 新增筛选条件，参考 getConsultRecord
    name,
    mobile,
    triageTimeDates,
    reportPeoples,
    triagePersonUserIds,
    consumeStatus,
    consultStages,
    customerId,
    projectIds,
    tradeStatus,
    receptionDates,
    source,
    customerSource,
    counselors,
    introducers,
    interviewDoctors
  } = content;
  
  if (!corpId) return { success: false, message: "机构id不能为空" };

  let matchConditions = { corpId };

  // 项目筛选
  if (Array.isArray(projectIds) && projectIds.length) {
    matchConditions["projectIds"] = { $in: projectIds };
  }
  
  // 接诊医生筛选
  if (Array.isArray(interviewDoctors)) {
    matchConditions["interviewDoctor"] = { $in: interviewDoctors };
  }

  // 分诊时间筛选
  if (
    triageTimeDates &&
    Array.isArray(triageTimeDates) &&
    triageTimeDates.length === 2
  ) {
    matchConditions.triageTime = {
      $gte: dayjs(triageTimeDates[0]).startOf("day").valueOf(),
      $lte: dayjs(triageTimeDates[1]).endOf("day").valueOf(),
    };
  }

  // 来源筛选
  if (source && Array.isArray(source) && source.length > 0) {
    matchConditions.source = { $in: source };
  }

  // 接待时间筛选
  if (
    receptionDates &&
    Array.isArray(receptionDates) &&
    receptionDates.length === 2
  ) {
    matchConditions.receptionTime = {
      $gte: dayjs(receptionDates[0]).startOf("day").valueOf(),
      $lte: dayjs(receptionDates[1]).endOf("day").valueOf(),
    };
  }

  // 介绍人筛选
  if (introducers && Array.isArray(introducers) && introducers.length > 0) {
    matchConditions.introducerUserId = { $in: introducers };
  }

  // 咨询师筛选
  if (counselors && Array.isArray(counselors) && counselors.length > 0) {
    matchConditions.counselorUserId = { $in: counselors };
  }

  // 接待人员筛选
  if (
    receptionPersonUserIds &&
    Array.isArray(receptionPersonUserIds) &&
    receptionPersonUserIds.length > 0
  ) {
    matchConditions.receptionPersonUserId = { $in: receptionPersonUserIds };
  }

  // 分诊人员筛选
  if (
    triagePersonUserIds &&
    Array.isArray(triagePersonUserIds) &&
    triagePersonUserIds.length > 0
  ) {
    matchConditions.triagePersonUserId = { $in: triagePersonUserIds };
  }

  // 就诊状态筛选
  if (visitStatus && Array.isArray(visitStatus) && visitStatus.length > 0) {
    matchConditions.visitStatus = { $in: visitStatus };
  }

  // 消费状态筛选
  if (
    consumeStatus &&
    Array.isArray(consumeStatus) &&
    consumeStatus.length > 0
  ) {
    let consumeCountQuery = [];
    if (consumeStatus.includes("notConsumed")) consumeCountQuery.push(0);
    if (consumeStatus.includes("consumed")) consumeCountQuery.push(1);
    if (consumeStatus.includes("moreConsumed"))
      consumeCountQuery.push({ $gt: 1 });
    matchConditions.consumeCount = { $in: consumeCountQuery };
  }

  // 咨询阶段筛选
  if (
    consultStages &&
    Array.isArray(consultStages) &&
    consultStages.length > 0
  ) {
    matchConditions.consultStage = { $in: consultStages };
  }

  // 客户ID筛选
  if (customerId) matchConditions.customerId = customerId;
  
  // 团队筛选
  if (teamId) matchConditions.teamId = teamId;
  
  // 交易状态筛选
  if (tradeStatus === "traded") matchConditions.tradeAmount = { $ne: 0 };
  if (tradeStatus === "untraded") matchConditions.tradeAmount = { $eq: 0 };

  // 创建时间筛选（兼容原有参数名）
  if (createDates && Array.isArray(createDates) && createDates.length) {
    matchConditions.createTime = {
      $gte: dayjs(createDates[0]).startOf("day").valueOf(),
      $lte: dayjs(createDates[1]).endOf("day").valueOf(),
    };
  }

  // 客户信息筛选条件
  let memberMatchConditions = {};
  if (name) memberMatchConditions["customerInfo.name"] = new RegExp(name, "i");
  if (mobile) {
    memberMatchConditions["$or"] = [
      { "customerInfo.mobile": mobile },
      { "customerInfo.phone1": mobile },
      { "customerInfo.phone2": mobile },
      { "customerInfo.phone3": mobile },
    ];
  }

  // 客户来源筛选
  if (Array.isArray(customerSource) && customerSource.length) {
    memberMatchConditions["customerInfo.customerSource"] = {
      $in: customerSource,
    };
  }

  // 报备人筛选
  if (
    reportPeoples &&
    Array.isArray(reportPeoples) &&
    reportPeoples.length > 0
  ) {
    memberMatchConditions["customerInfo.addMethod"] = "eStoreReport";
    memberMatchConditions["customerInfo.creator"] = { $in: reportPeoples };
  }

  const data = await db.collection("consult-record").aggregate([
    { $match: matchConditions },
    {
      $lookup: {
        from: "member",
        localField: "customerId",
        foreignField: "_id",
        as: "customerInfo",
      },
    },
    { $unwind: "$customerInfo" },
    { $match: memberMatchConditions },
    {
      $group: {
        _id: "$consultStage",
        count: { $sum: 1 },
        tradeCount: {
          $sum: {
            $cond: [{ $ne: ["$tradeAmount", 0] }, 1, 0],
          },
        },
        visitedCount: {
          $sum: {
            $cond: [{ $eq: ["$visitStatus", "visited"] }, 1, 0],
          },
        },
        totalTradeAmount: { $sum: "$tradeAmount" },
      },
    },
  ]).toArray();

  const result = data.reduce((acc, item) => {
    acc[item._id] = {
      count: item.count,
      tradeCount: item.tradeCount,
      totalTradeAmount: item.totalTradeAmount,
      visitedCount: item.visitedCount,
    };
    acc.totals = acc.totals || {
      count: 0,
      tradeCount: 0,
      totalTradeAmount: 0,
      visitedCount: 0,
    };
    acc.totals.count += item.count;
    acc.totals.tradeCount += item.tradeCount;
    acc.totals.totalTradeAmount += item.totalTradeAmount;
    acc.totals.visitedCount += item.visitedCount;
    return acc;
  }, {});

  return {
    success: true,
    data: result,
    message: "获取成功",
  };
}

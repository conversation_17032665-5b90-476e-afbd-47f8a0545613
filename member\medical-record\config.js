const RecordField = {
  outpatient: [
    { label: '创建人', prop: 'creator', required: true, placeholder: '创建人不能为空', ignore: 'checkIn' },
    { label: '就诊机构', prop: 'corp', required: true, type: 'select', placeholder: '就诊机构不能为空' },
    { label: '就诊科室', prop: 'deptName', type: 'input', placeholder: '就诊科室不能为空', showProp: 'corp', showValue: 'current corp' },
    { label: '就诊日期', prop: 'visitTime', required: true, type: 'date', placeholder: '就诊日期不能为空' },
    { label: '责任医生', prop: 'doctor', placeholder: '责任医生不能为空', showProp: 'corp', showValue: 'current corp' },
    { label: '门诊诊断', prop: 'diagnosisName', required: true, type: 'remoteSelect', placeholder: '门诊诊断不能为空', ignore: 'checkIn' },
    { label: '服务项目', prop: 'serviceItems', type: 'select' },
    { label: '费用金额', prop: 'amount' },
    { label: '处置计划', prop: 'disposalPlan', maxlength: 200 },
    { label: '诊后治疗方案', prop: 'treatmentPlan', maxlength: 200 },
    { label: '康复措施指导', prop: 'recoveryGuide', maxlength: 200 },
    { label: '上传文件', prop: 'files' }
  ],
  inhospital: [
    { label: '创建人', prop: 'creator', required: true, placeholder: '创建人不能为空' },
    { label: '就诊机构', prop: 'corp', required: true, type: 'select', placeholder: '就诊机构不能为空' },
    { label: '入院科室', prop: 'deptName', showProp: 'corp', showValue: 'current corp' },
    { label: '责任医生', prop: 'doctor', showProp: 'corp', showValue: 'current corp' },
    { label: '病区床位', prop: 'bedNo', showProp: 'corp', showValue: 'current corp' },
    { label: '入院时间', prop: 'inhosDate', required: true, type: 'date', placeholder: '入院时间不能为空' },
    { label: '入院诊断', prop: 'diagnosisName', required: true, type: 'remoteSelect', placeholder: '入院诊断不能为空' },
    { label: '手术名称', prop: 'operation', required: true, placeholder: '手术名称不能为空' },
    { label: '手术日期', prop: 'operationDate', required: true, type: 'date', placeholder: '手术日期不能为空' },
    { label: '出院日期', prop: 'outhosDate', required: true, type: 'date', placeholder: '出院日期不能为空' },
    { label: '费用金额', prop: 'amount' },
    { label: '院后治疗方案', prop: 'treatmentPlan', maxlength: 200 },
    { label: '康复措施指导', prop: 'recoveryGuide', maxlength: 200 },
    { label: '上传文件', prop: 'files' }
  ]
}

exports.RecordField = RecordField;
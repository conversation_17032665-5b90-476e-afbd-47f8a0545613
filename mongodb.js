const { MongoClient } = require("mongodb");

// 震元堂环境 // 注意后面的配置, 根据实际情况修改
const username = process.env.CONFIG_DB_USERNAME;
const password = process.env.CONFIG_DB_PASSWORD;
const host = process.env.CONFIG_DB_HOST; // 例如：'localhost' 或 MongoDB Atlas提供的连接地址
const port = process.env.CONFIG_DB_PORT || "27017"; // MongoDB默认端口为'27017'

// 构建连接字符串，包含身份验证信息
const url = `mongodb://${username}:${password}@${host}:${port}/admin`;
console.log(`mongoDB url: ${url}`);
const options = {
  maxPoolSize: 100, // 设置最大连接池大小为 100
  minPoolSize: 10, // 设置最小连接池大小为 10
  connectTimeoutMS: 30000, // 连接超时 30 秒
  socketTimeoutMS: 45000, // 数据传输超时 45 秒
  useUnifiedTopology: true, // 启用新的连接管理机制
};
let client;
async function connectToMongoDB() {
  try {
    console.log("连接 MongoDB");
    if (!client) {
      client = new MongoClient(url, options);
      await client.connect();
      console.log("MongoDB 连接成功");
    }
    return client;
  } catch (err) {
    console.error(err);
  }
}
async function getDatabase(dbName) {
  return client.db(dbName);
}
async function closeMongoDB() {
  if (client) {
    await client.close();
    client = null;
  }
}
// exports.connectToMongoDB = async (item, type, dbName) => {
//   try {
//     const db = await getDatabase(dbName);
//     // 获取 serverStatus 的连接数信息
//     const res = await type.main(item, db);
//     return res;
//   } catch (err) {
//     // 细化错误信息
//     return {
//       success: false,
//       message: err.message,
//     };
//   }
// };

process.on("SIGINT", async () => {
  console.log("关闭 MongoDB 连接");
  await closeMongoDB();
  process.exit(0);
});

module.exports = { connectToMongoDB, getDatabase, closeMongoDB };

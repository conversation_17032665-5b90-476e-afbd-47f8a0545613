const hzzx = require("./hzzx");
const request = require("./request");
exports.main = async (event) => {
  const { type, corpId } = event;
  if (corpId === "wpLgjyawAAJ69XjD39GMOXp2EsYHYb3w") {
    //杭州整形
    switch (type) {
      case "getHisCustomerArchive":
        return await hzzx.getHzzxCustomerArchive(event);
      case "getHisInHospitalRecord":
        return await hzzx.getHzzxInHospitalRecord(event);
      case "getHisOutHospitalRecord":
        return await hzzx.getHzzxOutHospitalRecord(event);
      case "getHisFeeRecord":
        return await hzzx.getHzzxFeeRecord(event);
      case "getHzzxPackageRecord":
        return await hzzx.getHzzxPackageRecord(event);
      default:
        return {
          success: false,
          message: "未找到对应的方法",
        };
    }
  } else if (corpId === "wwe3fb2faa52cf9dfb") {
    const url = "https://ykt.youcan365.com/hz/getYoucanData/customerHisSync";
    event.corpId = "wpLgjyawAAJ69XjD39GMOXp2EsYHYb3w";
    const res = await request.main(url, event, "POST");
    return res;
  } else {
    return {
      success: false,
      message: "未找到对应的机构",
    };
  }
};

/**
 * 分页查询优化辅助模块
 * 用于处理大数据量的分页查询性能问题
 */
const dayjs = require("dayjs");

/**
 * 构建优化的聚合管道
 */
function buildOptimizedPipeline(matchConditions, memberMatchConditions, createTeamId, page, pageSize) {
  const pipeline = [
    // 第一步：使用索引进行初步筛选
    { 
      $match: {
        ...matchConditions,
        // 添加时间范围限制，避免全表扫描
        createTime: matchConditions.createTime || { 
          $gte: dayjs().subtract(1, 'year').valueOf() // 默认只查询一年内的数据
        }
      }
    },
    
    // 第二步：早期排序（利用索引）
    { $sort: { corpId: 1, createTime: -1 } },
    
    // 第三步：限制处理的文档数量（避免处理过多数据）
    { $limit: (page * pageSize) + pageSize * 5 }, // 多取一些数据以应对筛选
    
    // 第四步：与member集合关联（只获取需要的字段）
    {
      $lookup: {
        from: "member",
        localField: "customerId",
        foreignField: "_id",
        as: "customerInfo",
        pipeline: [
          {
            $project: {
              name: 1,
              mobile: 1,
              phone1: 1,
              phone2: 1,
              phone3: 1,
              customerSource: 1,
              addMethod: 1,
              creator: 1
            }
          }
        ]
      },
    },
    { $unwind: "$customerInfo" },
    
    // 第五步：应用member相关的筛选条件
    ...(Object.keys(memberMatchConditions).length > 0 ? [{ $match: memberMatchConditions }] : []),
    
    // 第六步：使用facet同时计算总数和分页数据
    {
      $facet: {
        totalCount: [{ $count: "count" }],
        data: [
          { $skip: (page - 1) * pageSize },
          { $limit: pageSize },
          // 只对当前页的数据进行to-do-events关联
          {
            $lookup: {
              from: "to-do-events",
              localField: "customerId",
              foreignField: "customerId",
              as: "todo",
              pipeline: [
                {
                  $match: {
                    eventStatus: "untreated",
                    ...(createTeamId && { executeTeamId: createTeamId })
                  }
                },
                { $limit: 1 }, // 只需要知道是否存在
                {
                  $project: {
                    eventStatus: 1,
                    executeTeamId: 1
                  }
                }
              ]
            },
          },
          {
            $addFields: {
              hasPlan: { $gt: [{ $size: "$todo" }, 0] },
            },
          },
          {
            $project: {
              todo: 0, // 移除不需要的字段
              "customerInfo.addMethod": 0,
              "customerInfo.creator": 0
            },
          },
        ]
      }
    }
  ];

  return pipeline;
}

/**
 * 构建轻量级的计数查询管道
 */
function buildCountOnlyPipeline(matchConditions, memberMatchConditions) {
  const pipeline = [
    { $match: matchConditions },
    {
      $lookup: {
        from: "member",
        localField: "customerId",
        foreignField: "_id",
        as: "customerInfo",
        pipeline: [
          {
            $project: { _id: 1 } // 只获取ID，减少数据传输
          }
        ]
      },
    },
    { $unwind: "$customerInfo" },
    ...(Object.keys(memberMatchConditions).length > 0 ? [{ $match: memberMatchConditions }] : []),
    { $count: "totalCount" }
  ];

  return pipeline;
}

/**
 * 基于游标的分页查询（适用于大数据量）
 */
function buildCursorBasedPipeline(matchConditions, memberMatchConditions, createTeamId, cursor, pageSize) {
  const pipeline = [
    // 使用游标进行分页
    { 
      $match: {
        ...matchConditions,
        ...(cursor && { 
          $or: [
            { createTime: { $lt: cursor.createTime } },
            { 
              createTime: cursor.createTime, 
              _id: { $gt: cursor._id } 
            }
          ]
        })
      }
    },
    
    { $sort: { createTime: -1, _id: 1 } },
    { $limit: pageSize + 1 }, // 多取一个用于判断是否有下一页
    
    {
      $lookup: {
        from: "member",
        localField: "customerId",
        foreignField: "_id",
        as: "customerInfo",
        pipeline: [
          {
            $project: {
              name: 1,
              mobile: 1,
              phone1: 1,
              phone2: 1,
              phone3: 1,
              customerSource: 1
            }
          }
        ]
      },
    },
    { $unwind: "$customerInfo" },
    
    ...(Object.keys(memberMatchConditions).length > 0 ? [{ $match: memberMatchConditions }] : []),
    
    {
      $lookup: {
        from: "to-do-events",
        localField: "customerId",
        foreignField: "customerId",
        as: "todo",
        pipeline: [
          {
            $match: {
              eventStatus: "untreated",
              ...(createTeamId && { executeTeamId: createTeamId })
            }
          },
          { $limit: 1 },
          { $project: { eventStatus: 1, executeTeamId: 1 } }
        ]
      },
    },
    
    {
      $addFields: {
        hasPlan: { $gt: [{ $size: "$todo" }, 0] },
      },
    },
    
    {
      $project: {
        todo: 0,
      },
    }
  ];

  return pipeline;
}

/**
 * 判断是否应该使用游标分页
 */
function shouldUseCursorPagination(page, pageSize, estimatedTotal) {
  // 当页数较大或预估总数较大时，使用游标分页
  return (page > 10) || (estimatedTotal > 10000) || (page * pageSize > 1000);
}

/**
 * 优化查询条件
 */
function optimizeMatchConditions(matchConditions) {
  const optimized = { ...matchConditions };
  
  // 如果没有时间筛选，添加默认时间范围
  if (!optimized.createTime && !optimized.triageTime && !optimized.receptionTime) {
    optimized.createTime = { 
      $gte: dayjs().subtract(6, 'months').valueOf() // 默认只查询6个月内的数据
    };
  }
  
  return optimized;
}

/**
 * 估算查询结果数量（用于决定查询策略）
 */
async function estimateResultCount(db, matchConditions) {
  try {
    // 使用 estimatedDocumentCount 或简单的计数查询来快速估算
    const sampleSize = await db.collection("consult-record")
      .countDocuments({
        corpId: matchConditions.corpId,
        createTime: matchConditions.createTime || { 
          $gte: dayjs().subtract(6, 'months').valueOf() 
        }
      });
    
    return sampleSize;
  } catch (error) {
    console.warn('估算查询数量失败:', error.message);
    return 0;
  }
}

module.exports = {
  buildOptimizedPipeline,
  buildCountOnlyPipeline,
  buildCursorBasedPipeline,
  shouldUseCursorPagination,
  optimizeMatchConditions,
  estimateResultCount
}; 
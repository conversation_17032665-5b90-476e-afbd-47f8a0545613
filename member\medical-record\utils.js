const config = require("./config");
const dayjs = require("dayjs");

exports.getParams = function (data, isUpdate = false) {
  const fields = config.RecordField[data.medicalType];
  const params = { medicalType: data.medicalType };
  if (!fields) return "未知类型";
  for (let field of fields) {
    const { prop, required, placeholder, label, type, showValue, showProp, maxlength = 50, ignore } = field;
    if (isUpdate && !(prop in data)) continue;
    if (showProp && showValue != data[showProp]) continue;
    if (ignore && data.ignore === ignore) continue;
    const validValue = ["string", "number"].includes(typeof data[prop]) ? data[prop] : "";
    const value = typeof validValue === "string" ? validValue.trim() : validValue;
    if (required && !value) {
      return placeholder;
    }
    if (value && type === "date" && !dayjs(value).isValid()) {
      return `${label}时间格式不正确`;
    }
    if (prop === "files" && value.length > 2000) {
      return `${label}的值太长`;
    } else if (prop !== "files" && typeof data[prop] === "string" && value.length > maxlength) {
      return `${label}不能超过${maxlength}个字`;
    }
    params[prop] = value;
    if (["remoteSelect", "select"].includes(type)) {
      params[`${prop}Label`] = data[`${prop}Label`] || "";
    }
  }
  return params;
};

exports.generateRandomString = (length = 10) => {
  let result = "";
  let characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

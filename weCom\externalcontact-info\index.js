const request = require("../request.js");
const accessToken = require("../token");
const utils = require("../utils");
const api = require("../../api");
exports.main = async (context) => {
  switch (context.type) {
    case "getMyExternalcontact":
      return await exports.getMyExternalcontact(context);
    case "getNameByexternalUserId":
      return await exports.getNameByexternalUserId(context);
    case "getCorpMemberInfoByUserId":
      return await exports.getCorpMemberInfoByUserId(context);
    case "unionidToExternalUserid":
      return await exports.unionidToExternalUserid(context);
    case "getCorpMemberExternalContact":
      return await exports.getCorpMemberExternalContact(context);
    case "getMyExternalcontactByBatch":
      return await exports.getMyExternalcontactByBatch(context);
    case "batchGetWeChatFriend":
      return await exports.batchGetWeChatFriend(context);
    default: {
      return {
        success: false,
        message: "未找到对应的操作类型",
      };
    }
  }
};
// 获取机构成员的外部联系人
exports.getCorpMemberExternalContact = async (context) => {
  let { token, userId } = context;
  try {
    let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/list?access_token=${token}&userid=${userId}`;
    let res = await request.main(url, null, "GET");
    return {
      success: true,
      message: "获取成功",
      data: res.errcode === 0 ? res.external_userid : [],
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};
/**
 * 批量获取外部联系人详情
 * @param {*} context
 * @returns
 */
exports.getMyExternalcontact = async (context) => {
  // 获取access_token
  let { access_token = "", userId, corpId, permanentCode } = context;
  try {
    if (!access_token) {
      access_token = await accessToken.getToken({
        corpId: corpId,
        permanentCode: permanentCode,
      });
    }
    let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/batch/get_by_user?access_token=${access_token}`;
    let params = {
      userid_list: userId,
      limit: 1000,
      cursor: "", // 初始的 cursor 值为 1
    };
    let allData = [];
    while (true) {
      let res = await request.main(url, params, "POST");
      let arr = res.external_contact_list.map((item) => {
        return {
          ...item.external_contact,
          userid: item.follow_info ? item.follow_info.userid : "",
          createtime: item.follow_info ? item.follow_info.createtime : "",
          addWay: item.follow_info ? item.follow_info.add_way : "",
        };
      });
      allData.push(...arr);
      if (res.next_cursor) {
        params.cursor = res.next_cursor; // 更新 cursor 的值
      } else {
        break; // 如果 next_cursor 为空，表示所有数据都已经获取完毕
      }
    }
    return {
      success: true,
      message: "获取成功",
      data: allData,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

/**
 * 分批获取外部联系人
 */
exports.batchGetWeChatFriend = async () => {
  const corpRes = await api.getCorpApi({
    type: "getCorpList",
  });
  console.log("corpRes", corpRes);
  const corpIds =
    corpRes.data && Array.isArray(corpRes.data)
      ? corpRes.data.map((i) => i.corpId)
      : [];
  console.log("corpIds", corpIds);
  for (let corpId of corpIds) {
    // 获取access_token
    const { data } = await api.getCorpApi({
      corpId,
      accessToken,
      type: "getOpenedAccount",
    });
    const access_token = await accessToken.getToken({
      corpId,
    });
    if (typeof access_token !== "string") return;
    const userIds = data.map((i) => i.userid);
    const taskFn = (userId) => {
      return this.getMyExternalcontactByBatch({ corpId, userId, access_token });
    };
    await utils.executeWithConcurrency(userIds, taskFn);
  }
  return {
    success: true,
    message: "获取成功",
  };
};
exports.getMyExternalcontactByBatch = async (context) => {
  let { corpId, access_token, userId } = context;
  try {
    if (!access_token) {
      access_token = await accessToken.getToken({
        corpId,
      });
    }
    if (typeof access_token !== "string") return;
    let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/batch/get_by_user?access_token=${access_token}`;
    let params = {
      userid_list: [userId],
      limit: 1000,
      cursor: "", // 初始的 cursor 值为 1
    };
    while (true) {
      let res = await request.main(url, params, "POST");
      let arr = res.external_contact_list.map((item) => {
        return {
          ...item.external_contact,
          userid: item.follow_info ? item.follow_info.userid : "",
          createtime: item.follow_info ? item.follow_info.createtime : "",
          addWay: item.follow_info ? item.follow_info.add_way : "",
        };
      });
      await api.getCorpApi({
        type: "batchUpdateWechatFriends",
        corpId,
        data: arr,
      });
      if (res.next_cursor) {
        params.cursor = res.next_cursor; // 更新 cursor 的值
      } else {
        break; // 如果 next_cursor 为空，表示所有数据都已经获取完毕
      }
    }
    return {
      success: true,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

/**
 *  获取外部联系人
 * @param {*} context
 * @returns
 */
exports.getNameByexternalUserId = async (context) => {
  // 获取access_token
  let { corpId, permanentCode, access_token = "", externalUserId } = context;
  try {
    if (!access_token)
      access_token = await accessToken.getToken({
        corpId: corpId,
        permanentCode,
      });
    let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get?access_token=${access_token}&external_userid=${externalUserId}`;
    let res = await request.main(url, null, "GET");
    if (res.errcode === 0) {
      return {
        success: true,
        message: "获取成功",
        data: res,
      };
    } else {
      return {
        success: false,
        message: res.errmsg,
        errcode: res.errcode,
      };
    }
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};
exports.unionidToExternalUserid = async (context) => {
  let { unionid, openid, corpId } = context;
  let { suiteToken } = await api.getCorpApi({
    type: "getSuiteToken",
  });
  let url = `https://qyapi.weixin.qq.com/cgi-bin/service/externalcontact/unionid_to_external_userid_3rd?suite_access_token=${suiteToken}`;
  const params = {
    unionid,
    openid,
  };
  let res = await request.main(url, params, "POST");
  const info =
    res.external_userid_info &&
    Array.isArray(res.external_userid_info) &&
    res.external_userid_info.find((i) => i.corpid === corpId);
  let externalUserId = info ? info.external_userid : "";
  return {
    success: true,
    message: "获取成功",
    data: externalUserId,
  };
};
// 获取成员详情
exports.getCorpMemberInfoByUserId = async (context) => {
  const { corpId, permanentCode, userId } = context.params;
  try {
    let ACCESS_TOKEN = await accessToken.getToken({
      corpId: corpId,
      permanentCode: permanentCode,
    });
    let url = `https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=${ACCESS_TOKEN}&userid=${userId}`;
    let res = await request.main(url, null, "GET");
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

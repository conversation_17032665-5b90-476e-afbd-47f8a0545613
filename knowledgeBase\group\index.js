const dayjs = require("dayjs");
const utils = require("../utils");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "createGroup":
      return await exports.createGroup(content);
    case "getGroups":
      return await exports.getGroups(content);
    case "removeGroup":
      return await exports.removeGroup(content);
    case "updateGroup":
      return await exports.updateGroup(content);
    case "addGroupIdForMember":
      return await exports.addGroupIdForMember(content);
  }
};

exports.createGroup = async (context) => {
  const { corpId, params } = context;
  const {
    groupName,
    managementPlan = {},
    teamId,
    groupType,
    description,
    creator,
  } = params;

  if (!corpId) {
    return {
      success: false,
      message: "机构ID未输入",
    };
  }
  if (!groupName) {
    return {
      success: false,
      message: "分组名称未输入",
    };
  }

  const countQuery = {
    corpId,
    groupName,
  };
  if (groupType === "corp") {
    countQuery.groupType = "corp";
  } else {
    countQuery.teamId = teamId;
  }

  const count = await db.collection("group").countDocuments(countQuery);
  if (count > 0) {
    return {
      success: false,
      message: "分组名称重复!",
    };
  }

  let query = {
    _id: common.generateRandomString(24),
    corpId,
    teamId,
    groupName,
    managementPlan,
    creator,
    description,
    createTime: dayjs().valueOf(),
  };
  if (groupType === "corp") query["groupType"] = "corp";

  try {
    const result = await db.collection("group").insertOne(query);
    if (groupType === "corp")
      await distributeGroupToTeam(result.insertedId, query);
    return {
      success: true,
      data: result.insertedId,
      message: "新增分组成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "新增分组失败",
    };
  }
};

// 分发机构分组到团队
async function distributeGroupToTeam(parentGroupId, group) {
  const { corpId, groupName, managementPlan, description } = group;

  try {
    const teams = await db.collection("team").find({ corpId }).toArray();
    const bulkOps = teams.map((team) => {
      const { teamId } = team;
      const query = {
        _id: common.generateRandomString(24),
        parentGroupId,
        corpId,
        teamId,
        groupName,
        managementPlan,
        description,
        createTime: dayjs().valueOf(),
      };
      return {
        insertOne: {
          document: query,
        },
      };
    });

    if (bulkOps.length > 0) {
      await db.collection("group").bulkWrite(bulkOps);
    }

    return {
      success: true,
      message: "分发成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "分发失败",
    };
  }
}

exports.updateGroup = async (context) => {
  const { id, params, corpId } = context;
  if (!id) {
    return {
      success: false,
      message: "分组id未传",
    };
  }

  const { groupName = "", managementPlan, description } = params;
  let query = {
    $set: {
      updateTime: dayjs().valueOf(),
      groupName,
      description,
      managementPlan: managementPlan,
    },
  };

  try {
    await db.collection("group").updateMany(
      {
        corpId,
        $or: [{ _id: id }, { parentGroupId: id }],
      },
      query
    );
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "更新失败",
    };
  }
};

exports.removeGroup = async (context) => {
  const { corpId, id, groupType, teamId } = context;
  if (!corpId) {
    return {
      success: false,
      message: "机构ID未输入",
    };
  }
  if (!id) {
    return {
      success: false,
      message: "分组ID未输入",
    };
  }

  try {
    let memberCount = 0;
    if (groupType === "corp") {
      const groups = await db
        .collection("group")
        .find({ corpId, parentGroupId: id })
        .toArray();
      const groupIds = groups.map((e) => e._id);
      memberCount = await db
        .collection("member")
        .countDocuments({ corpId, groupIds: { $in: groupIds } });
    } else {
      memberCount = await db
        .collection("member")
        .countDocuments({ corpId, groupIds: id, teamId });
    }

    if (memberCount !== 0) {
      return {
        success: false,
        message: "此分组下还存在客户,请将客户转组后再删除本分组!",
      };
    }

    await db.collection("group").deleteMany({
      corpId,
      $or: [{ _id: id }, { parentGroupId: id }],
    });
    return {
      success: true,
      message: "删除成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "删除失败",
    };
  }
};
exports.getGroups = async (context) => {
  const {
    corpId,
    teamId,
    page = 1,
    pageSize = 10,
    groupName,
    groupType,
  } = context;

  if (!corpId) {
    return {
      success: false,
      message: "机构ID未输入",
    };
  }

  // 构建查询条件
  let query = { corpId };

  if (groupType === "corp") {
    query.groupType = "corp";
  } else if (teamId) {
    query.teamId = teamId;
  }

  if (groupName && typeof groupName === "string" && groupName.trim()) {
    query.groupName = new RegExp(groupName.trim(), "i"); // MongoDB regex 查询，忽略大小写
  }

  try {
    // 计算总数（使用 countDocuments 替代查询，避免不必要的遍历）
    const total = await db.collection("group").countDocuments(query);
    const pages = Math.ceil(total / pageSize);

    // 使用 aggregate 优化查询
    const groups = await db
      .collection("group")
      .aggregate([
        { $match: query },
        { $sort: { createTime: -1 } }, // 按 createTime 降序排列
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
        {
          $lookup: {
            from: "member", // 假设 members 数据在 "member" 集合中
            localField: "_id",
            foreignField: "groupIds",
            pipeline: [
              { $match: { corpId, teamId } }, // 筛选当前团队下的成员
            ],
            as: "members",
          },
        },
        {
          $addFields: {
            memberCount: { $size: "$members" }, // 计算每个分组的成员数量
          },
        },
        { $project: { members: 0 } }, // 不返回 "members" 数组
      ])
      .toArray();

    return {
      success: true,
      data: groups,
      message: "获取成功",
      total,
      pages,
      size: pageSize,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.addGroupIdForMember = async (context) => {
  const { memberId, toGroupId, fromGroupId } = context;
  if (!memberId) {
    return {
      success: false,
      message: "客户Id未输入!",
    };
  }

  try {
    const member = await db.collection("member").findOne({ _id: memberId });
    let { groupIds = [] } = member;

    if (fromGroupId && Array.isArray(groupIds)) {
      const index = groupIds.findIndex((e) => e == fromGroupId);
      groupIds.splice(index, 1);
    }
    if (toGroupId && Array.isArray(groupIds)) {
      const index = groupIds.findIndex((e) => e == toGroupId);
      if (index < 0) groupIds.push(toGroupId);
    }

    await db
      .collection("member")
      .updateOne({ _id: memberId }, { $set: { groupIds } });
    return {
      success: true,
      groupIds,
      message: "更新成功!",
    };
  } catch (error) {
    return {
      success: false,
      message: "更新失败!",
    };
  }
};

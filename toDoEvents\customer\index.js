const utils = require("../utils");
const dayjs = require("dayjs");
const common = require("../../common");
let db = null;
/**
 *  自动建档的客户24小时后,为处理生成待办事项
 */
async function autoCreateCustomerToEnvent({ corpId }, DB) {
  db = DB;

  try {
    // 获取到所有自动建档的档案
    const params = {
      createType: "auto",
      toDo: { $ne: "generated" },
      createTime: { $lte: dayjs().subtract(24, "hour").valueOf() },
      teamId: { $exists: true, $ne: [], $ne: "" },
    };
    if (corpId) params["corpId"] = corpId;

    const fetchData = async (page, pageSize, db) => {
      const data = await db
        .collection("member")
        .find(params)
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .toArray();
      return data;
    };

    const list = await utils.getAllData(fetchData, db);
    console.log("所有自动建档的档案", list);

    const bulkOps = [];
    const bulkUpdateOps = [];

    for (const i of list) {
      let params = {
        eventType: "customerUpdate",
        customerId: i._id,
        externalUserId: i.externalUserId || "",
        customerName: i.name,
        corpId: i.corpId,
        creatorUserId: "system",
        taskContent: `客户${i.name}的档案为系统自动创建，请尽快更新客户档案信息，保证客户信息的真实有效`,
      };
      params = utils.createTodoEvents(params);

      for (const j of i.personResponsibles) {
        const newParams = { ...params };
        newParams._id = common.generateRandomString(24);
        newParams.executeTeamId = j.teamId;
        newParams.executorUserId = j.corpUserId;
        console.log("生成待办", newParams);
        bulkOps.push({
          insertOne: {
            document: newParams,
          },
        });
      }

      bulkUpdateOps.push({
        updateOne: {
          filter: { _id: i._id },
          update: { $set: { toDo: "generated" } },
        },
      });
    }

    if (bulkOps.length > 0) {
      await db.collection("to-do-events").bulkWrite(bulkOps);
    }

    if (bulkUpdateOps.length > 0) {
      await db.collection("member").bulkWrite(bulkUpdateOps);
    }

    return {
      success: true,
      message: "生成待办成功",
    };
  } catch (error) {
    throw error;
  }
}

module.exports = { autoCreateCustomerToEnvent };

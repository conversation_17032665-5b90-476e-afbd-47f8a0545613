let db = "";
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "getDiagnosisList":
      return await getDiagnosisList(item);
    case "deleteDiagnosis":
      return await deleteDiagnosis(item);
    case "updateDiagnosis":
      return await updateDiagnosis(item);
    case "addDiagnosis":
      return await addDiagnosis(item);
  }
};

// 获取诊断列表

async function getDiagnosisList(item) {
  const { page, pageSize, name, code, keyword } = item;
  try {
    // 调用mongo 数据库查询
    let query = {};
    if (name && typeof name === "string") {
      query.name = new RegExp(name.trim(), "i");
    }
    if (code && typeof code === "string") {
      query.code = new RegExp(code.trim(), "i");
    }
    if (typeof keyword == "string" && keyword.trim()) {
      query.$or = [
        { name: new RegExp(keyword.trim(), "i") },
        { code: new RegExp(keyword.trim(), "i") },
        { category: new RegExp(keyword.trim(), "i") },
        { pinyin: new RegExp(keyword.trim(), "i") },
      ];
    }
    const res = await db
      .collection("hlw-diagnosis")
      .find(query)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    const total = await db.collection("hlw-diagnosis").countDocuments(query);
    return {
      success: true,
      message: "查询成功",
      data: res,
      total,
      pages: Math.ceil(total / pageSize),
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "查询失败",
    };
  }
}

// 删除诊断信息
async function deleteDiagnosis(item) {
  const { _id } = item;
  try {
    // 调用mongo 数据库删除
    const res = await db.collection("hlw-diagnosis").deleteOne({
      _id: new ObjectId(_id),
    });
    return {
      success: true,
      message: "删除成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "删除失败",
    };
  }
}

// 更新诊断信息
async function updateDiagnosis(item) {
  const { _id, name, code } = item;
  try {
    // 调用mongo 数据库更新
    const res = await db
      .collection("hlw-diagnosis")
      .updateOne(
        { _id: new ObjectId(_id) },
        { $set: { name, code, updateTime: Date.now() } }
      );
    return {
      success: true,
      message: "更新成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "更新失败",
    };
  }
}

//新增诊断信息
/**
 * 新增诊断信息
 * @param {Object} item
 * @param {String} item.name 诊断名称
 * @param {String} item.code 诊断编码
 */

async function addDiagnosis(item) {
  const { name, code } = item;
  try {
    // 调用mongo 数据库新增
    const res = await db.collection("hlw-diagnosis").insertOne({
      name,
      code,
      createTime: Date.now(),
    });
    return {
      success: true,
      message: "新增成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "新增失败",
    };
  }
}


async function uploadDiagnosis(ids) {
  if (!Array.isArray(ids) || ids.length == 0) return {
    success: false,
    message: "参数错误"
  };
  try {
    const res = await db.collection("hlw-prescription").find({_id: {$in: ids.map(new ObjectId)}}).toArray();
    
    return {
      success: true,
      message: "上传成功",
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "上传失败",
    };
  }


}
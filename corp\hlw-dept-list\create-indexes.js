// 互联网科室表索引创建脚本
// 使用数据库：corp
// 集合：hlw-dept-list

// 为科室ID和机构ID创建复合唯一索引，确保在同一机构内科室ID唯一
db.getCollection("hlw-dept-list").createIndex(
  { corpId: 1, hlw_dept_id: 1 },
  { unique: true, name: "idx_corpId_hlw_dept_id_unique" }
);

// 为科室名称和机构ID创建复合唯一索引，确保在同一机构内科室名称唯一
db.getCollection("hlw-dept-list").createIndex(
  { corpId: 1, hlw_dept_name: 1 },
  { unique: true, name: "idx_corpId_hlw_dept_name_unique" }
);

// 为机构ID创建索引，提高查询效率
db.getCollection("hlw-dept-list").createIndex(
  { corpId: 1 },
  { name: "idx_corpId" }
);

// 为上级ID创建索引，提高树形查询效率
db.getCollection("hlw-dept-list").createIndex(
  { parentId: 1 },
  { name: "idx_parentId" }
);

// 为院区ID创建索引，提高按院区查询效率
db.getCollection("hlw-dept-list").createIndex(
  { areaId: 1 },
  { name: "idx_areaId" }
);

// 为层级创建索引，提高按层级筛选的查询效率
db.getCollection("hlw-dept-list").createIndex(
  { level: 1 },
  { name: "idx_level" }
);

// 为状态字段创建索引，提高按状态筛选的查询效率
db.getCollection("hlw-dept-list").createIndex(
  { status: 1 },
  { name: "idx_status" }
);

// 为排序字段创建索引，提高排序查询效率
db.getCollection("hlw-dept-list").createIndex(
  { sort: 1 },
  { name: "idx_sort" }
);

// 为创建时间创建索引，提高按时间排序的查询效率
db.getCollection("hlw-dept-list").createIndex(
  { createTime: -1 },
  { name: "idx_createTime_desc" }
);

// 为更新时间创建索引
db.getCollection("hlw-dept-list").createIndex(
  { updateTime: -1 },
  { name: "idx_updateTime_desc" }
);

// 复合索引：机构ID + 状态 + 层级，用于常见的查询组合
db.getCollection("hlw-dept-list").createIndex(
  { corpId: 1, status: 1, level: 1 },
  { name: "idx_corpId_status_level" }
);

// 复合索引：机构ID + 院区ID + 状态，用于按院区查询科室
db.getCollection("hlw-dept-list").createIndex(
  { corpId: 1, areaId: 1, status: 1 },
  { name: "idx_corpId_areaId_status" }
);

console.log("互联网科室表索引创建完成");

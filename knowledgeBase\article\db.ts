interface ArticleCate {
  _id: string
  corpId: string // 机构id
  createTime: number // 创建时间
  name: string // 分类名称
  useId: string //  创建者userId
  updateTime: number
  updateUserId: string
}

interface Article {
  id: string // 
  cateId: string // 分类id
  corpId: string // 机构id
  cover: string //分类
  createTime: number // 创建时间
  disease: Disease[] // 关联疾病
  enable: boolean // true 已发布 | false 已停用
  keyword: string // 关键字
  summary: string // 摘要
  title: string //  文章标题
  link: string // 文章的第三方链接
}

interface Disease {
  label: string
  value: string
}
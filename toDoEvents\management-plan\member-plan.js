const dayjs = require("dayjs");
const common = require("../../common");

let db = null;

exports.main = async (event, DB) => {
  db = DB;
  switch (event.type) {
    case "addMemberManagementPlan":
      return await this.addMemberManagementPlan(event);
    case "updateMemberMangePlanStatus":
      return await this.updateMemberMangePlanStatus(event);
    case "stopMemberMangePlan":
      return await this.stopMemberMangePlan(event);
    case "getMemberManagementPlan":
      return await this.getMemberManagementPlan(event);
    case "updateMemberManagementPlanStatus":
      return await this.updateMemberManagementPlanStatus(event);
    case "batchUpdateMemberManagePlan":
      return await this.batchUpdateMemberManagePlan(event);
  }
};

exports.addMemberManagementPlan = async (item) => {
  let {
    planName,
    planId,
    executeTeamId,
    executorUserId,
    customerId,
    planExecutStaus,
    executeTeamName,
    planExecutionTime,
    changedStatus = "",
    timeType,
    corpId,
  } = item;
  const query = {
    corpId,
    planId,
    planName,
    planExecutionTime,
    executeTeamId,
    executeTeamName,
    executorUserId,
    planExecutStaus,
    customerId,
    changedStatus,
    timeType,
  };
  // 获取管理计划列表
  const memberPlanCount = await db
    .collection("member-management-plan")
    .find({
      customerId,
      executeTeamId,
      planExecutStaus: "untreated",
    })
    .count();
  if (memberPlanCount > 0) {
    return false;
  }
  query._id = common.generateRandomString(24);
  const result = await db.collection("member-management-plan").insertOne(query);
  return result.insertedId;
};

// 更新管理计划状态
exports.updateMemberMangePlanStatus = async (item) => {
  const { memberPlanId, params } = item;
  const { changedStatus, planExecutStaus } = params;
  if (!memberPlanId) {
    return {
      success: false,
      message: "客户管理计划ID未传",
    };
  }
  const updateFields = {
    changedStatus,
    planExecutStaus,
    updateTime: dayjs().valueOf(),
  };
  try {
    await db
      .collection("member-management-plan")
      .updateOne({ _id: memberPlanId }, { $set: updateFields });
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "更新失败",
    };
  }
};

exports.stopMemberMangePlan = async ({ memberPlanId }) => {
  if (!memberPlanId) return true;
  // 查询是否有未处理的任务
  const taskCount = await db
    .collection("management-plan-task")
    .find({
      memberPlanId,
      taskStatus: "unexecuted",
    })
    .count();
  // 查询是否还有未处理的事件
  const memberPlanCount = await db
    .collection("to-do-events")
    .find({
      memberPlanId,
      eventStatus: "untreated",
    })
    .count();
  // 如果没有未处理的任务和事件, 则关闭管理计划
  if (taskCount === 0 && memberPlanCount === 0) {
    await db
      .collection("member-management-plan")
      .updateOne({ _id: memberPlanId }, { $set: { planExecutStaus: "closed" } });
    return true;
  } else {
    return false;
  }
};

exports.getMemberManagementPlan = async (item) => {
  const { customerId, executeTeamId } = item;
  const query = {
    customerId,
    executeTeamId,
  };
  try {
    const planList = await db
      .collection("member-management-plan")
      .find(query)
      .toArray();
    const plan = planList.length > 0 ? planList[planList.length - 1] : {};
    return {
      success: true,
      data: plan,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.updateMemberManagementPlanStatus = async (item) => {
  const { memberPlanId, params } = item;
  const { planExecutStaus } = params;
  try {
    await db
      .collection("member-management-plan")
      .updateOne({ _id: memberPlanId }, { $set: { planExecutStaus } });
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "更新失败",
    };
  }
};

exports.batchUpdateMemberManagePlan = async (context) => {
  const { customerIds, currentTeamId, params, corpId } = context;
  try {
    const query = {
      customerId: { $in: customerIds },
      planExecutStaus: "executing",
      executeTeamId: currentTeamId,
      corpId,
    };
    const res = await db
      .collection("member-management-plan")
      .updateMany(query, { $set: params });
    return {
      success: true,
      data: res,
      message: "修改成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "修改失败",
    };
  }
};

const dayjs = require("dayjs");
const utils = require("./utils");
const common = require("../../common");
const api = require("../../api");
let db = null;
let currentCorpId = "";

exports.main = async (event, mongodb) => {
  db = mongodb;
  currentCorpId = event.corpId;
  switch (event.type) {
    case "getWork":
      return await getWorkSchedule(event);
    case "removeWork":
      return await removeWork(event);
    case "setWork":
      return await setWorkSchedule(event);
    case "closeWorkTodo":
      return await closeWorkTodo(event);
  }
};

async function setWorkSchedule(context) {
  const { _id } = context;
  const { success: pass, data: params, message } = utils.verifyData(context);
  if (pass) {
    const isUpdate = Boolean(_id);
    const res = await (isUpdate ? update(_id, params) : add(params));
    return res;
  }
}

async function add(payload) {
  try {
    let doctorWorkId = "";
    const createTime = new Date().getTime();
    if (
      payload.workType === "visit-reg" &&
      payload.doctorUserId &&
      payload.doctorUserId !== payload.userId
    ) {
      const {
        doctorUserId,
        isTodo,
        todoTime,
        taskContent,
        sendContent,
        pannedEventSendFile,
        ...doctorPayload
      } = payload;
      const res = await add({
        ...doctorPayload,
        doctorUserId,
        taskContent,
        userId: doctorUserId,
        isTodo: "NO",
      });
      if (!res.success) {
        return { success: false, message: res.message };
      }
      doctorWorkId = res.id;
    }
    if (payload.isTodo === "YES") {
      const { success: addSuccess, id, message } = await addTodo(payload);
      if (addSuccess) {
        payload.todoId = id;
      } else {
        return { success: false, message };
      }
    }
    const res = await db.collection("work-schedule").insertOne({
      ...payload,
      createTime,
      doctorWorkId,
      _id: common.generateRandomString(24),
    });
    return { success: true, message: "新增日程成功", id: res.insertedId };
  } catch (e) {
    return { success: false, message: "新增日程失败" };
  }
}

async function update(_id, params) {
  try {
    const record = await db
      .collection("work-schedule")
      .findOne({ _id, corpId: params.corpId, userId: params.userId });
    if (record) {
      if (record.workTimeStamp < +new Date()) {
        return { success: false, message: "更新日程失败(日程已过期)" };
      }
      if (record.todoId && params.isTodo === "YES") {
        const { title, workContent, corpId, userId } = params;
        params = { title, workContent, corpId, userId };
      }
      if (
        params.workType === "visit-reg" &&
        record.workType === "visit-reg" &&
        record.doctorWorkId &&
        params.doctorUserId &&
        params.doctorUserId === record.doctorUserId
      ) {
        const {
          doctorUserId,
          doctorWorkId,
          isTodo,
          todoTime,
          taskContent,
          sendContent,
          pannedEventSendFile,
          ...doctorPayload
        } = params;
        const res = await update(record.doctorWorkId, {
          ...doctorPayload,
          userId: doctorUserId,
          isTodo: "NO",
        });
        if (!res.success) {
          return { success: false, message: res.message };
        }
      }
      if (
        params.workType === "schedule" &&
        record.workType === "visit-reg" &&
        record.doctorWorkId &&
        record.doctorUserId
      ) {
        const res = await removeWork({
          id: record.doctorWorkId,
          userId: record.doctorUserId,
          corpId: record.corpId,
        });
        if (!res.success) {
          return { success: false, message: res.message };
        }
      }
      if (
        params.workType === "visit-reg" &&
        record.workType === "visit-reg" &&
        params.doctorUserId &&
        record.doctorUserId &&
        params.doctorUserId !== record.doctorUserId
      ) {
        if (record.doctorWorkId && record.doctorUserId) {
          const res = await removeWork({
            id: record.doctorWorkId,
            userId: record.doctorUserId,
            corpId: record.corpId,
          });
          if (!res.success) {
            return { success: false, message: res.message };
          }
          params.doctorWorkId = "";
        }
        if (record.userId !== params.doctorUserId) {
          const {
            doctorUserId,
            isTodo,
            todoTime,
            taskContent,
            ...doctorPayload
          } = params;
          const res = await add({
            ...doctorPayload,
            userId: doctorUserId,
            isTodo: "NO",
          });
          if (!res.success) {
            return { success: false, message: res.message };
          }
          params.doctorWorkId = res.id;
        }
      }
      if (params.isTodo === "YES") {
        const { success: addSuccess, id, message } = await addTodo(params);
        if (addSuccess) {
          params.todoId = id;
        } else {
          return { success: false, message };
        }
      }
      await db
        .collection("work-schedule")
        .updateOne(
          { _id, corpId: params.corpId, userId: params.userId },
          { $set: params }
        );
      return { success: true, message: "更新日程成功" };
    }
    return { success: false, message: "更新日程失败(未查询到对应日程)" };
  } catch (e) {
    return { success: false, message: "更新日程失败" };
  }
}

async function getWorkSchedule(context) {
  try {
    const { userId, corpId, startDate = "", endDate = "", teamId } = context;
    if (!userId) {
      return { success: false, message: "员工id不能为空" };
    }
    if (!corpId) {
      return { success: false, message: "机构id不能为空" };
    }
    if (!startDate) {
      return { success: false, message: "开始时间不能为空" };
    }
    if (!endDate) {
      return { success: false, message: "结束时间不能为空" };
    }
    let query = {
      userId,
      corpId,
      workTimeStamp: { $gte: new Date(startDate).getTime(), $lte: new Date(endDate).getTime() },
    };
    if (teamId) query["teamId"] = teamId;
    const list = await db.collection("work-schedule").find(query).toArray();
    return { success: true, message: "查询日程成功", list };
  } catch (e) {
    return { success: false, message: "查询日程失败" };
  }
}

async function removeWork(context) {
  try {
    const { id: _id, todoId = "", userId, corpId } = context;
    const query = { _id, userId, corpId };
    const record = await db
      .collection("work-schedule")
      .aggregate([
        { $match: query },
        {
          $lookup: {
            from: "to-do-events",
            localField: "todoId",
            foreignField: "_id",
            as: "todo",
          },
        },
      ])
      .toArray();
    if (!record.length)
      return { success: false, message: "删除失败(日程不存在)" };
    const todo = record[0].todo ? record[0].todo[0] : null;
    if (
      record[0] &&
      record[0].workTimeStamp > +new Date() &&
      todo &&
      todoId &&
      todoId === todo._id &&
      todo.eventStatus === "untreated"
    ) {
      const { message, success } = await (dayjs(todo.createTime)
        .startOf("day")
        .isAfter(dayjs())
        ? removeTodo(todoId, userId, corpId)
        : closeTodo(todoId, userId));
      if (!success) return { message, success };
    }
    await db.collection("work-schedule").deleteOne(query);
    return { success: true, message: "删除成功" };
  } catch (e) {
    return { success: false, message: "删除失败" };
  }
}

async function closeWorkTodo(context) {
  try {
    const { id: _id, userId, corpId } = context;
    const record = await db
      .collection("work-schedule")
      .aggregate([
        { $match: { _id, userId, corpId } },
        {
          $lookup: {
            from: "to-do-events",
            localField: "todoId",
            foreignField: "_id",
            as: "todo",
          },
        },
      ])
      .toArray();
    if (!record.length) return { success: true, message: "未查询到该日程" };
    if (record[0].workTimeStamp < new Date().getTime())
      return { success: true, message: "该日程已经过期" };
    const todo = record[0].todo ? record[0].todo[0] : null;
    if (record[0].isTodo === "YES" && todo) {
      const { message, success } = await (dayjs(todo.createTime)
        .startOf("day")
        .isAfter(dayjs())
        ? removeTodo(todo._id, userId, corpId)
        : closeTodo(todo._id, userId));
      if (!success) {
        return { success: false, message };
      }
      await db.collection("work-schedule").updateOne(
        { _id, userId, corpId },
        {
          $set: {
            isTodo: "NO",
            taskContent: "",
            sendContent: "",
            pannedEventSendFile: {},
            advanceDay: "",
            advanceDayStr: "",
          },
        }
      );
      return {
        success: true,
        message: "关闭日程待办成功",
        data: {
          isTodo: "NO",
          taskContent: "",
          sendContent: "",
          pannedEventSendFile: {},
          advanceDay: "",
          advanceDayStr: "",
        },
      };
    }
    return { success: false, message: "关闭日程待办失败" };
  } catch (e) {
    return { success: false, message: "关闭日程待办失败" };
  }
}

async function addTodo(payload) {
  try {
    let res = await api.getTodoApi({
      type: "createTodo",
      corpId: payload.corpId,
      customerId: payload.memberId, // 服务对象id
      customerName: payload.customerName, // 服务对象姓名
      expireTime: payload.workTimeStamp + 7 * 24 * 60 * 60 * 1000, // 过期时间
      plannedExecutionTime: dayjs(payload.todoTime).valueOf(), // 计划执行时间
      externalUserId: payload.externalUserId, // 服务对象的外部联系人id
      executeTeamId: payload.teamId,
      taskContent: payload.taskContent, // 待办内容
      createTime: payload.todoTime, // 创建时间
      creatorUserId: payload.userId, // 创建人
      eventStatus: "untreated", // 待办状态
      eventType: "workSchedule", // 待办类型
      executorUserId: payload.userId, // 执行人userId
    });
    if (res) {
      const { success, message, id } = res;
      return { success, message, id };
    }
  } catch (e) {
    return { success: false, message: "新增日程待办失败" };
  }
}

async function closeTodo(id, userId) {
  try {
    let res = await api.getTodoApi({
      type: "setTodoStatus",
      id,
      userId,
      eventStatus: "closed",
      result: "因关联日程删除，待办单关闭",
      corpId: currentCorpId,
    });
    if (res) {
      const { success, message } = res;
      return { success, message };
    }
  } catch (e) {
    return { success: false, message: "关闭日程待办失败" };
  }
}

async function removeTodo(id, userId, corpId) {
  try {
    let res = await api.getTodoApi({
      type: "removeTodo",
      id,
      userId,
      corpId,
    });
    if (res) {
      const { success, message } = res;
      return { success, message };
    }
  } catch (e) {
    return { success: false, message: "关闭日程待办失败" };
  }
}

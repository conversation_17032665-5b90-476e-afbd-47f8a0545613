const dayjs = require("dayjs");
const common = require("../../common");

let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "addRecommendRecord":
      return addRecommendRecord(content);
    case "updateRecommendRecord":
      return updateRecommendRecord(content);
    case "deleteRecommendRecord":
      return deleteRecommendRecord(content);
    case "getRecommendRecord":
      return getRecommendRecord(content);
    case "getRecommendRecordList":
      return getRecommendRecordList(content);
  }
};

// 新增推荐记录
async function addRecommendRecord(params) {
  const data = verifyParams(params);
  if (typeof data === "string") return { success: false, message: data };

  // 生成记录ID
  data._id = common.generateRandomString(24);
  data.sendTime = Date.now();

  try {
    const res = await db.collection("recommend-records").insertOne(data);
    return {
      success: true,
      message: "新增推荐记录成功",
      data: { _id: data._id },
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 更新推荐记录
async function updateRecommendRecord(params) {
  if (!params._id) return { success: false, message: "记录id不能为空" };

  const data = verifyParams(params, true);
  if (typeof data === "string") return { success: false, message: data };

  // 移除_id字段，避免更新时出错
  delete data._id;
  data.updateTime = Date.now();

  try {
    const res = await db.collection("recommend-records").updateOne(
      { _id: params._id, corpId: params.corpId },
      { $set: data }
    );
    if (res.matchedCount === 0)
      return { success: false, message: "未找到对应的推荐记录" };
    return { success: true, message: "更新推荐记录成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 删除推荐记录
async function deleteRecommendRecord(params) {
  if (!params._id) return { success: false, message: "记录id不能为空" };
  if (!params.corpId) return { success: false, message: "机构id不能为空" };

  try {
    const res = await db.collection("recommend-records").deleteOne({
      _id: params._id,
      corpId: params.corpId
    });
    if (res.deletedCount === 0)
      return { success: false, message: "未找到对应的推荐记录" };
    return { success: true, message: "删除推荐记录成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 根据ID查询单条推荐记录
async function getRecommendRecord(params) {
  if (!params._id) return { success: false, message: "记录id不能为空" };
  if (!params.corpId) return { success: false, message: "机构id不能为空" };

  try {
    const record = await db.collection("recommend-records").findOne({
      _id: params._id,
      corpId: params.corpId
    });

    if (!record) {
      return { success: false, message: "未找到对应的推荐记录" };
    }

    return {
      success: true,
      message: "查询成功",
      data: record,
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 查询推荐记录列表
async function getRecommendRecordList(params) {
  const {
    corpId,
    creatorUserId,
    hlwDeptId,
    doctorId,
    page = 1,
    pageSize = 20,
    startDate,
    endDate
  } = params;

  if (!corpId) return { success: false, message: "机构id不能为空" };

  const query = { corpId };

  // 添加查询条件
  if (creatorUserId) query.creatorUserId = creatorUserId;
  if (hlwDeptId) query.hlwDeptId = hlwDeptId;
  if (doctorId) query.doctorId = doctorId;

  // 时间范围查询
  if (startDate && endDate) {
    query.sendTime = {
      $gte: dayjs(startDate).startOf("day").valueOf(),
      $lte: dayjs(endDate).endOf("day").valueOf(),
    };
  }

  try {
    const [list, total] = await Promise.all([
      db
        .collection("recommend-records")
        .find(query)
        .sort({ sendTime: -1 })
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .toArray(),
      db.collection("recommend-records").countDocuments(query),
    ]);

    return {
      success: true,
      list,
      total,
      pages: Math.ceil(total / pageSize),
      message: "查询成功",
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 参数校验
function verifyParams(params, isUpdate = false) {
  if (!params.corpId) return "机构id不能为空";

  if (!isUpdate) {
    // 新增时的必填字段校验
    if (!params.creatorUserId) return "创建人用户ID不能为空";
    if (!params.creatorName) return "创建人姓名不能为空";
    if (!params.hlwDeptId) return "HLW部门ID不能为空";
    if (!params.hlwDeptName) return "HLW部门名称不能为空";
    if (!params.doctorId) return "医生ID不能为空";
    if (!params.doctorName) return "医生姓名不能为空";
    if (!params.sendLink) return "发送链接不能为空";
  }

  // 构建数据对象
  const data = {
    corpId: params.corpId,
    creatorUserId: params.creatorUserId,
    creatorName: params.creatorName,
    hlwDeptId: params.hlwDeptId,
    hlwDeptName: params.hlwDeptName,
    doctorId: params.doctorId,
    doctorName: params.doctorName,
    doctorTitle: params.doctorTitle || "",
    doctorSpecialty: params.doctorSpecialty || "",
    sendLink: params.sendLink,
  };

  // 如果是更新操作，只包含传入的字段
  if (isUpdate) {
    const updateData = {};
    Object.keys(data).forEach(key => {
      if (params[key] !== undefined) {
        updateData[key] = data[key];
      }
    });
    return updateData;
  }

  return data;
}
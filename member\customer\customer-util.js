const dayjs = require('dayjs');

exports.getBirthdayStamp = customer => {
  const { birthday, idCard } = (customer || {});
  if (typeof idCard === 'string' && isChinaId(idCard)) {
    const year = idCard.substring(6, 10);
    const month = idCard.substring(10, 12);
    const day = idCard.substring(12, 14);
    const date = `${year}-${month}-${day}`;
    if (dayjs(date).isValid()) return dayjs(date).valueOf();
  }
  if (birthday && dayjs(birthday).isValid()) {
    return dayjs(birthday).valueOf();
  }
}

const idTransfer = (sId) => {
  if (sId.length === 15) {
    const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2],
      arrCh = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
    let nTemp = 0;

    sId = sId.substr(0, 6) + "19" + sId.substr(6, sId.length - 6);

    for (let i = 0; i < 17; i++) {
      nTemp += Number(sId.substr(i, 1)) * arrInt[i];
    }
    sId += arrCh[nTemp % 11];
  }
  return sId;
};

function isChinaId(s) {
  const sId = idTransfer(s),
    aCity = {
      11: "北京",
      12: "天津",
      13: "河北",
      14: "山西",
      15: "内蒙古",
      21: "辽宁",
      22: "吉林",
      23: "黑龙江 ",
      31: "上海",
      32: "江苏",
      33: "浙江",
      34: "安徽",
      35: "福建",
      36: "江西",
      37: "山东",
      41: "河南",
      42: "湖北 ",
      43: "湖南",
      44: "广东",
      45: "广西",
      46: "海南",
      50: "重庆",
      51: "四川",
      52: "贵州",
      53: "云南",
      54: "西藏 ",
      61: "陕西",
      62: "甘肃",
      63: "青海",
      64: "宁夏",
      65: "新疆",
      71: "台湾",
      81: "香港",
      82: "澳门",
      91: "国外 ",
    };
  if (!/^\d{17}(\d|x)$/i.test(sId)) {
    return false;
  }

  if (!aCity[parseInt(sId.substr(0, 2), 10)]) {
    return false
  }

  if (sId.length == 18) {
    //∑(ai×Wi)(mod 11)
    //加权因子
    const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    //校验位
    const parity = [1, 0, "X", 9, 8, 7, 6, 5, 4, 3, 2];
    let sum = 0;
    let ai = 0;
    let wi = 0;
    for (let i = 0; i < 17; i++) {
      ai = Number(sId[i]);
      wi = factor[i];
      sum += ai * wi;
    }
    const last = parity[sum % 11];
    if (last != sId[17].toUpperCase()) {
      return false;
    }
  }

  return true
}
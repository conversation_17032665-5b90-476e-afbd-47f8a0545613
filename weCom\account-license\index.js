const corpToken = require("../token/index");
const request = require("../request");
exports.transferLicense = async (context) => {
  const { handoverUserid, takeoverUserid, corpId } = context;
  const provider_access_token = await corpToken.getProviderAccessToken();
  let url = `https://qyapi.weixin.qq.com/cgi-bin/license/batch_transfer_license?provider_access_token=${provider_access_token}`;
  const query = {
    corpid: corpId,
    transfer_list: [
      {
        handover_userid: handoverUserid,
        takeover_userid: takeoverUserid,
      },
    ],
  };
  let res = await request.main(url, query, "POST");
  if (res.errcode !== 0) {
    return {
      success: false,
      message: res.errmsg,
      errcode: res.errcode,
    };
  } else {
    return {
      success: true,
      message: "转移成功",
    };
  }
};

const dayjs = require("dayjs");
const common = require("../../common");
const api = require("../../api");

let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "addEConsuleRecord":
      return addEConsuleRecord(content);
    case "updateEConsuleRecord":
      return updateEConsuleRecord(content);
    case "getEConsuleRecord":
      return getEConsuleRecord(content);
    case "getFirstEConsuleRecord":
      return getFirstEConsuleRecord(content);
  }
};
// 新增网络咨询记录
async function addEConsuleRecord(params) {
  const data = verfiyParams(params);
  if (typeof data === "string") return { success: false, message: data };
  data._id = common.generateRandomString(24); // 生成随机字符串作为_id
  data.corpId = params.corpId;
  data.userId = params.userId;
  data.customerId = params.customerId;
  data.registrantUserId = params.registrantUserId;
  try {
    const res = await db.collection("e-consult-record").insertOne(data);
    return {
      success: true,
      message: "新增网络咨询记录成功",
      data: res.insertedId,
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function getEConsuleRecord(params) {
  const {
    page,
    pageSize,
    customerId,
    corpId,
    projectIds,
    startDate,
    endDate,
    source,
    mobile,
    registrantUserIds,
    developerIds,
    customerName,
    isArrived,
    isStatistics, // 是否需要统计数据
  } = params;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  const query = { corpId };
  if (customerId) query.customerId = customerId;
  if (Array.isArray(projectIds) && projectIds.length > 0)
    query["projectIds"] = { $in: projectIds };
  if (Array.isArray(source) && source.length > 0)
    query.source = { $in: source };
  if (startDate && endDate) {
    query.timestamp = {
      $gte: dayjs(startDate).startOf("day").valueOf(),
      $lte: dayjs(endDate).endOf("day").valueOf(),
    };
  }
  if (
    registrantUserIds &&
    Array.isArray(registrantUserIds) &&
    registrantUserIds.length > 0
  ) {
    query.registrantUserId = { $in: registrantUserIds };
  }

  if (developerIds && Array.isArray(developerIds) && developerIds.length > 0) {
    query.userId = { $in: developerIds };
  }

  // 到院状态筛选

  try {
    // 构建基础聚合管道
    const basePipeline = [
      { $match: query },
      {
        $lookup: {
          from: "member",
          localField: "customerId",
          foreignField: "_id",
          as: "memberInfo",
        },
      },
      {
        $unwind: {
          path: "$memberInfo",
          preserveNullAndEmptyArrays: true,
        },
      },
    ];

    // 添加手机号和姓名筛选
    const filterConditions = {};
    if (mobile) {
      filterConditions["memberInfo.mobile"] = { $regex: mobile, $options: "i" };
    }
    if (customerName) {
      filterConditions["memberInfo.name"] = {
        $regex: customerName,
        $options: "i",
      };
    }
    if (isArrived === true) {
      filterConditions["memberInfo.inHospitalTimes"] = {
        $exists: true,
        $ne: [],
      };
    } else if (isArrived === false) {
      filterConditions.$or = [
        { "memberInfo.inHospitalTimes": { $exists: false } },
        { "memberInfo.inHospitalTimes": null },
        { "memberInfo.inHospitalTimes": [] },
      ];
    }
    const pipelines = [...basePipeline];
    if (Object.keys(filterConditions).length > 0) {
      pipelines.push({ $match: filterConditions });
    }

    // 完成查询管道
    const pipeline = [
      ...pipelines,
      { $sort: { timestamp: -1, createTime: -1 } },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize },
      {
        $project: {
          _id: 1,
          corpId: 1,
          customerId: 1,
          userId: 1,
          registrantUserId: 1,
          source: 1,
          projectIds: 1,
          date: 1,
          timestamp: 1,
          reportDesc: 1,
          createTime: 1,
          updateTime: 1,
          customerName: "$memberInfo.name",
          customerMobile: "$memberInfo.mobile",
          inHospitalTimes: "$memberInfo.inHospitalTimes",
        },
      },
    ];
    const list = await db
      .collection("e-consult-record")
      .aggregate(pipeline)
      .toArray();

    // 计算总数
    const countPipeline = [...pipelines, { $count: "total" }];

    const totalResult = await db
      .collection("e-consult-record")
      .aggregate(countPipeline)
      .toArray();
    const total = totalResult.length > 0 ? totalResult[0].total : 0;

    // 如果需要统计到院和未到院人次
    let statistics = {};
    if (isStatistics) {
      const statisticsPipeline = [
        ...pipelines,
        {
          $facet: {
            // 总数统计
            total: [{ $count: "count" }],
            // 到院人次统计 - inHospitalTimes 是数组且长度大于0
            visited: [
              {
                $match: {
                  "memberInfo.inHospitalTimes": { $type: "array", $ne: [] },
                },
              },
              { $count: "count" },
            ],
            // 未到院人次统计 - inHospitalTimes 不存在、为null、为空数组
            notVisited: [
              {
                $match: {
                  $or: [
                    { "memberInfo.inHospitalTimes": { $exists: false } },
                    { "memberInfo.inHospitalTimes": null },
                    { "memberInfo.inHospitalTimes": [] },
                  ],
                },
              },
              { $count: "count" },
            ],
          },
        },
      ];

      const statsResult = await db
        .collection("e-consult-record")
        .aggregate(statisticsPipeline)
        .toArray();

      if (statsResult && statsResult.length > 0) {
        const statsData = statsResult[0];
        statistics = {
          total: statsData.total.length > 0 ? statsData.total[0].count : 0,
          visited:
            statsData.visited.length > 0 ? statsData.visited[0].count : 0,
          notVisited:
            statsData.notVisited.length > 0 ? statsData.notVisited[0].count : 0,
        };
      }
    }

    // 获取项目信息
    const projectIds = list.reduce((ids, item) => {
      if (Array.isArray(item?.projectIds)) {
        ids.push(...item.projectIds);
      }
      return ids;
    }, []);

    if (projectIds.length) {
      const res = await api.getCorpApi({
        type: "getProjectIntentNames",
        corpId,
        ids: projectIds,
      });

      if (res?.data?.length) {
        const projectMaps = res.data.reduce(
          (maps, item) => {
            if (item._id) {
              if (item.projectName)
                maps.nameMap.set(item._id, item.projectName);
              if (item.deptId) maps.deptMap.set(item._id, item.deptId);
            }
            return maps;
          },
          { nameMap: new Map(), deptMap: new Map() }
        );
        list.forEach((item) => {
          if (Array.isArray(item.projectIds)) {
            item.projectNames = item.projectIds
              .map((id) => projectMaps.nameMap.get(id))
              .filter(Boolean);
            item.projectDeptIds = item.projectIds
              .map((id) => projectMaps.deptMap.get(id))
              .filter(Boolean);
          } else {
            item.projectNames = [];
            item.projectDeptIds = [];
          }
        });
      }
    }

    return {
      success: true,
      list,
      total,
      pages: Math.ceil(total / pageSize),
      ...(isStatistics ? { statistics } : {}),
      filterConditions,
      message: "查询成功",
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}
// 构建基础查询条件
function buildQueryConditions({
  corpId,
  customerId,
  projectIds,
  startDate,
  endDate,
  source,
  registrantUserIds,
  developerIds,
}) {
  const query = { corpId };

  if (customerId) query.customerId = customerId;
  if (Array.isArray(projectIds) && projectIds.length > 0) {
    query.projectIds = { $in: projectIds };
  }
  if (Array.isArray(source) && source.length > 0) {
    query.source = { $in: source };
  }
  if (startDate && endDate) {
    query.timestamp = {
      $gte: dayjs(startDate).startOf("day").valueOf(),
      $lte: dayjs(endDate).endOf("day").valueOf(),
    };
  }
  if (
    registrantUserIds &&
    Array.isArray(registrantUserIds) &&
    registrantUserIds.length > 0
  ) {
    query.registrantUserId = { $in: registrantUserIds };
  }
  if (developerIds && Array.isArray(developerIds) && developerIds.length > 0) {
    query.userId = { $in: developerIds };
  }

  return query;
}

// 构建会员查询条件
function buildMemberQuery({ mobile, customerName, isArrived, corpId }) {
  const memberQuery = { corpId };

  if (mobile) {
    // 性能优化：如果mobile是完整的手机号，使用精确匹配
    if (/^\d{11}$/.test(mobile)) {
      memberQuery.mobile = mobile;
    } else {
      // 部分匹配时使用正则，但添加索引提示
      memberQuery.mobile = new RegExp(mobile, "i");
    }
  }

  if (customerName) {
    // 性能优化：如果名字长度较短（<2个字符），建议使用更精确的搜索
    if (customerName.length < 2) {
      console.warn("[性能警告] 客户姓名搜索条件过短，可能影响查询性能");
    }
    memberQuery.name = new RegExp(customerName, "i");
  }

  if (isArrived === true) {
    memberQuery.inHospitalTimes = { $exists: true, $ne: [] };
  } else if (isArrived === false) {
    memberQuery.$or = [
      { inHospitalTimes: { $exists: false } },
      { inHospitalTimes: null },
      { inHospitalTimes: [] },
    ];
  }

  return memberQuery;
}

// 有会员筛选条件的查询
async function getRecordsWithMemberFilter(
  query,
  memberQuery,
  page,
  pageSize,
  isStatistics
) {
  // 先查询符合条件的会员ID
  const memberIds = await db
    .collection("member")
    .find(memberQuery, {
      projection: { _id: 1, name: 1, mobile: 1, inHospitalTimes: 1 },
    })
    .toArray();

  if (memberIds.length === 0) {
    return {
      success: true,
      list: [],
      total: 0,
      pages: 0,
      ...(isStatistics
        ? { statistics: { total: 0, visited: 0, notVisited: 0 } }
        : {}),
      message: "查询成功",
    };
  }

  // 构建会员信息映射
  const memberMap = new Map();
  memberIds.forEach((member) => {
    memberMap.set(member._id, {
      name: member.name,
      mobile: member.mobile,
      inHospitalTimes: member.inHospitalTimes,
    });
  });

  // 添加客户ID筛选条件
  query.customerId = { $in: memberIds.map((m) => m._id) };

  // 查询记录
  const [list, total] = await Promise.all([
    db
      .collection("e-consult-record")
      .find(query)
      .sort({ timestamp: -1, createTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray(),
    db.collection("e-consult-record").countDocuments(query),
  ]);

  // 补充会员信息
  list.forEach((record) => {
    const memberInfo = memberMap.get(record.customerId);
    if (memberInfo) {
      record.customerName = memberInfo.name;
      record.customerMobile = memberInfo.mobile;
      record.inHospitalTimes = memberInfo.inHospitalTimes;
    }
  });

  // 统计信息
  let statistics = {};
  if (isStatistics) {
    // 性能优化：如果总数超过10000，跳过复杂的统计查询，避免超时
    if (total > 10000) {
      console.warn("[性能优化] 数据量过大，跳过详细统计查询");
      statistics = {
        total: total,
        visited: 0,
        notVisited: 0,
        message: "数据量过大，统计功能已禁用，请缩小查询范围",
      };
    } else {
      try {
        // 使用已有的memberMap进行统计，避免额外的数据库查询
        const visitedMembers = Array.from(memberMap.values()).filter(
          (memberInfo) =>
            memberInfo.inHospitalTimes &&
            Array.isArray(memberInfo.inHospitalTimes) &&
            memberInfo.inHospitalTimes.length > 0
        );

        statistics = {
          total: total,
          visited: visitedMembers.length,
          notVisited: memberMap.size - visitedMembers.length,
        };
      } catch (statsError) {
        console.error("[统计查询错误]", statsError);
        statistics = {
          total: total,
          visited: 0,
          notVisited: 0,
          message: "统计查询失败",
        };
      }
    }
  }

  // 获取项目信息
  await enrichProjectInfo(list, query.corpId);

  return {
    success: true,
    list,
    total,
    pages: Math.ceil(total / pageSize),
    ...(isStatistics ? { statistics } : {}),
    message: "查询成功",
  };
}

// 无会员筛选条件的查询（性能更好）
async function getRecordsWithoutMemberFilter(
  query,
  page,
  pageSize,
  isStatistics
) {
  // 并行查询列表和总数
  const [list, total] = await Promise.all([
    db
      .collection("e-consult-record")
      .find(query)
      .sort({ timestamp: -1, createTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray(),
    db.collection("e-consult-record").countDocuments(query),
  ]);

  if (list.length === 0) {
    return {
      success: true,
      list: [],
      total: 0,
      pages: 0,
      ...(isStatistics
        ? { statistics: { total: 0, visited: 0, notVisited: 0 } }
        : {}),
      message: "查询成功",
    };
  }

  // 批量获取会员信息
  const customerIds = [...new Set(list.map((item) => item.customerId))];
  const members = await db
    .collection("member")
    .find(
      { _id: { $in: customerIds }, corpId: query.corpId },
      { projection: { _id: 1, name: 1, mobile: 1, inHospitalTimes: 1 } }
    )
    .toArray();

  // 构建会员信息映射
  const memberMap = new Map();
  members.forEach((member) => {
    memberMap.set(member._id, {
      name: member.name,
      mobile: member.mobile,
      inHospitalTimes: member.inHospitalTimes,
    });
  });

  // 补充会员信息
  list.forEach((record) => {
    const memberInfo = memberMap.get(record.customerId);
    if (memberInfo) {
      record.customerName = memberInfo.name;
      record.customerMobile = memberInfo.mobile;
      record.inHospitalTimes = memberInfo.inHospitalTimes;
    }
  });

  // 统计信息
  let statistics = {};
  if (isStatistics) {
    // 统计实际查询记录中有到院记录的客户数量
    const visitedMembers = members.filter(
      (m) =>
        m.inHospitalTimes &&
        Array.isArray(m.inHospitalTimes) &&
        m.inHospitalTimes.length > 0
    );

    statistics = {
      total: total,
      visited: visitedMembers.length,
      notVisited: customerIds.length - visitedMembers.length,
    };
  }

  // 获取项目信息
  await enrichProjectInfo(list, query.corpId);

  return {
    success: true,
    list,
    total,
    pages: Math.ceil(total / pageSize),
    ...(isStatistics ? { statistics } : {}),
    message: "查询成功",
  };
}

// 补充项目信息
async function enrichProjectInfo(list, corpId) {
  const projectIds = list.reduce((ids, item) => {
    if (Array.isArray(item?.projectIds)) {
      ids.push(...item.projectIds);
    }
    return ids;
  }, []);

  if (projectIds.length === 0) {
    list.forEach((item) => {
      item.projectNames = [];
      item.projectDeptIds = [];
    });
    return;
  }

  try {
    const res = await api.getCorpApi({
      type: "getProjectIntentNames",
      corpId,
      ids: [...new Set(projectIds)], // 去重
    });

    if (res?.data?.length) {
      const projectMaps = res.data.reduce(
        (maps, item) => {
          if (item._id) {
            if (item.projectName) maps.nameMap.set(item._id, item.projectName);
            if (item.deptId) maps.deptMap.set(item._id, item.deptId);
          }
          return maps;
        },
        { nameMap: new Map(), deptMap: new Map() }
      );

      list.forEach((item) => {
        if (Array.isArray(item.projectIds)) {
          item.projectNames = item.projectIds
            .map((id) => projectMaps.nameMap.get(id))
            .filter(Boolean);
          item.projectDeptIds = item.projectIds
            .map((id) => projectMaps.deptMap.get(id))
            .filter(Boolean);
        } else {
          item.projectNames = [];
          item.projectDeptIds = [];
        }
      });
    }
  } catch (error) {
    console.error("获取项目信息失败:", error);
    // 如果获取项目信息失败，设置默认值
    list.forEach((item) => {
      item.projectNames = [];
      item.projectDeptIds = [];
    });
  }
}

// 更新网络咨询记录
async function updateEConsuleRecord(params) {
  const data = verfiyParams(params, true);
  if (typeof data === "string") return { success: false, message: data };
  try {
    const res = await db.collection("e-consult-record").updateOne(
      {
        corpId: params.corpId,
        customerId: params.customerId,
        _id: params._id,
      },
      { $set: data }
    );
    await updateLatestEConsuleRecordCustomerInfo(params);
    if (res.matchedCount == 0)
      return { success: false, message: "更新网络咨询记录失败" };
    return { success: true, message: "更新网络咨询记录成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 更新最新一条记录的信息
async function updateLatestEConsuleRecordCustomerInfo(params) {
  const { _id, corpId, customerId, reportDesc, source, projectIds } = params;
  if (!_id || !corpId || !customerId) {
    return { success: false, message: "参数错误" };
  }
  try {
    const latestRecord = await db
      .collection("e-consult-record")
      .find({ corpId, customerId })
      .sort({ createTime: -1 })
      .limit(1)
      .toArray();
    if (latestRecord.length === 0) {
      return { success: false, message: "未找到对应的网络咨询记录" };
    }
    if (latestRecord[0]._id !== params._id) {
      return { success: false, message: "不是最新的网络咨询记录" };
    }
    const updateData = {
      reportDesc: reportDesc ? reportDesc.trim() : "",
      infoSource: source || [],
      projectIds: Array.isArray(projectIds) ? projectIds : [],
      updateTime: Date.now(),
    };
    const res = await db
      .collection("member")
      .updateOne({ _id: customerId, corpId }, { $set: updateData });
    if (res.matchedCount === 0) {
      return { success: false, message: "未找到匹配的记录" };
    }
    return { success: true, message: "更新成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 获取第一条网络咨询记录
async function getFirstEConsuleRecord(ctx) {
  const { corpId, customerId } = ctx;
  if (!corpId || !customerId) return { success: false, message: "参数错误" };
  try {
    const record = await db
      .collection("e-consult-record")
      .find({ corpId, customerId })
      .sort({ timestamp: 1, createTime: 1 })
      .project({ createTime: 1, userId: 1 })
      .limit(1)
      .toArray();
    if (record.length)
      return { success: true, record: record[0], message: "查询成功" };
    return { success: false, message: "未查询到网络咨询记录" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 验证参数
function verfiyParams(params, requiredId) {
  if (!params.corpId) return "机构id不能为空";
  // if (!requiredId && !params.userId) return "登记人不能为空";
  if (requiredId && !params._id) return "记录id不能为空";
  if (typeof params.customerId != "string" || params.customerId.trim() === "")
    return "客户id不能为空";
  if (!Array.isArray(params.source) || params.source.length === 0)
    return "信息来源不能为空";
  if (params.projectIds === 0) return "项目不能为空";
  if (!params.date || !dayjs(params.date).isValid()) return "日期不能为空";
  if (dayjs().isBefore(dayjs(params.date))) return "日期不能大于当前日期";
  const data = {
    source: params.source,
    projectIds: params.projectIds,
    date: dayjs(params.date).format("YYYY-MM-DD HH:mm"),
    timestamp: dayjs(params.date).valueOf(),
    reportDesc:
      typeof params.reportDesc === "string" ? params.reportDesc.trim() : "",
  };
  if (requiredId) data.updateTime = Date.now();
  else data.createTime = Date.now();
  return data;
}

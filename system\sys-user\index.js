const common = require("../../common.js");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getUserInfo":
      return await this.getUserInfo(content);
    case "getCorpUsers":
      return await this.getCorpUsers(content);
    case "getMenuList":
      return await this.getMenuList(content);
  }
};

// 获取用户信息
exports.getUserInfo = async (event) => {
  try {
    const item = await getUser(event.userId);
    if (item.length === 0) {
      return {
        success: true,
        message: "获取成功",
        data: "",
      };
    }
    let user = item[0];
    // 获取角色信息
    let role = await getRoleInfo(user);
    user["roleList"] = role;
    user["teamId"] = await getTeamListByuserId(event);
    return {
      success: true,
      message: "获取成功",
      data: user,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 获取公司用户
exports.getCorpUsers = async (context) => {
  const { corpId, userIds } = context;
  if (!corpId || !Array.isArray(userIds))
    return { success: false, message: "查询失败" };
  if (userIds.length === 0)
    return { success: true, message: "查询成功", list: [] };
  try {
    const data = await db
      .collection("corp-member")
      .find({ corpId, userid: { $in: userIds } })
      .project({
        anotherName: 1,
        avatar: 1,
        job: 1,
        userid: 1,
      })
      .toArray();
    return { success: true, message: "查询成功", list: data };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 根据用户ID获取团队列表
async function getTeamListByuserId(event) {
  const { userId, corpId } = event;
  const data = await db
    .collection("team")
    .find({
      corpId,
      memberList: userId,
    })
    .toArray();
  return data.map((item) => item.teamId);
}

// 获取系统成员
async function getUser(userId) {
  return await db
    .collection("corp-member")
    .find({
      userid: userId,
    })
    .toArray();
}

// 根据角色Id获取用户所属的角色
async function getRoleInfo(item) {
  return await db
    .collection("sys-role")
    .find({
      _id: { $in: item.roleIds },
    })
    .toArray();
}

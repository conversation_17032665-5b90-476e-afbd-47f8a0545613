const common = require("../../common");
let db = null;

exports.main = async (context, mongodb) => {
  db = mongodb;
  switch (context.type) {
    case "addProjectCate":
      return await addProjectCate(context);
    case "updateProjectCate":
      return await updateProjectCate(context);
    case "deleteProjectCate":
      return await deleteProjectCate(context);
    case "getProjectCateList":
      return await getProjectCateList(context);
    case "sortProjectCate":
      return await sortProjectCate(context);
    case "getDeptProjectCate":
      return await getDeptProjectCate(context);
    case 'getProjectAllCateGroup':
      return await getProjectAllCateGroup(context);
  }
};

async function addProjectCate(context) {
  let { corpId, label, parentId, deptId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    if (parentId) {
      const parent = await db
        .collection("project-cate")
        .findOne({ _id: parentId, corpId });
      if (!parent) return { success: false, message: "父级分类不存在" };
      if (![1, 2, 3].includes(parent.level))
        return { success: false, message: "父级分类层级错误" };
      const parentGroup = [...(parent.parentGroup || []), parent._id];
      const res = await db.collection("project-cate").insertOne({
        _id: common.generateRandomString(24),
        corpId,
        label,
        level: parent.level + 1,
        parentId,
        createTime: Date.now(),
        parentGroup,
      });
      return { success: true, data: res, message: "添加分类成功" };
    }
    const payload = {
      _id: common.generateRandomString(24),
      corpId,
      label,
      level: 1,
      createTime: Date.now(),
      parentGroup: [],
    };
    payload.deptId =
      typeof deptId === "string" && deptId.trim() !== "" ? deptId.trim() : "";
    if (payload.deptId) {
      const existCate = await db
        .collection("project-cate")
        .findOne(
          { corpId, deptId: payload.deptId },
          { projection: { label: 1 } }
        );
      if (existCate)
        return {
          success: false,
          message: `当前科室已经关联项目分类【${existCate.label}】`,
        };
    }
    const res = await db.collection("project-cate").insertOne(payload);
    return { success: true, data: res, message: "添加分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function updateProjectCate(context) {
  let { corpId, id: _id, label, deptId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    const cate = await db.collection("project-cate").findOne({ _id, corpId });
    if (cate) {
      const payload = { label: label.trim() };
      if (cate.level === 1 && typeof deptId === "string") {
        payload.deptId = deptId.trim();
        if (deptId.trim()) {
          const existCate = await db
            .collection("project-cate")
            .findOne(
              { corpId, deptId: deptId.trim(), _id: { $ne: _id } },
              { projection: { label: 1 } }
            );
          if (existCate)
            return {
              success: false,
              message: `当前科室已经关联项目分类【${existCate.label}】`,
            };
        }
      }
      await db
        .collection("project-cate")
        .updateOne({ _id, corpId }, { $set: payload });
      if (cate.level === 1 && cate.deptId !== payload.deptId) {
        await updateProjectBillDepts(
          corpId,
          cate._id,
          payload.deptId,
          cate.deptId
        );
      }
      return { success: true, message: "更新分类成功" };
    }
    return { success: false, message: "分类不存在" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function deleteProjectCate(context) {
  let { corpId, id: _id } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  try {
    const cate = await db.collection("project-cate").findOne({ _id, corpId });
    if (!cate) return { success: false, message: "分类不存在" };
    const parentGroup = [...(cate.parentGroup || []), _id];
    const childrenQuery = parentGroup.reduce(
      (acc, cur, idx) => {
        acc[`parentGroup.${idx}`] = cur; // 查询子分类
        return acc;
      },
      { corpId }
    );
    const query = { $or: [{ corpId, _id }, childrenQuery] };
    const list = await db
      .collection("project-cate")
      .find(query, { projection: { _id: 1 } })
      .toArray();
    const ids = list.map((i) => i._id);
    const res2 = await db
      .collection("project-list")
      .updateMany(
        { corpId, projectCateIdGroup: { $in: ids } },
        { $pull: { projectCateIdGroup: { $in: ids } } }
      );
    const res = await db
      .collection("project-cate")
      .deleteMany({ corpId, _id: { $in: ids } });
    return {
      success: true,
      data: {
        cateDeleted: res.deletedCount,
        projectDeleted: res2.modifiedCount,
      },
      message: "删除分类成功",
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function getProjectCateList(context) {
  let { corpId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const list = await db
      .collection("project-cate")
      .find({ corpId, init: { $exists: false } })
      .sort({ createTime: 1 })
      .limit(10000)
      .toArray();
    if (list.length) return { success: true, list, message: "获取分类成功" };
    const initRecord = await db
      .collection("project-cate")
      .findOne({ corpId, init: true });
    if (initRecord) return { success: true, list: [], message: "获取分类成功" };
    await db.collection("project-cate").insertOne({ corpId, init: true });
    await db
      .collection("project-cate")
      .insertOne({ corpId, label: "默认", level: 1, createTime: Date.now() });
    const newlist = await db
      .collection("project-cate")
      .find({ corpId, init: { $exists: false } })
      .sort({ createTime: 1 })
      .limit(10000)
      .toArray();
    return { success: true, list: newlist, message: "获取分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function sortProjectCate(context) {
  const { corpId, sortData } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  const arr = Array.isArray(sortData)
    ? sortData.filter(
      (i) =>
        i._id && typeof i.sort === "number" && i.sort % 1 === 0 && i.sort >= 0
    )
    : [];
  if (arr.length === 0) return { success: false, message: "参数错误" };

  const bulkOps = arr.map((item) => ({
    updateOne: {
      filter: { _id: item._id, corpId, parentId: { $exists: false } },
      update: { $set: { sort: item.sort } },
    },
  }));

  try {
    const res = await db.collection("project-cate").bulkWrite(bulkOps);
    if (res.modifiedCount === arr.length) {
      return { success: true, message: "操作成功" };
    } else {
      return { success: false, message: "部分更新失败" };
    }
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function updateProjectBillDepts(
  corpId,
  cateId,
  newBillDeptId,
  oldBillDeptId
) {
  try {
    const query1 = { corpId, _id: cateId };
    const query2 = { corpId, "parentGroup.0": cateId };
    const cates = await db
      .collection("project-cate")
      .find({ $or: [query1, query2] })
      .project({ _id: 1 })
      .limit(10000)
      .toArray();
    const cateIds = cates.map((i) => i._id);
    if (oldBillDeptId) {
      await db.collection("project-list").updateMany(
        {
          corpId,
          projectCateIdGroup: { $in: cateIds },
          billDeptIds: { $exists: true },
        },
        { $pull: { billDeptIds: oldBillDeptId } }
      );
    }
    if (newBillDeptId) {
      await db.collection("project-list").updateMany(
        {
          corpId,
          projectCateIdGroup: { $in: cateIds },
          billDeptIds: { $exists: true },
        },
        { $addToSet: { billDeptIds: newBillDeptId } }
      );
      await db.collection("project-list").updateMany(
        {
          corpId,
          projectCateIdGroup: { $in: cateIds },
          billDeptIds: { $exists: false },
        },
        { $set: { billDeptIds: [newBillDeptId] } }
      );
    }
    return { success: true, message: "操作成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}


async function getDeptProjectCate(ctx) {
  const { corpId, deptId } = ctx;
  if (!corpId || typeof deptId !== "string" || deptId.trim() === '') return { success: false, message: "参数错误" };
  try {
    const list = await db
      .collection("project-cate")
      .find({ corpId, deptId, level: 1 }, { projection: { _id: 1 } })
      .toArray();
    return { success: true, list, message: "获取分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function getProjectAllCateGroup(ctx) {
  const { cateIds, corpId, firstCateIds } = ctx;
  if (!corpId || !cateIds || cateIds.length === 0) return { success: false, message: "参数错误" };
  try {
    const query = { _id: { $in: cateIds } }
    if (Array.isArray(firstCateIds)) query['parentGroup.0'] = { $in: firstCateIds };
    const data = await db.collection("project-cate").aggregate([
      { $match: query },
      {
        // 使用 $graphLookup 来进行递归查询
        $graphLookup: {
          from: "project-cate",           // 目标集合
          startWith: "$parentGroup", // 起始点是当前项的 parentIds 数组
          connectFromField: "_id",  // 父项 ID 字段
          connectToField: "_id",    // 当前项的 ID 字段
          as: "parents"            // 递归结果保存到 parents 数组中
        }
      },
      {
        // 只保留我们需要的字段
        $project: {
          _id: 1,
          label: 1,
          level: 1,
          parentGroup: 1,
          parents: {
            $concatArrays: [
              {
                $map: {
                  input: "$parents",  // 对父项进行映射
                  as: "parent",       // 为每个父项创建一个 alias
                  in: {              // 创建包含父项的 _id 和 label 的新对象
                    _id: "$$parent._id",
                    label: "$$parent.label",
                    level: "$$parent.level"
                  }
                }
              }
            ]
          }
        }
      }
    ]).toArray();
    return { success: true, list: data, message: "获取分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }

}

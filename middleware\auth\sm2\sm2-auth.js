const smCrypto = require('sm-crypto');

const sm2AuthMiddleware = (req, res, next) => {
  const { client_id, signature, data, timestamp, nonce } = req.body;

  // 1. 基本参数校验
  if (!client_id || !signature || !data || !timestamp || !nonce) {
    return res.status(400).json({ code: 400, message: '缺少必要参数' });
  }

  // 2. 时效性检查（5分钟内有效）
  const now = Date.now();
  if (Math.abs(now - parseInt(timestamp)) > 5 * 60 * 1000) {
    return res.status(400).json({ code: 400, message: '请求已过期' });
  }

  // 3. 获取客户端公钥
  const client = clientCerts.get(client_id);
  if (!client || !client.publicKey) {
    return res.status(401).json({ code: 401, message: '客户端未注册' });
  }

  try {
    // 4. 验证签名
    const verifyResult = smCrypto.sm2.doVerifySignature(
      signature,
      JSON.stringify({ data, timestamp, nonce }),
      client.publicKey
    );

    if (verifyResult) {
      next(); // 验证通过
    } else {
      res.status(401).json({ code: 401, message: '签名验证失败' });
    }
  } catch (error) {
    res.status(500).json({ code: 500, message: '验签处理异常' });
  }
};

exports.sm2AuthMiddleware = sm2AuthMiddleware;
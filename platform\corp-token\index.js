const dayjs = require("dayjs");
const request = require("../request");

exports.getProviderAccessToken = async () => {
  let url = `https://qyapi.weixin.qq.com/cgi-bin/service/get_provider_token`;
  let params = {
    corpid: "wwe3fb2faa52cf9dfb",
    provider_secret: "v5sv-M56Amgik91OewBIzcq6ptbnlkC-QUisBq3UNErDF6Yyxd4BgBRea0_636se",
  };
  let { provider_access_token } = await request.main(url, params, "POST");
  return provider_access_token;
};

exports.getCorpToken = async (suite_access_token, corpid, permanentCode) => {
  let url = `https://qyapi.weixin.qq.com/cgi-bin/service/get_corp_token?suite_access_token=${suite_access_token}`;
  let params = {
    auth_corpid: corpid,
    permanent_code: permanentCode,
  };
  let res = await request.main(url, params, "POST");
  return res.access_token;
};

const sourceCate = require("./information-source-cate");
const { ObjectId } = require("mongodb"); // 添加 ObjectId 导入
const dayjs = require("dayjs");
let db = null;

exports.main = async (content, mongodb) => {
  db = mongodb;
  switch (content.type) {
    case "addSource":
      return await exports.addSource(content);
    case "updateSource":
      return await exports.updateSource(content);
    case "updateSourceStatus":
      return await exports.updateSourceStatus(content);
    case "getSourceList":
      return await exports.getSourceList(content);
    case "addSourceCate":
    case "updateSourceCate":
    case "deleteSourceCate":
    case "getSourceCateList":
    case "sortSourceCate":
      return await sourceCate.main(content, db);
  }
};

// 新增来源
exports.addSource = async (content) => {
  const { corpId, params } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const res = await db.collection("information-source-list").insertOne({
      corpId,
      ...params,
      sourceStatus: "enable",
      createTime: dayjs().valueOf(),
    });
    return { success: true, message: "新增成功", data: res.insertedId };
  } catch (e) {
    return { success: false, message: "新增失败" };
  }
};

// 更新来源
exports.updateSource = async (content) => {
  const { corpId, params, id } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    await db
      .collection("information-source-list")
      .updateOne({ _id: new ObjectId(id) }, { $set: params });
    return { success: true, message: "更新成功" };
  } catch (error) {
    return { success: false, message: "更新失败" };
  }
};

// 更新来源状态
exports.updateSourceStatus = async (content) => {
  const { corpId, id, sourceStatus } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!id) return { success: false, message: "来源id不能为空" };
  if (!sourceStatus || !["enable", "disable"].includes(sourceStatus))
    return { success: false, message: "状态参数错误" };

  try {
    await db
      .collection("information-source-list")
      .updateOne({ _id: new ObjectId(id), corpId }, { $set: { sourceStatus } });
    return { success: true, message: "状态更新成功" };
  } catch (error) {
    return { success: false, message: "状态更新失败" };
  }
};

// 获取来源列表 分页
exports.getSourceList = async (content) => {
  const { corpId, cateIds, page, pageSize, sourceStatus, ids, searchKey } =
    content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  let query = { corpId };
  if (searchKey) {
    query.$or = [
      { sourceName: { $regex: searchKey, $options: "i" } },
      { pinyin: { $regex: searchKey, $options: "i" } },
    ];
  }

  if (Array.isArray(cateIds)) query.sourceCateIdGroup = { $in: cateIds };
  if (sourceStatus) query.sourceStatus = sourceStatus;
  if (Array.isArray(ids))
    query._id = { $in: ids.map((id) => new ObjectId(id)) };
  try {
    const total = await db
      .collection("information-source-list")
      .find(query)
      .count();
    const pages = Math.ceil(total / pageSize);
    const data = await db
      .collection("information-source-list")
      .aggregate([
        { $match: query },
        { 
          $sort: { 
            sourceStatus: -1, 
            createTime: -1 
          } 
        },
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
      ])
      .toArray();
    return { success: true, message: "获取成功", list: data, total, pages };
  } catch (e) {
    return { success: false, message: e.message || "获取失败" };
  }
};

const utils = require("../utils");
const qrcodeStatistics = require("./qrcodeStatistics");
const staffqrcodeCate = require("./staff-qrcode-cate");
const dayjs = require("dayjs");
const common = require("../../common");
const api = require("../../api");
let db = null;
let currentCorpId = "";
exports.main = async (content, DB) => {
  db = DB;
  currentCorpId = content.corpId;
  switch (content.type) {
    case "addStaffQrcode":
      return await exports.addStaffQrcode(content);
    case "getStaffQrcode":
      return await exports.getStaffQrcode(content);
    case "getStaffQrcodeByQrcodeId":
      return await exports.getStaffQrcodeByQrcodeId(content);
    case "removeStaffQrcode":
      return await exports.removeStaffQrcode(content);
    case "updateStaffQrcode":
      return await exports.updateStaffQrcode(content);
    case "getStaffQrCodeDetail":
      return await exports.getStaffQrCodeDetail(content);
    case "getPersonalQrCode":
      return await exports.getPersonalQrCode(content);
    case "getPersonalQrcodeList":
      return await exports.getPersonalQrcodeList(content);
    case "getStaffQrcodeCount":
      return await getStaffQrcodeCount(content);
    case "addStaffQrcodeCate":
    case "updateStaffQrcodeCate":
    case "deleteStaffQrcodeCate":
    case "getStaffQrcodeCateList":
    case "initCorpStaffQrcodeCate":
    case "sortStaffQrcodeCate":
      return await staffqrcodeCate.main(content, db);
  }
};

exports.getStaffQrcodeByQrcodeId = async (content) => {
  const { qrcodeId, corpId } = content;
  const qrCodeInfo = await db.collection("staff-qrcode").findOne({
    qrcodeId,
    corpId,
  });
  return {
    success: true,
    data: qrCodeInfo,
  };
};

exports.addStaffQrcode = async (context) => {
  let { params } = context;
  const {
    corpUserId,
    qrCodetype = 1,
    corpId,
    tagIds,
    qrCodeName,
    customerSource,
    welcomContent,
    skipVerify,
    fileList,
    qrCodeStatus,
    creator,
    qrcodeId,
    staffQrcodeType,
    archiveLink,
    autoCreateCustomer,
    qrCode,
    configId,
    cateId,
    headerImage,
  } = params;
  try {
    const query = {
      _id: common.generateRandomString(24),
      corpId,
      corpUserId,
      qrCodetype,
      qrCode,
      configId,
      tagIds,
      qrCodeName,
      customerSource,
      welcomContent,
      skipVerify,
      fileList,
      qrCodeStatus,
      createTime: new Date().getTime(),
      creator,
      qrcodeId,
      staffQrcodeType,
      headerImage,
      cateId,
      autoCreateCustomer:
        typeof autoCreateCustomer === "boolean" ? autoCreateCustomer : false,
    };
    if (archiveLink) query.archiveLink = archiveLink;
    await db.collection("staff-qrcode").insertOne(query);
    return {
      success: true,
      message: "添加成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "添加失败",
    };
  }
};

exports.getStaffQrcode = async (context) => {
  try {
    let { page, pageSize, params } = context;
    page = parseInt(page) > 0 ? parseInt(page) : 1;
    pageSize = parseInt(pageSize) > 0 ? parseInt(pageSize) : 10;
    const { corpId, qrCodeName, qrCodetype, qrCodeStatus } = params || {};
    const cateIds = Array.isArray(params.cateIds) ? params.cateIds : [];
    if (!corpId) return { success: false, message: "机构id不能为空" };

    // MongoDB 查询条件
    const query = {
      staffQrcodeType: { $ne: "personal" },
      corpId,
      cateId: { $in: cateIds },
    };

    // 添加可选条件
    if (typeof qrCodeName === "string" && qrCodeName.trim()) {
      query.qrCodeName = {
        $regex: ".*" + qrCodeName.trim() + ".*",
        $options: "i",
      };
    }
    if (qrCodetype) query.qrCodetype = qrCodetype;
    if (qrCodeStatus) query.qrCodeStatus = qrCodeStatus;

    // 时间戳
    const todayStartTimestamp = dayjs().startOf("day").valueOf();
    const todayEndTimestamp = dayjs().endOf("day").valueOf();

    // 获取总数和分页数据
    const totalQuery = db.collection("staff-qrcode").countDocuments(query);
    const listQuery = db
      .collection("staff-qrcode")
      .aggregate([
        { $match: query },
        {
          $lookup: {
            from: "qrcode-statistics",
            localField: "configId",
            foreignField: "configId",
            as: "addList",
          },
        },
        {
          $addFields: {
            allAddCustomerCount: { $size: "$addList" },
            todayAddCustomerCount: {
              $size: {
                $filter: {
                  input: "$addList",
                  as: "item",
                  cond: {
                    $and: [
                      { $gt: ["$$item.createTime", todayStartTimestamp] },
                      { $lt: ["$$item.createTime", todayEndTimestamp] },
                    ],
                  },
                },
              },
            },
          },
        },
        { $project: { addList: 0 } },
        { $sort: { createTime: -1 } },
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
      ])
      .toArray();

    const [total, list] = await Promise.all([totalQuery, listQuery]);
    const pages = Math.ceil(total / pageSize);

    return { data: list, total, pages, size: pageSize, success: true };
  } catch (error) {
    return { success: false, message: error.message };
  }
};
exports.removeStaffQrcode = async (context) => {
  let { id, params } = context;
  const { corpId, configId, allAddCustomerCount } = params;
  try {
    if (allAddCustomerCount > 0) {
      await db
        .collection("staff-qrcode")
        .updateOne({ _id: id }, { $set: { qrCodeStatus: "disable" } });
      const res = await api.getWecomApi({
        type: "delContactWay",
        corpId,
        configId,
      });
      return res;
    } else {
      await db.collection("staff-qrcode").deleteOne({ _id: id });
      return {
        success: true,
        message: "删除成功",
      };
    }
  } catch (error) {
    return {
      success: false,
      message: "删除失败",
    };
  }
};

exports.updateStaffQrcode = async (context) => {
  let { id, params } = context;
  params["updateTime"] = new Date().getTime();
  if ("archiveLink" in params && !params.archiveLink) {
    params.archiveLink = null;
  }
  await db.collection("staff-qrcode").updateOne({ _id: id }, { $set: params });
  for (let key in params) {
    if (!params[key]) {
      delete params[key];
    }
  }
  await api.getWecomApi({
    type: "updateContactWay",
    params,
    corpId: currentCorpId,
  });
  return {
    success: true,
    message: "更新成功",
  };
};

exports.getStaffQrCodeDetail = async (context) => {
  let { id } = context;
  const item = await db.collection("staff-qrcode").findOne({ _id: id });
  let res = await qrcodeStatistics.main(
    {
      type: "getAllQrcodeCustomerCount",
      item,
      corpId: currentCorpId,
    },
    db
  );
  const { allAddCustomerCount, todayAddCustomerCount } = res.data;
  item.allAddCustomerCount = allAddCustomerCount;
  item.todayAddCustomerCount = todayAddCustomerCount;
  return {
    success: true,
    message: "获取成功",
    data: item,
    res,
  };
};

exports.getPersonalQrCode = async (context) => {
  let { corpUserId, corpId, creator } = context;
  let qrcode = {};
  async function createStaffQrcode(corpUserId, corpId) {
    const data = await db.collection("staff-qrcode").findOne({
      corpUserId,
      corpId,
      staffQrcodeType: "personal",
      qrCodeStatus: "enable",
    });
    return data;
  }
  try {
    qrcode = await createStaffQrcode(corpUserId, corpId);
    const DEFAULT_WELCOME_MESSAGE =
      "你好，很高兴能与您建立联系。接下来，我会为您提供专业的健康咨询和贴心的服务。为了更好的为您提供一对一的精致服务，请您发送一下您的真实姓名和联系方式。感谢您的信任！";
    if (!qrcode) {
      let params = {
        corpUserId: [corpUserId],
        qrCodetype: 1,
        corpId,
        qrCodeName: "个人二维码",
        skipVerify: 1,
        qrCodeStatus: "enable",
        creator: creator || corpUserId,
        qrcodeId: `q&${utils.getRandomStr(5)}`,
        tagIds: [],
        fileList: [],
        welcomContent: DEFAULT_WELCOME_MESSAGE,
        customerSource: "",
        staffQrcodeType: "personal",
      };
      let result = await api.getWecomApi({
        type: "addContactWay",
        corpId,
        corpUserId,
        qrCodetype: 1,
        qrcodeId: params.qrcodeId,
      });
      if (!result.success)
        return {
          success: false,
          message: result.message,
        };
      params["configId"] = result.configId;
      params["qrCode"] = result.data;
      const { success, message } = await this.addStaffQrcode({ params });
      if (!success) return { success, message };
      qrcode = await createStaffQrcode(corpUserId, corpId);
    }
    return {
      success: true,
      message: "获取成功",
      data: qrcode,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.getPersonalQrcodeList = async (context) => {
  try {
    let { params } = context;
    const { corpId, userIds, qrCodeStatus } = params || {};
    if (!corpId) return { success: false, message: "机构id不能为空" };
    const query = {
      staffQrcodeType: "personal",
      qrCodetype: 1,
      corpId,
    };
    if (Array.isArray(userIds) && userIds.length)
      query.corpUserId = { $in: userIds };
    if (qrCodeStatus) query.qrCodeStatus = qrCodeStatus;
    const todayStartTimestamp = dayjs().startOf("day").valueOf();
    const todayEndTimestamp = dayjs().endOf("day").valueOf();
    const data = await db
      .collection("staff-qrcode")
      .aggregate([
        { $match: query },
        { $limit: 1000 },
        {
          $lookup: {
            from: "qrcode-statistics",
            localField: "configId",
            foreignField: "configId",
            as: "addList",
          },
        },
        {
          $addFields: {
            allAddCustomerCount: { $size: "$addList" },
            todayAddCustomerCount: {
              $size: {
                $filter: {
                  input: "$addList",
                  as: "item",
                  cond: {
                    $and: [
                      { $gt: ["$$item.createTime", todayStartTimestamp] },
                      { $lt: ["$$item.createTime", todayEndTimestamp] },
                    ],
                  },
                },
              },
            },
          },
        },
        { $project: { addList: 0 } },
        { $sort: { createTime: -1 } },
      ])
      .toArray();
    return { data, success: true };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

async function getStaffQrcodeCount(context) {
  const { corpId, cateIds } = context;
  if (!corpId) return { success: false, msg: "缺少corpId参数" };
  if (!Array.isArray(cateIds))
    return { success: false, msg: "缺少cateIds参数" };
  try {
    const total = await db
      .collection("staff-qrcode")
      .find({
        corpId,
        cateId: { $in: cateIds },
      })
      .count();
    return { success: true, data: total };
  } catch (e) {
    return { success: false, msg: e.message };
  }
}

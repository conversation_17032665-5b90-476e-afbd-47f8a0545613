// 院区表索引创建脚本
// 使用数据库：corp
// 集合：hlw-hospital-area

// 为院区ID和机构ID创建复合唯一索引，确保在同一机构内院区ID唯一
db.getCollection("hlw-hospital-area").createIndex(
  { corpId: 1, areaId: 1 },
  { unique: true, name: "idx_corpId_areaId_unique" }
);

// 为院区名称和机构ID创建复合唯一索引，确保在同一机构内院区名称唯一
db.getCollection("hlw-hospital-area").createIndex(
  { corpId: 1, areaName: 1 },
  { unique: true, name: "idx_corpId_areaName_unique" }
);

// 为机构ID创建索引，提高查询效率
db.getCollection("hlw-hospital-area").createIndex(
  { corpId: 1 },
  { name: "idx_corpId" }
);

// 为状态字段创建索引，提高按状态筛选的查询效率
db.getCollection("hlw-hospital-area").createIndex(
  { status: 1 },
  { name: "idx_status" }
);

// 为创建时间创建索引，提高按时间排序的查询效率
db.getCollection("hlw-hospital-area").createIndex(
  { createTime: -1 },
  { name: "idx_createTime_desc" }
);

// 为更新时间创建索引
db.getCollection("hlw-hospital-area").createIndex(
  { updateTime: -1 },
  { name: "idx_updateTime_desc" }
);

// 复合索引：机构ID + 状态，用于常见的查询组合
db.getCollection("hlw-hospital-area").createIndex(
  { corpId: 1, status: 1 },
  { name: "idx_corpId_status" }
);

// 为排序字段创建索引，提高按排序查询的效率
db.getCollection("hlw-hospital-area").createIndex(
  { sort: 1 },
  { name: "idx_sort" }
);

// 复合索引：机构ID + 排序，用于按机构排序查询
db.getCollection("hlw-hospital-area").createIndex(
  { corpId: 1, sort: 1 },
  { name: "idx_corpId_sort" }
);

// 复合索引：机构ID + 状态 + 排序，用于列表查询优化
db.getCollection("hlw-hospital-area").createIndex(
  { corpId: 1, status: 1, sort: 1 },
  { name: "idx_corpId_status_sort" }
);

console.log("院区表索引创建完成");

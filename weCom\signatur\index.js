const request = require("../request");
const { getJsApiSignature } = require("@wecom/crypto");
const accessToken = require("../token");
const logger = require("../../utils/logger");
const api = require("../../api");
//获取企业签名
/**
 *
 * @param {*} permanentCode  // 永久授权码
 * @param {*} corpid   // 企业ID
 * @param {*} url   // 域名
 * @returns
 */
exports.getSignstrOfjsapi = async (context) => {
  // 获取access_token
  const { corpId } = context;
  if (!corpId) {
    return {
      success: false,
      messsage: "机构Id未传",
    };
  }
  let access_token = await accessToken.getToken(context);
  // 获取企业jsapi_tickt
  let corp_tickt = await getCorpTicket(corpId, access_token);
  // 获取应用jsapi_tickt
  let app_tickt = await getAppTicket(corpId, access_token);
  // 获取企业的签名
  return setSignStr(corp_tickt, app_tickt, context.url);
};
//获取企业的jsapi_ticket
async function getCorpTicket(corpId, access_token) {
  const { success, data } = await api.getCorpApi({
    corpId,
    cacheType: "corpTicket",
    type: "getCorpCache",
  });
  if (success) return data.value;
  let url = `https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=${access_token}`;
  let { ticket, expires_in } = await request.main(url, null, "GET");
  await api.getCorpApi({
    corpId,
    value: ticket,
    expiresIn: expires_in,
    cacheType: "corpTicket",
    type: "createCorpCache",
    id: data ? data._id : "",
  });
  return ticket;
}
// 获取应用的jsapi_ticket
async function getAppTicket(corpId, access_token) {
  const { success, data } = await api.getCorpApi({
    corpId,
    cacheType: "appTicket",
    type: "getCorpCache",
  });
  if (success) return data.value;
  let url = `https://qyapi.weixin.qq.com/cgi-bin/ticket/get?access_token=${access_token}&type=agent_config`;
  let { ticket, expires_in } = await request.main(url, null, "GET");
  await api.getCorpApi({
    corpId,
    value: ticket,
    expiresIn: expires_in,
    cacheType: "appTicket",
    type: "createCorpCache",
    id: data ? data._id : "",
  });
  return ticket;
}
// 获取企业的签名
async function setSignStr(corp_ticket, app_ticket, url) {
  return new Promise((reslove) => {
    let corp_options = {
      url: url,
      /** 用于签名的 jsapi ticket */
      ticket: corp_ticket,
      /** 生成签名的随机串 */
      nonceStr: generateRandomString(16),
      /** 生成签名的时间戳 */
      timestamp: Math.floor(Date.now() / 1000),
    };
    let app_options = {
      url: url,
      /** 用于签名的 jsapi ticket */
      ticket: app_ticket,
      /** 生成签名的随机串 */
      nonceStr: generateRandomString(16),
      /** 生成签名的时间戳 */
      timestamp: Math.floor(Date.now() / 1000),
    };
    const corpSign = getJsApiSignature(corp_options);
    const appSign = getJsApiSignature(app_options);
    logger.info("corpSign", corpSign);
    reslove({ corpSign, appSign,success: true });
  });
}

function generateRandomString(length) {
  let result = "";
  let characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

const axios = require('axios');
exports.post = async (url, params) => {
  // 发送POST请求
  console.log(`请求URL: ${url}`);
  try {
    let options = {
      json: true,
      headers: { 'Content-Type': 'application/json' },
    };
    const response = await axios.post(url, params, options);
    console.log(`请求成功: ${JSON.stringify(response.data)}`);
    return response.data;
  } catch (error) {
    console.log(`请求失败: ${error}`);
    console.error(error);
    return false;
  }
};

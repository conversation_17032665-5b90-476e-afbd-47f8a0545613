const dayjs = require("dayjs");
const common = require("../../common");
let db = null;
exports.main = async (event, mongodb) => {
  db = mongodb;
  switch (event.type) {
    case "getCorpInfo":
      return await getCorpInfo(event);
    case "getCustomCorpInfo":
      return await getCustomCorpInfo(event);
    case "addCorpDisease":
      return await addCorpDisease(event);
    case "updateCorp":
      return await updateCorp(event);
    case "addCorp":
      return await addCorp(event);
    case "getCorpList":
      return await getCorpList(event);
  }
  await client.close();
};

async function getCorpList(event) {
  const { corpId } = event;
  try {
    let query = {};
    if (corpId) {
      query.corpId = corpId;
    }
    let data = await db.collection("corp").find(query).toArray();
    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch {
    return {
      success: false,
      message: "获取失败",
    };
  }
}

async function getCorpInfo(event) {
  const { corpId } = event;
  try {
    let data = await db.collection("corp").find({ corpId }).toArray();
    let [corp] = data;
    if (corp && corp.package) {
      const { packageEndTime } = corp.package;
      const isAfterToday = dayjs(packageEndTime).isAfter(dayjs(), "day");
      if (!isAfterToday) {
        corp.packageStatus = "expire";
      }
    }
    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch {
    return {
      success: false,
      message: "获取失败",
    };
  }
}

async function getCustomCorpInfo(event) {
  const { corpId, fields } = event;
  if (!corpId || !Array.isArray(fields) || fields.length === 0) {
    return { success: false, message: "参数错误" };
  }
  const project = fields.reduce(
    (acc, val) => {
      if (typeof val === "string" && val.trim()) {
        acc[val] = 1;
      }
      return acc;
    },
    { _id: 0, corp_name: 1 }
  );
  try {
    let corp = await db
      .collection("corp")
      .findOne({ corpId }, { projection: project });
    return {
      success: Boolean(corp),
      message: "获取成功",
      data: corp,
    };
  } catch {
    return {
      success: false,
      message: "获取失败",
    };
  }
}

async function addCorpDisease(event) {
  let { corpId, disease, oldDisease } = event;
  try {
    if (oldDisease) {
      await db
        .collection("corp")
        .updateOne({ corpId }, { $pull: { diseases: oldDisease } });
    }
    await db
      .collection("corp")
      .updateOne(
        { corpId },
        { $push: { diseases: { $each: [disease], $position: 0 } } }
      );
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      message: error.message,
    };
  }
}

async function updateCorp(event) {
  let { corpId, params } = event;
  try {
    let res = await db
      .collection("corp")
      .updateOne({ corpId }, { $set: params });
    return {
      success: true,
      message: "更新成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
}

async function addCorp(event) {
  let { params } = event;
  try {
    let res = await db.collection("corp").insertOne({
      ...params,
      _id: common.generateRandomString(24),
    });
    return {
      success: true,
      message: "添加成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
}

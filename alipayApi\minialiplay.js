
let alipay = require("alipay-sdk");
const AlipaySdkConfig = {
  appId: "2021005110656858",
  privateKey: 'MIIEowIBAAKCAQEAkzRKNNt5x0nsmF7rzO+CZUSMNlOlMK2OVmZwJrAm8ERZH1BhW4ZG+o/7J75wP2FIbnEO+pjMM+8cXMc5loldEeGJwV9a4OOMrVbQp/EELZC1QRztbte0sZucpLQ1OEU+XKfBBiBRb3TWlq+6plOqARmW9OdtlFPq0DEX/Ol6LCxDgB5b9hMyc8Dn6ZeBLQ/OTqYprbY4DcV45m1kJrsmnk7KLIbzGi28D4YzFXQXkPG1cDakXXRYTvF3T9ZbfjKmLfMKnnQO3jWe9B+AsqQUuA5t44bgzvubNBv1JgGWfNRltFVrMRyr9vLQepf1bebod3iUQk62+dJ72uN6EC08fwIDAQABAoIBAE9rILYPy03n5kv2ukVg2vsD1png2vi+SlL3BX75/qgWV/b7COu2MEkysRAqQKkRNWDQgoJVOaazVJwBqGI9Tgc9pXK8nW1DlzxV4FEbSWPD9OZiUv9ARW6lRAfgae4n/ZNDtnI1sXnukW/O82aVMN1TDwymZ4plFASq2ARNeuqlTUNt96dPjg9SbLxGHeUvgHzWe3D+FfTBmdMSOYWkLjOqp47YL5BmCIsTvKw5pPO8mQcv/Wq7hGbCx2rB0twllaE4PH0Q1JyPXykg9o7RS8yB7y7zOgdts75JmaoB6QaMGRLYklCSOpM+piVKDZVDCfcbuU5Ne4J8wkulmYVNWJECgYEAyrnO/t+vjOSpW5LeQGb/qXrf1/t81QD/sdjx1cUaLZJnYPmLJlYN3GD1H8PHeHXYT1wqBDkNUvOdXzF7CtTvMzB0pl6QFpHSZn99UfGzeM2G85QWwX5Ugcsvgz9T4PFU/RzHYxLHsQzU+VKyjfjU3KZjFDvtc5YzbvrYhurUm3sCgYEAueNR54LpWW3IjpBOZod/U8GFtI7dxtQg05LKFFOZcbLi6cKhr13WZKInqNgyCTG02vsIwUD3MdMAPJG6XhlpUTOURhAYb1i9UeibSs3MsWvIoKmiPx+18p3l206jB5LJpxF/T6h8bltFwMT+PwwclDxuC4s/NOFEKk+djlOPwc0CgYABSwIY+hBCk8rIvL0SKBYFXLVEyWPYb/MxjTKtrF4oYECsi6rpia748t1dYIBLMmU4zFuwPzTojIk6bDRes1D7QJnd3ciJfFCdOr7v4NzGLnln4E/s2ANt+H/LFIJScveZza8v4JTJek15cZ+V7DpfVehX3rH7BMA7OvpakfDdNQKBgQCQvCKFtDSk4QoLlui0hEPrnv7QO30K1xhIJD8b3IvZL4PmpxjlRpcr++YNXWh4KC+9YUDL2B2gub2xa3ZE1lxeUV7dvqKH7/xYzlg7wkniyx2Hmw0tQS+WHMNgIrZlqzE/2Cvchj98XazOD5Q22uNVuEemzWEFd3Bv0VXH/+Aq8QKBgFY45YH2A/BXY2owrRFutfk4Ju1i919bHtn5KNSTFF2RoGmpObNrBSWG5GPYychRfXa8d6MAEipSzVTnclIV8c3T5EFW2pI4hWx7FDv/mQ6ezmzZ5DO9nxudQP1ZeJhAL8BwpHNWPyLACsiELR7jS5Sk5p/32z1d2KR7o1YmSQVD',
  alipayPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiJ6GBTB75FZQhooJDdEgumFILe+MVXIPR7ea8yZH/o7Gj5lLJai/r7xbl04IqE3ZyoPOzGISV79VfGYUJSaX/Lo9K0skW50hD5fn7Zx/8gDRPY3Si6FNKvfohJiUKXnau9bK9jtoF0NeU4Fvq19a0ac/3a+c6xL7BGoB2HrKP/y4Xdj1z01V0wglJAzosGrJ0sQQpJ8ETnj6tvRZXXHskMZ+BP+oc1rRVaAcKbdkfi7J1M2jVW5SD/lomjXX2nZSfZXEQYw1MNHVOGYDcCdIZUx7ZBxoVODhF94ctYmpgsHkRa/u8qP5XTXWvAbfEP9v9alxTISmnEl463ClzC4viwIDAQAB'
};
const AlipaySdk = alipay.AlipaySdk;
const alipaySdk = new AlipaySdk(AlipaySdkConfig);

class aliplayApi {
  /**
   * 获取accToken
   * @param {obj} param0 
   */
  static async accToken({ code }) {
    try {
      let params = {
        grantType: 'authorization_code',
        code,
      };
      let options = {};
      let res = await alipaySdk.exec("alipay.system.oauth.token", params, options);
      const accessToken = res && res.accessToken ? res.accessToken : '';
      if (accessToken) return accessToken
      return Promise.reject({ message: res && res.subMsg ? res.subMsg : '获取token失败' });
    }
    catch (e) {
      return Promise.reject({ message: e.message || '获取token失败' });
    }
  }
  /**
   * 获取用户信息
   * @param {obj} param0 
   */
  static async userInfo(auth_token) {
    try {
      let res = await alipaySdk.exec("alipay.user.info.share", { auth_token }, {});
      if (res && res.code === '10000') {
        const { avatar, certNo, mobile, userName, userId } = res;
        return { avatar, certNo, mobile, userName, userId };
      }
      return Promise.reject({ message: res && res.subMsg ? res.subMsg : '获取用户信息失败' });
    }
    catch (e) {
      return Promise.reject({ message: e.message || '获取用户信息失败' });
    }
  }
};

module.exports = aliplayApi;
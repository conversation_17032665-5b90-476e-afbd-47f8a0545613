let db = null;
const api = require("../api");
exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "convertCustomerProjectData":
      return await convertCustomerProjectData(content);
    case "convertConsultProjectData":
      return await convertConsultProjectData(content);
    case "convertEConsultProjectData":
      return await convertEConsultProjectData(content);
    case "convertCustomerSourceData":
      return await convertCustomerSourceData(content);
    case "convertConsultSourceData":
      return await convertConsultSourceData(content);
    case "convertEConsultSourceData":
      return await convertEConsultSourceData(content);
  }
};

// 转换网店报表的项目数据
async function convertCustomerProjectData(content) {
  const { corpId } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  try {
    // 1. 查询 member 表，获取 addMethod 为 eStoreReport 的记录
    const members = await db
      .collection("member")
      .find({
        corpId,
        addMethod: "eStoreReport",
        projectIds: { $exists: false },
      })
      .toArray();

    if (!members || members.length === 0) {
      return {
        success: true,
        message: "没有找到需要转换的数据",
        count: 0,
      };
    }
    let successCount = 0;
    let failCount = 0;
    let skippedCount = 0;

    // 2. 遍历处理每个会员记录
    for (const member of members) {
      // 检查是否有 projects 字段
      if (
        !member.projects ||
        !Array.isArray(member.projects) ||
        member.projects.length === 0
      ) {
        skippedCount++;
        continue;
      }

      // 3. 提取 projects 中的 _id 形成 ids 数组
      const ids = member.projects.map((project) => project._id).filter(Boolean);

      if (ids.length === 0) {
        skippedCount++;
        continue;
      }
      // 4. 调用 transformProjectData 进行转换
      const result = await api.getCorpApi({
        type: "transformProjectData",
        ids,
        corpId: member.corpId,
      });

      // 5. 如果转换成功，更新 member 表中的 projectIds
      if (result.success && result.data && result.data.length > 0) {
        await db
          .collection("member")
          .updateOne(
            { _id: member._id, corpId },
            { $set: { projectIds: result.data } }
          );
        successCount++;
      } else {
        failCount++;
      }
    }

    return {
      success: true,
      message: "项目数据转换完成",
      result: {
        total: members.length,
        successCount,
        failCount,
        skippedCount,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: `项目数据转换失败: ${error.message}`,
      error: error.stack,
    };
  }
}

// 转换网店报表的项目数据
async function convertConsultProjectData(content) {
  const { corpId } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  try {
    // 1. 查询 member 表，获取 addMethod 为 eStoreReport 的记录
    const members = await db
      .collection("consult-record")
      .find({
        corpId,
        projectIds: { $exists: false },
      })
      .toArray();
    if (!members || members.length === 0) {
      return {
        success: true,
        message: "没有找到需要转换的数据",
        count: 0,
      };
    }

    let successCount = 0;
    let failCount = 0;
    let skippedCount = 0;

    // 2. 遍历处理每个会员记录
    for (const member of members) {
      // 检查是否有 projects 字段
      if (
        !member.projects ||
        !Array.isArray(member.projects) ||
        member.projects.length === 0
      ) {
        skippedCount++;
        continue;
      }
      // 3. 提取 projects 中的 _id 形成 ids 数组
      const ids = member.projects.map((project) => project._id).filter(Boolean);
      if (ids.length === 0) {
        skippedCount++;
        continue;
      }
      // 4. 调用 transformProjectData 进行转换
      const result = await api.getCorpApi({
        type: "transformProjectData",
        ids,
      });
      console.log("===========result", result);
      // 5. 如果转换成功，更新 member 表中的 projectIds
      if (result.success && result.data && result.data.length > 0) {
        await db
          .collection("consult-record")
          .updateOne(
            { _id: member._id, corpId },
            { $set: { projectIds: result.data } }
          );
        successCount++;
      } else {
        failCount++;
      }
    }

    return {
      success: true,
      message: "项目数据转换完成",
      result: {
        total: members.length,
        successCount,
        failCount,
        skippedCount,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: `项目数据转换失败: ${error.message}`,
      error: error.stack,
    };
  }
}

async function convertEConsultProjectData(content) {
  const { corpId } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  try {
    // 1. 查询 member 表，获取 addMethod 为 eStoreReport 的记录
    const members = await db
      .collection("e-consult-record")
      .find({
        corpId,
        projectIds: { $exists: false },
      })
      .toArray();

    if (!members || members.length === 0) {
      return {
        success: true,
        message: "没有找到需要转换的数据",
        count: 0,
      };
    }

    let successCount = 0;
    let failCount = 0;
    let skippedCount = 0;

    // 2. 遍历处理每个会员记录
    for (const member of members) {
      // 检查是否有 projects 字段
      if (
        !member.projects ||
        !Array.isArray(member.projects) ||
        member.projects.length === 0
      ) {
        skippedCount++;
        continue;
      }

      // 3. 提取 projects 中的 _id 形成 ids 数组
      const ids = member.projects.map((project) => project._id).filter(Boolean);

      if (ids.length === 0) {
        skippedCount++;
        continue;
      }
      // 4. 调用 transformProjectData 进行转换
      const result = await api.getCorpApi({
        type: "transformProjectData",
        ids,
      });

      // 5. 如果转换成功，更新 member 表中的 projectIds
      if (result.success && result.data && result.data.length > 0) {
        await db
          .collection("e-consult-record")
          .updateOne(
            { _id: member._id, corpId },
            { $set: { projectIds: result.data } }
          );
        successCount++;
      } else {
        failCount++;
      }
    }

    return {
      success: true,
      message: "项目数据转换完成",
      result: {
        total: members.length,
        successCount,
        failCount,
        skippedCount,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: `项目数据转换失败: ${error.message}`,
      error: error.stack,
    };
  }
}

// 转换会员表中的customerSource为infoSource
async function convertCustomerSourceData(content) {
  const { corpId } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    // 1. 查询 member 表
    const members = await db
      .collection("member")
      .find({
        corpId,
        customerSource: { $exists: true, $ne: null },
        infoSource: { $exists: false },
      })
      .toArray();

    if (!members || members.length === 0) {
      return {
        success: true,
        message: "没有找到需要转换的数据",
        count: 0,
      };
    }

    let successCount = 0;
    let failCount = 0;
    let skippedCount = 0;

    // 2. 遍历处理每个会员记录
    for (const member of members) {
      // 检查customerSource是否是数组
      if (
        !Array.isArray(member.customerSource) ||
        member.customerSource.length === 0
      ) {
        skippedCount++;
        continue;
      }

      // 3. 根据infoObj将老数据转换为新数据
      const infoSource = member.customerSource
        .map((source) => {
          return exports.infoObj[source] || source;
        })
        .filter(Boolean);

      if (infoSource.length === 0) {
        skippedCount++;
        continue;
      }

      // 4. 更新member表记录，写入infoSource字段
      try {
        await db
          .collection("member")
          .updateOne({ _id: member._id, corpId }, { $set: { infoSource } });
        successCount++;
      } catch (err) {
        console.error(`更新会员失败: ${member._id}`, err);
        failCount++;
      }
    }

    return {
      success: true,
      message: "会员来源数据转换完成",
      result: {
        total: members.length,
        successCount,
        failCount,
        skippedCount,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: `会员来源数据转换失败: ${error.message}`,
      error: error.stack,
    };
  }
}

// 转换咨询记录表中的source字段
async function convertConsultSourceData(content) {
  const { corpId } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  try {
    // 1. 查询 consult-record 表
    const records = await db
      .collection("consult-record")
      .find({
        corpId,
        source: { $exists: true, $ne: null },
      })
      .toArray();

    if (!records || records.length === 0) {
      return {
        success: true,
        message: "没有找到需要转换的数据",
        count: 0,
      };
    }

    let successCount = 0;
    let failCount = 0;
    let skippedCount = 0;

    // 2. 遍历处理每个咨询记录
    for (const record of records) {
      // 检查source是否是数组
      if (!Array.isArray(record.source) || record.source.length === 0) {
        skippedCount++;
        continue;
      }

      // 3. 根据infoObj将老数据转换为新数据
      const newSource = record.source
        .map((source) => {
          return exports.infoObj[source] || source;
        })
        .filter(Boolean);

      if (newSource.length === 0) {
        skippedCount++;
        continue;
      }

      // 4. 更新consult-record表记录，更新source字段
      try {
        await db
          .collection("consult-record")
          .updateOne(
            { _id: record._id, corpId },
            { $set: { source: newSource } }
          );
        successCount++;
      } catch (err) {
        console.error(`更新咨询记录失败: ${record._id}`, err);
        failCount++;
      }
    }

    return {
      success: true,
      message: "咨询来源数据转换完成",
      result: {
        total: records.length,
        successCount,
        failCount,
        skippedCount,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: `咨询来源数据转换失败: ${error.message}`,
      error: error.stack,
    };
  }
}

// 转换电子咨询记录表中的source字段
async function convertEConsultSourceData(content) {
  const { corpId } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  try {
    // 1. 查询 e-consult-record 表
    const records = await db
      .collection("e-consult-record")
      .find({
        corpId,
        source: { $exists: true, $ne: null },
      })
      .toArray();

    if (!records || records.length === 0) {
      return {
        success: true,
        message: "没有找到需要转换的数据",
        count: 0,
      };
    }

    let successCount = 0;
    let failCount = 0;
    let skippedCount = 0;

    // 2. 遍历处理每个电子咨询记录
    for (const record of records) {
      // 检查source是否是数组
      if (!Array.isArray(record.source) || record.source.length === 0) {
        skippedCount++;
        continue;
      }

      // 3. 根据infoObj将老数据转换为新数据
      const newSource = record.source
        .map((source) => {
          return exports.infoObj[source] || source;
        })
        .filter(Boolean);

      if (newSource.length === 0) {
        skippedCount++;
        continue;
      }

      // 4. 更新e-consult-record表记录，更新source字段
      try {
        await db
          .collection("e-consult-record")
          .updateOne(
            { _id: record._id, corpId },
            { $set: { source: newSource } }
          );
        successCount++;
      } catch (err) {
        console.error(`更新电子咨询记录失败: ${record._id}`, err);
        failCount++;
      }
    }

    return {
      success: true,
      message: "电子咨询来源数据转换完成",
      result: {
        total: records.length,
        successCount,
        failCount,
        skippedCount,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: `电子咨询来源数据转换失败: ${error.message}`,
      error: error.stack,
    };
  }
}

exports.infoObj = {
  大众点评: "大众点评网",
  小红书: "小红书",
  "三方转诊-悠咪网": "悠咪网",
  医院官网: "医院网站",
  "三方转诊-清颜": "清颜",
  新氧: "新氧",
  "三方转诊-爱丽帮": "爱丽帮",
  爱丽帮: "爱丽帮",
  高德: "高德",
  微信小程序: "微信小程序",
  口碑: "口碑",
  "三方转诊-非常爱美": "非常爱美",
  "三方转诊-百里挑一": "百里挑一",
  挂号转诊平台: "挂号转诊平台",
  "三方转诊-讨喜": "讨喜",
  其他网站: "其他网站",
  "三方转诊 / 鹿知颜": "鹿知颜",
  "三方转诊 / 美呗转诊": "美呗转诊",
  其他: "其他",
  "三方转诊 / 更美": "更美转诊",
  "三方转诊-圣嘉美": "圣嘉美",
  "三方转诊-伴言转诊": "伴言转诊",
  老带新: "老带新",
  "大客户部-中信协同": "大客户部",
};

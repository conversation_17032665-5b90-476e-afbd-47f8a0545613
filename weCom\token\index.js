const request = require("../request");
const api = require("../../api");
exports.getToken = async ({ corpid = "", corpId = "", permanentCode = "" }) => {
  try {
    console.log("corpId", corpid , corpId);
    if (!corpId) corpId = corpid;
    const { success, data } = await api.getCorpApi({
      corpId,
      cacheType: "appToken",
      type: "getCorpCache",
    });
    if (success) return data.value;
    let suiteToken = await exports.getSuiteToken();
    if (!permanentCode) {
      const result = await api.getCorpApi({ type: "getCorpInfo", corpId });

      const corp = result.data[0];
      permanentCode = corp.permanent_code;
    }
 
    const url = `https://qyapi.weixin.qq.com/cgi-bin/service/get_corp_token?suite_access_token=${suiteToken}`;
    const params = {
      auth_corpid: corpId,
      permanent_code: permanentCode,
    };
    let { expires_in, access_token, errmsg, errcode } = await request.main(
      url,
      params,
      "POST"
    );
    if (access_token) {
      await api.getCorpApi({
        corpId,
        value: access_token,
        expiresIn: expires_in,
        cacheType: "appToken",
        type: "createCorpCache",
        id: data ? data._id : "",
      });
      return access_token;
    } else {
      return {
        success: false,
        message: errmsg,
        errcode: errcode,
      };
    }
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取 token 时发生错误",
    };
  }
};
exports.getProviderAccessToken = async () => {
  try {
    const url = `https://qyapi.weixin.qq.com/cgi-bin/service/get_provider_token`;
    const params = {
      corpid: "wwe3fb2faa52cf9dfb",
      provider_secret:
        "v5sv-M56Amgik91OewBIzcq6ptbnlkC-QUisBq3UNErDF6Yyxd4BgBRea0_636se",
    };
    const { provider_access_token } = await request.main(url, params, "POST");
    if (provider_access_token) {
      return provider_access_token;
    } else {
      throw new Error("获取 provider_access_token 失败");
    }
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取 provider_access_token 时发生错误",
    };
  }
};

exports.getSuiteToken = async () => {
  try {
    let res = await api.getCorpApi({
      type: "getSuiteToken",
    });
    console.log(res);
    return res.suiteToken;
  } catch {
    return "";
  }
};

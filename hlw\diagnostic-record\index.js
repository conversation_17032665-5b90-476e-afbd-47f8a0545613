let db = "";
const { ObjectId } = require("mongodb");
const common = require("../../common");
const dayjs = require("dayjs");
const { formatUploadRxData } = require("./format");
const zytHis = require("../zyt-his");
const consultOrder = require("../consult-order");
const tencentIM = require("../tencent-im");
const beijingCa = require("../beijing-ca");
const status = {
  init: "INIT", //待审核
  pass: "PASS", //审核通过
  unpass: "UNPASS", //审核未通过
  expired: "EXPIRED", //订单过期 无法
};
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "addConsultDiagnosis":
      return await addConsultDiagnosis(item, db);
    case "getOrderDiagnosis":
      return await getOrderDiagnosis(item, db);
    case "getAuditDiagnosisList":
      return await getAuditDiagnosisList(item, db);
    case "getAuditedDiagnosisList":
      return await getAuditedDiagnosisList(item, db);
    case "getPatientDiagnosisRecord":
      return await getPatientDiagnosisRecord(item, db);
    case "auditDiagnosis":
      return await auditDiagnosis(item, db);
    case "uploadDiagnosis":
      return await uploadDiagnosis(item, db);
    case "orderHasDiagnosis":
      return await orderHasDiagnosis(item, db);
    case "getLatestSubmitTime":
      return await getLatestSubmitTime(item, db);
    case "reUploadDiagnosticRecord":
      return await reUploadDiagnosticRecord(item, db);
  }
};
// 咨询订单库 数据库是 diagnostic-record
async function addConsultDiagnosis(item) {
  let { params = {}, corpId } = item;
  const { orderId, doctorCode } = params;
  if (
    typeof orderId !== "string" ||
    orderId.trim() === "" ||
    typeof doctorCode !== "string" ||
    doctorCode.trim() === ""
  ) {
    return { success: false, message: "参数错误" };
  }
  try {
    // 提交诊断前, 先判单CA 自动签名是否失效
    const res = await beijingCa({
      type: "autoSignVerifyCA",
      code: doctorCode,
    });
    const doctorCAUserId = res.userId;
    if (!res.success) {
      return { success: false, message: "CA自动签名已失效, 请开启自动签" };
    }
    const record = await db
      .collection("diagnostic-record")
      .findOne({ orderId, doctorCode });
    if (record && record.status === status.init) {
      return { success: false, message: "该订单已存在诊断信息，请勿重复添加" };
    } else if (record && record.status === status.pass) {
      return { success: false, message: "该订单已审核通过，请勿重复添加" };
    } else if (record && record.status === status.unpass) {
      return await rewriteConsultDiagnosis(params, record._id, corpId);
    }
    const data = {
      ...params,
      createTime: Date.now(),
      status: status.init,
      doctorCAUserId,
    };
    const { insertedId } = await db
      .collection("diagnostic-record")
      .insertOne(data);
    // 发起咨询后, 咨询订单状态变为已完成
    await updateOrderStatusAndSendNotification({
      orderId,
      corpId,
      doctorCode,
      notification: "医生已提交诊断, 等待审方中",
      syncOtherMachine: 2,
      orderStatus: "completed",
      msgType: "MEDICALADVICE",
    });
    return {
      success: true,
      message: "新增成功",
      data: insertedId,
      doctorCAUserId,
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "新增失败",
    };
  }
}
async function rewriteConsultDiagnosis(item, _id, corpId) {
  const {
    complaint,
    presentIllness,
    dispose,
    diagnosisList,
    drugs,
    orderId,
    doctorCode,
  } = item;
  try {
    const data = {
      complaint,
      presentIllness,
      dispose,
      diagnosisList,
      drugs,
      updateTime: Date.now(),
      status: status.init,
    };
    const { modifiedCount } = await db
      .collection("diagnostic-record")
      .updateOne({ _id }, { $set: data });
    await updateOrderStatusAndSendNotification({
      orderId,
      corpId,
      doctorCode,
      notification: "医生重新提交诊断, 等待审方中",
      syncOtherMachine: 2,
      orderStatus: "completed",
      msgType: "MEDICALADVICE",
    });
    if (modifiedCount === 1) {
      return { success: true, message: "更新成功" };
    }
    return { success: false, message: "更新失败" };
  } catch (e) {
    return { success: false, message: e.message || "更新失败" };
  }
}

async function getOrderDiagnosis(item, db) {
  const { orderId, doctorNo, status } = item;
  if (typeof orderId !== "string" || orderId.trim() === "") {
    return { success: false, message: "参数错误" };
  }
  try {
    const query = { orderId, doctorNo };
    if (typeof status === "string" && status.trim() !== "") {
      query.status = status;
    }
    const data = await db
      .collection("diagnostic-record")
      .findOne(query);
    return { success: true, message: "查询成功", data };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}

async function getAuditDiagnosisList(item, db) {
  const page = item.page > 0 && Number.isInteger(item.page) ? item.page : 1;
  const pageSize =
    item.pageSize > 0 && Number.isInteger(item.pageSize) ? item.pageSize : 1;
  const query = { status: status.init };
  if (typeof item.name === "string" && item.name.trim() !== "") {
    query.name = new RegExp(item.name);
  }
  if (typeof item.mobile === "string" && item.mobile.trim() !== "") {
    query.mobile = new RegExp(item.mobile);
  }
  if (Array.isArray(item.doctorCodes) && item.doctorCodes.length > 0) {
    query.doctorCode = { $in: item.doctorCodes };
  }
  try {
    const total = await db
      .collection("diagnostic-record")
      .countDocuments(query);
    const res = await db.collection("diagnostic-record").findOne(
      {},
      {
        sort: { createTime: -1 }, // Sort by createTime in descending order
        projection: { createTime: 1, _id: 0 }, // Only return the createTime field, exclude _id
      }
    );
    const list = await db
      .collection("diagnostic-record")
      .aggregate([
        {
          $match: query, // 保留原始查询条件
        },
        {
          $lookup: {
            from: "consult-order", // 要关联的集合
            localField: "orderId", // 当前集合中的字段
            foreignField: "orderId", // 关联集合中的字段
            as: "order", // 结果数组字段，包含匹配的文档
          },
        },
        {
          $unwind: "$order", // 展开 order 数组，确保每条诊断记录有一个对应的 order
        },
        {
          $skip: (page - 1) * pageSize, // 分页跳过
        },
        {
          $limit: pageSize, // 分页限制返回的数量
        },
      ])
      .toArray();
    return {
      success: true,
      message: "查询成功",
      list,
      total,
      latestTime: res && res.createTime ? res.createTime : 0,
    };
  } catch (e) {
    return { success: false, message: e.message || "查询失败", latestTime: 0 };
  }
}

async function getPatientDiagnosisRecord(item, db) {
  if (!item.patientId) {
    return { success: false, message: "参数错误" };
  }
  const page = item.page > 0 && Number.isInteger(item.page) ? item.page : 1;
  const pageSize =
    item.pageSize > 0 && Number.isInteger(item.pageSize) ? item.pageSize : 20;
  const query = { patientId: item.patientId, status: status.pass };
  try {
    const total = await db
      .collection("diagnostic-record")
      .countDocuments(query);
    const list = await db
      .collection("diagnostic-record")
      .find(query, { sort: { auditTime: -1 } })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    return {
      success: true,
      message: "查询成功",
      list,
      total,
      paegs: Math.ceil(total / pageSize),
    };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}

async function getAuditedDiagnosisList(item, db) {
  const page = item.page > 0 && Number.isInteger(item.page) ? item.page : 1;
  const pageSize =
    item.pageSize > 0 && Number.isInteger(item.pageSize) ? item.pageSize : 1;
  const query = { status: { $in: [status.pass, status.unpass, status.expired] } };
  if (typeof item.name === "string" && item.name.trim() !== "") {
    query.name = new RegExp(item.name);
  }
  if (typeof item.mobile === "string" && item.mobile.trim() !== "") {
    query.mobile = new RegExp(item.phone);
  }
  if (Array.isArray(item.doctorCodes) && item.doctorCodes.length > 0) {
    query.doctorCode = { $in: item.doctorCodes };
  }
  console.log('query', query)
  try {
    const total = await db
      .collection("diagnostic-record")
      .countDocuments(query);
    const list = await db
      .collection("diagnostic-record")
      .aggregate([
        {
          $match: query, // 保留原始查询条件
        },
        {
          $lookup: {
            from: "consult-order", // 要关联的集合
            localField: "orderId", // 当前集合中的字段
            foreignField: "orderId", // 关联集合中的字段
            as: "order", // 结果数组字段，包含匹配的文档
          },
        },
        { $sort: { auditTime: -1 } },
        {
          $skip: (page - 1) * pageSize, // 分页跳过
        },
        {
          $limit: pageSize, // 分页限制返回的数量
        },
      ])
      .toArray();
    return {
      success: true, message: "查询成功", list: list.map(i => ({
        ...i,
        order: i.order && i.order[0] ? i.order[0] : {}
      })), total
    };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}
async function auditDiagnosis(item, db) {
  const { ids, pharmacistNo, pharmacist } = item;
  const res = await beijingCa({
    type: "autoSignVerifyCA",
    code: pharmacistNo,
  });
  if (!res.success) {
    return { success: false, message: "CA自动签名已失效, 请开启自动签" };
  }
  const pharmacistCAUserId = res.userId;
  if (![status.pass, status.unpass].includes(item.status)) {
    return { success: false, message: "状态错误" };
  }
  if (!pharmacistNo || !pharmacist) {
    return { success: false, message: "审方药师信息不能为空" };
  }
  if (!Array.isArray(ids) || ids.length === 0) {
    return { success: false, message: "id错误" };
  }
  const reasons = Array.isArray(item.reasons)
    ? item.reasons.filter((i) => typeof i === "string" && i.trim() !== "")
    : [];
  if (item.status === status.unpass && reasons.length === 0) {
    return { success: false, message: "请填写拒绝原因" };
  }
  try {
    const _ids = ids.map((id) => newObjectId (id));
    const recordIds = await db
      .collection("diagnostic-record")
      .find({ _id: { $in: _ids } }, { projection: { orderId: 1, _id: 1 } })
      .toArray();
    // 查询未过期的咨询订单id
    const noExpiredOrderIds = await db
      .collection("consult-order")
      .find(
        {
          orderId: { $in: recordIds.map((i) => i.orderId) },
          orderStatus: { $in: ["pending", "processing", "completed"] },
        },
        { projection: { orderId: 1 } }
      )
      .toArray();
    // 根据查询到的咨询订单id 筛选出需要审核的诊断id
    const toAuditIds = recordIds
      .filter((i) => noExpiredOrderIds.some((j) => j.orderId === i.orderId))
      .map((i) => i._id);
    const expiredRecordIds = recordIds.filter(
      (i) => !toAuditIds.some((j) => j.toString() === i._id.toString())
    );
    const expiredRes = await db.collection("diagnostic-record").updateMany(
      {
        _id: { $in: expiredRecordIds.map((i) => i._id) },
        status: status.init,
      },
      { $set: { status: status.expired } }
    );
    const res = await db.collection("diagnostic-record").updateMany(
      { _id: { $in: toAuditIds }, status: status.init },
      {
        $set: {
          status: item.status,
          auditTime: Date.now(),
          reasons,
          pharmacistNo,
          pharmacist,
        },
      }
    );
    const { matchedCount, modifiedCount } = res;
    const {
      matchedCount: expiredMatchedCount,
      modifiedCount: expiredModifiedCount,
    } = expiredRes;
    // 批量处理订单
    let msg = `找到${matchedCount + expiredMatchedCount
      }条数据, 已通过${modifiedCount}条数据`;
    if (expiredModifiedCount > 0) {
      msg += `, 已过期${expiredMatchedCount}条数据`;
    }
    batchHandleOrderByAudit(toAuditIds, pharmacistCAUserId, pharmacist);
    if (item.status === status.pass) {
      await uploadDiagnosis(toAuditIds);
      return {
        success: true,
        message: msg,
      };
    }
    return {
      success: true,
      message: `找到${matchedCount}条数据, 已不通过${modifiedCount}条数据`,
    };
  } catch (e) {
    return { success: false, message: e.message || "审核失败" };
  }
}
async function batchHandleOrderByAudit(toAuditIds, pharmacistCAUserId) {
  // 利用promise.all处理多个异步任务
  await Promise.all(
    toAuditIds.map(async (_id) => {
      const res = await db.collection("diagnostic-record").findOne({ _id });
      if (!res) {
        console.log("诊断记录不存在");
        return;
      }
      await handleOrderByAudit(res, pharmacistCAUserId);
    })
  );
}

// 根据审核状态, 处理订单
async function handleOrderByAudit(item, pharmacistCAUserId) {
  const { orderId, reasons, doctorCode, pharmacist } = item;
  const order = await db.collection("consult-order").findOne({ orderId });
  if (!order) {
    return {
      success: false,
      message: "订单不存在",
    };
  }
  if (order.orderStatus === "cancelled" || order.orderStatus === "finished") {
    console.log("订单已取消或已完成");
    return;
  }
  if (item.status === status.pass) {
    // 签名
    await sign(item, pharmacistCAUserId);
    await tencentIM({
      type: "sendSystemNotification",
      corpId: order.corpId,
      formAccount: doctorCode,
      toAccount: orderId,
      SyncOtherMachine: 1,
      msgBody: [
        {
          MsgType: "TIMTextElem",
          MsgContent: {
            Text: "我已为您开具处方，处方已审方通过",
          },
        },
      ],
    });
  } else if (item.status === status.unpass) {
    // 不通过  发送消息
    // 数组转字符串
    const reason = reasons.join(",");
    await updateOrderStatusAndSendNotification({
      orderId,
      corpId: order.corpId,
      doctorCode,
      notification: `药师${pharmacist}审方不通过，驳回原因：${reason}`,
      syncOtherMachine: 2,
      orderStatus: "processing",
      msgType: "AUDIFAIL",
    });
  }
}

// 对医生和药师进行签名
async function sign(item, pharmacistCAUserId) {
  const { doctorCAUserId, pharmacist } = item;
  console.log("进入签名");
  const oriData = getOriData(item);
  const doctorRes = await beijingCa(
    {
      type: "autoSign",
      userId: doctorCAUserId,
      oriData,
    },
    db
  );
  const pharmacistRes = await beijingCa(
    {
      type: "autoSign",
      userId: pharmacistCAUserId,
      oriData: `${oriData} 药师${pharmacist}审方通过`,
    },
    db
  );
  console.log("医生签名结果", doctorRes);
  console.log("药师签名结果", pharmacistRes);
}

function getOriData(data) {
  const doctorName = data.doctorName;
  const patientName = data.name;
  const diagnosis = data.diagnosisList.map((d) => d.name).join(", ");
  const drugs = data.drugs
    .map(
      (d) =>
        `${d.drugName} (${d.dosage}${d.dosage_unit}, ${d.frequencyName}, ${d.days}天)`
    )
    .join(", ");
  return `医生${doctorName}向患者${patientName}开具的诊断是${diagnosis}，开具的药物是${drugs}。`;
}

async function uploadDiagnosis(ids) {
  if (!Array.isArray(ids) || ids.length == 0)
    return {
      success: false,
      message: "参数错误",
    };
  try {
    const res = await db
      .collection("diagnostic-record")
      .find({ _id: { $in: ids }, status: status.pass })
      .limit(ids.length)
      .toArray();
    const list = res.map((item) => ({
      payload: formatUploadRxData(item),
      _id: item._id,
    }));
    const res1 = await Promise.all(
      list.map((item) =>
        zytHis({
          type: "hlwuploadprescription",
          payload: item.payload,
          _id: item._id,
        })
      )
    );
    const successIds = res1.filter((i) => i.success).map((i) => i._id);
    await db
      .collection("diagnostic-record")
      .updateMany(
        { _id: { $in: successIds } },
        { $set: { uploadStatus: "uploaded" } }
      );
    const successCount = successIds.length;
    return {
      success: true,
      message: `成功上传${successCount}条数据`,
    };
  } catch (err) {
    console.log(err, err.message)
    return {
      success: false,
      message: err.message || "上传失败",
    };
  }
}

/**
 *
 * orderStatus  completed(已完成) processing(处理中) pending(待处理) cancelled(已取消) finished(已结束)
 * syncOtherMachine 若不希望将消息同步至 From_Account，则 SyncOtherMachine 填写2；若希望将消息同步至 From_Account，则 SyncOtherMachine 填写1。
 * MsgType TIMTextElem（文本消息）  TIMLocationElem（位置消息） TIMFaceElem（表情消息） TIMCustomElem（自定义消息） TIMSoundElem（语音消息） TIMImageElem（图像消息） TIMFileElem（文件消息） TIMVideoFileElem（视频消息）
 * MsgContent.Text 文本消息内容
 * MsgContent.Data 自定义消息类型 MEDICALADVICE(医嘱)
 * MsgContent.Desc notification
 *
 */
async function updateOrderStatusAndSendNotification(item) {
  const {
    orderId,
    doctorCode,
    corpId,
    notification,
    syncOtherMachine,
    orderStatus,
    msgType,
  } = item;
  await consultOrder(
    {
      type: "updateConsultOrderStatus",
      orderId,
      orderStatus,
      corpId,
    },
    db
  );
  await tencentIM({
    type: "sendSystemNotification",
    corpId,
    formAccount: orderId,
    toAccount: doctorCode,
    SyncOtherMachine: syncOtherMachine,
    msgBody: [
      {
        MsgType: "TIMCustomElem",
        MsgContent: {
          Data: msgType, //medical advice
          Desc: "notification",
          Ext: notification,
        },
      },
    ],
  });
}

async function getLatestSubmitTime() {
  try {
    const res = await db.collection("diagnostic-record").findOne(
      {},
      {
        sort: { createTime: -1 }, // Sort by createTime in descending order
        projection: { createTime: 1, _id: 0 }, // Only return the createTime field, exclude _id
      }
    );
    return {
      success: true,
      message: "获取成功",
      data: res ? res.createTime : 0,
    };
  } catch (e) {
    return { success: false, message: e.message || "获取失败", data: 0 };
  }
}

async function orderHasDiagnosis(item) {
  if (typeof item.patientId !== "string" || typeof item.orderId !== "string") {
    return {
      success: false,
      message: "参数错误",
    };
  }
  try {
    const count = await db.collection("diagnostic-record").countDocuments({
      patientId: item.patientId,
      orderId: item.orderId,
    });
    return { success: true, message: "查询成功", exist: count > 0 };
  } catch (e) {
    return { success: true, message: "查询成功", exist: false };
  }
}

async function reUploadDiagnosticRecord({ _id }) {
  try {
    const data = await db.collection('diagnostic-record').findOne({ _id: new ObjectId(_id) });
    if (!data) return { success: false, message: '处方不存在' };
    if (data && data.uploadStatus === 'uploaded') return { success: false, message: '处方已上传' };
    const { uploaded } = await zytHis({ type: 'hlwPrescriptionOrderStatus', orderno: data.orderId, patientId: data.patientId });
    if (uploaded) {
      await db.collection('diagnostic-record').updateOne({ _id: new ObjectId(_id) }, { $set: { uploadStatus: 'uploaded' } });
      return { success: true, message: '处方上传成功' };
    }
    const payload = formatUploadRxData(data);
    const { success, message } = await zytHis({ type: 'hlwuploadprescription', payload, _id: data._id });
    if (success) {
      await db.collection('diagnostic-record').updateOne({ _id: new ObjectId(_id) }, { $set: { uploadStatus: 'uploaded' } });
      return { success: true, message: '处方上传成功' };
    }
    return { success: false, message: message || '重新上传失败' };

  } catch (e) {
    return { success: false, message: e.message || '重新上传失败' };
  }
}
const request = require("../request");
const accessToken = require("../token");
const logger = require("../../utils/logger");
exports.main = async (context) => {
  let {
    access_token = "",
    corpId,
    permanentCode,
    suiteToken,
    corpid,
  } = context;
  if (!access_token) {
    access_token = await accessToken.getToken({
      corpId,
      corpid,
      permanentCode: permanentCode,
      suiteToken,
    });
    context.access_token = access_token;
  }
  console.log("context", context);
  switch (context.type) {
    case "addWecomMsgTemplate":
      return await addWecomMsgTemplate(context);
    case "getWecomGroupmsgtask":
      return await getWecomGroupmsgtask(context);
    case "getWecomGroupmsgSendResult":
      return await getWecomGroupmsgSendResult(context);
    case "stopWecomGroupmsgTask":
      return await stopWecomGroupmsgTask(context);
    case "remindGroupmsgSend":
      return await remindGroupmsgSend(context);
    default: {
      return {
        success: false,
        message: "未找到对应的操作类型",
      };
    }
  }
};

async function addWecomMsgTemplate({
  externalUserIds = [],
  attachments = [],
  content = "",
  executor = "",
  access_token,
}) {
  let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/add_msg_template?access_token=${access_token}`;
  let item = {
    chat_type: "single",
    external_userid: externalUserIds,
    text: {
      content,
    },
    attachments: attachments,
    allow_select: false,
    sender: executor,
  };
  let { errcode, fail_list, msgid } = await request.main(url, item, "POST");
  return {
    errcode,
    fail_list,
    msgid,
  };
}

// 获取群发成员发送任务列表
async function getWecomGroupmsgtask({ access_token, msgid, parentId = "" }) {
  let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_groupmsg_task?access_token=${access_token}`;
  let item = {
    msgid,
    limit: 1000,
  };
  let { errcode, task_list } = await request.main(url, item, "POST");
  if (errcode === 0) {
    return {
      success: true,
      message: "获取成功",
      task_list,
      parentId,
      msgid,
    };
  } else {
    return {
      success: true,
      message: "获取失败",
      msgid,
      parentId,
      task_list: [],
    };
  }
}

//获取企业群发成员执行结果
async function getWecomGroupmsgSendResult(context) {
  let { msgid, userid, access_token } = context;
  let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_groupmsg_send_result?access_token=${access_token}`;
  let item = {
    msgid,
    userid,
    limit: 1000,
  };
  let { errcode, send_list } = await request.main(url, item, "POST");
  if (errcode === 0) {
    send_list = send_list.map((item) => {
      item["msgid"] = msgid;
      return item;
    });
    return {
      success: true,
      message: "获取成功",
      send_list,
      msgid,
    };
  } else {
    return {
      success: true,
      message: "获取失败",
      msgid,
      send_list: [],
    };
  }
}

async function stopWecomGroupmsgTask(content) {
  let { msgid, access_token } = content;
  // 请求的 URL
  let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/cancel_groupmsg_send?access_token=${access_token}`;
  try {
    // 发起请求
    let { errcode, errmsg } = await request.main(url, { msgid }, "POST");
    // 成功时，更新数据库任务状态
    return {
      success: errcode === 0,
      message: errmsg,
    };
  } catch (error) {
    // 捕获异常并返回
    logger.error("Error stopping WeCom group message task:", error);
    return {
      success: false,
      message: "请求失败，无法停止群发任务",
    };
  }
}

async function remindGroupmsgSend(content) {
  let { msgid, access_token } = content;

  let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/remind_groupmsg_send?access_token=${access_token}`;
  try {
    let { errcode, errmsg } = await request.main(
      url,
      {
        msgid,
      },
      "POST"
    );
    if (errcode === 0) {
      return {
        success: true,
        message: "提醒群发成功",
      };
    } else {
      return {
        success: false,
        message: errmsg,
      };
    }
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
}

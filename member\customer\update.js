const dayjs = require("dayjs");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "updateCustomerInHospitalTime":
      return await updateCustomerInHospitalTime(content);
    case "pushCustomerTeamId":
      return await pushCustomerTeamId(content);
    case "updateCustomerPersonResponsibles":
      return await updateCustomerPersonResponsibles(content);
    case "updateCustomerCounselor":
      return await updateCustomerCounselor(content);
    case "updateCustomerIntroducer":
      return await updateCustomerIntroducer(content);
  }
};
async function updateCustomerInHospitalTime(content) {
  console.log("content", content);
  const { corpId, customerId, inHospitalTime, counselor, creator, teamId } =
    content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  const query = { corpId, _id: customerId };
  const customer = await db.collection("member").findOne(query);
  if (!customer) return { success: false, message: "客户不存在" };
  let { inHospitalTimes } = customer;
  if (!inHospitalTimes || !Array.isArray(inHospitalTimes)) inHospitalTimes = [];
  const inHospitalTimeDay = dayjs(inHospitalTime).format("YYYY-MM-DD");
  const inHospitalTimesDay = inHospitalTimes.map((time) =>
    dayjs(time).format("YYYY-MM-DD")
  );
  let params = {};
  if (!inHospitalTimesDay.includes(inHospitalTimeDay)) {
    inHospitalTimes.push(inHospitalTime);
    // 根据大小排序
    inHospitalTimes.sort((a, b) => a - b);
    params.inHospitalTimes = inHospitalTimes;
  }
  const res = await db
    .collection("member")
    .updateOne({ _id: customer._id }, { $set: params });
  return { success: true, data: res, message: "更新到院时间成功" };
}
// 更新所属咨询师
async function updateCustomerCounselor({
  customerId,
  counselor,
  counselorRecord = [],
  creator,
  teamId,
  personResponsibles,
  customerTeamIds,
}) {
  if (!Array.isArray(counselorRecord)) {
    counselorRecord = [];
  }
  counselorRecord.push({
    counselor,
    time: new Date().getTime(),
    operator: creator,
    teamId,
  });
  let query = { counselorRecord };
  if (personResponsibles) {
    query = { ...query, personResponsibles };
  }
  if (customerTeamIds && customerTeamIds.length > 0) {
    query = { ...query, teamId: customerTeamIds };
  }
  const res = await db
    .collection("member")
    .updateOne({ _id: customerId }, { $set: query });
  return { success: true, data: res, message: "更新咨询师成功" };
}

// 更新开发人员
async function updateCustomerIntroducer({
  customerId,
  creator,
  introducer,
  introducerRecord = [],
  teamId,
  personResponsibles,
  customerTeamIds,
}) {
  // 确保 introducerRecord 是数组，如果不是则初始化为空数组
  if (!Array.isArray(introducerRecord)) {
    introducerRecord = [];
  }

  introducerRecord.push({
    introducer,
    time: new Date().getTime(),
    operator: creator,
    teamId,
  });
  const res = await db.collection("member").updateOne(
    { _id: customerId },
    {
      $set: { introducerRecord, personResponsibles, teamId: customerTeamIds },
    }
  );
  return { success: true, data: res, message: "更新开发人员成功" };
}

// 更新客户负责人
async function updateCustomerPersonResponsibles(item) {
  try {
    const { id, personResponsibles, teamId } = item;
    const res = await db
      .collection("member")
      .updateOne({ _id: id }, { $set: { personResponsibles, teamId } });

    return {
      message: "添加成功",
      success: true,
      data: res,
    };
  } catch {
    return {
      message: "添加失败",
      success: false,
    };
  }
}

// 添加客户团队
async function pushCustomerTeamId(content) {
  const { customerId, teamId } = content;
  const res = await db
    .collection("member")
    .updateOne({ _id: customerId }, { $addToSet: { teamId } });

  return {
    message: "添加成功",
    success: true,
    data: res,
  };
}

const common = require("../../common");

let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getCus":
      return await exports.getCus(content);
  }
};

// 获取客户信息
exports.getCus = async (context) => {
  const { corpId } = context;
  try {
    // 查询 customer-source 集合中的数据
    let res = await db.collection("customer-source").find({ corpId }).toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 插入数据时使用 common.generateRandomString(24) 生成 _id
exports.insertCus = async (context) => {
  const { corpId, customerData } = context;
  try {
    customerData._id = common.generateRandomString(24);
    await db.collection("customer-source").insertOne(customerData);
    return {
      success: true,
      message: "插入成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

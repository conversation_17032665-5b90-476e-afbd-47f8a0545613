const team = require("./corp-team");
const work = require("./work-schedule");
const corpMember = require("./corp-member");
const corpTemplate = require("./corp-template");
const corpDefaultInfo = require("./corp-default-info");
const transfer = require("./corp-member-transfer");
const corpTag = require("./corp-group-tag");
const corp = require("./corp-base");
const package = require("./corp-package-list");
const corpRole = require("./corp-role");
const projectManage = require("./project-manage");
const deptList = require("./dept-list");
const behaviorStatistics = require("./behavior-statistics");
const wecomToken = require("./wecom-token");
const corpCache = require("./corp-cache");
const corpAccount = require("./corp-account");
const projectIntent = require("./benefitManagement/project-intent");
const informationSource = require("./benefitManagement/information-source");
const hospitalArea = require("./hlw-hospital-area");
const hlwDeptList = require("./hlw-dept-list");
const hlwHospital = require("./hlw-hospital");
const recommendRecord = require("./recommend-record");
exports.main = async (event, db) => {
  switch (event.type) {
    case "getCorpInfo":
    case "getCustomCorpInfo":
    case "addCorpDisease":
    case "updateCorp":
    case "addCorp":
    case "getCorpList":
      return await corp.main(event, db);
    case "getPackageList":
      return await package.getPackageList(event, db);
    case "getCustomTeamData":
    case "getTeamBymember":
    case "getCorpTeams":
    case "deleteCorpTeam":
    case "getTeamById":
    case "updateTeam":
    case "getAllTeamByCorp":
    case "getTeamLeaderNumByUserId":
    case "getTeamByMainLeaderUserId":
    case "updateMemberRolesByTeam":
    case "getTeamData":
    case "getTeamNamesByTeamIds":
      return await team.main(event, db);
    case "getCorpMember":
    case "getCorpMainMember":
    case "updateCorpMember":
    case "upDateCorpMemberByUserId":
    case "addCorpMember":
    case "removeCorpMember":
    case "getCorpMemberByUserId":
    case "getCorpMemberData":
    case "getCorpMemberJob":
    case "getCorpUsers":
    case "getRolesMemberList":
    case "getSuperAdmin":
    case "getCorpMemberAndCustomorCount":
    case "removeAccount":
    case "getNotOpenedAccount":
    case "getOpenedAccount":
    case "getCustomMemberInfo":
    case "getCorpMemberByTeamsAndJobs":
    case "checkAdminRole":
    case "getCorpMemberHomepageInfo":
    case "addWechatFriend":
    case "removeWechatFriend":
    case "getWechatFriends":
    case "batchUpdateWechatFriends":
    case "corpMemberExist":
    case "getExternalUserIdByUserId":
    case "getCorpMemberByJobs":
    case "getAllCorpMember":
    case "getCorpMemberByHlwDept":
    case "addHlwMembers":
    case "batchUpdateSortOrder":
    case "getHlwDeptMembersWithSort":
    case "reorderHlwDeptMembers":
    case "removeHlwMemberFromDept":
    case "searchHlwMembers":
    case "searchOpenedAccounts":
    case "searchCorpMembers":
      return await corpMember.main(event, db);
    case "setWork":
    case "getWork":
    case "removeWork":
    case "closeWorkTodo":
      return await work.main(event, db);
    case "getCorpTemplate":
    case "getCurrentTemplate":
    case "getTemplateGroup":
    case "updateCorpTemplate":
    case "updateCorpTemplateStatus":
    case "getFilterFieldCorpTemplate":
      return await corpTemplate.main(event, db);
    case "upDatetransferTime":
    case "judgeTransferTime":
      return await transfer.main(event, db);
    case "addCorpDefaultInfo":
      return await corpDefaultInfo.addCorpDefaultInfo(event, db);
    case "getCorpTags":
    case "addCorpGroupTag":
    case "addCorpTagToGroup":
    case "updateCorpGroupTag":
    case "updateCorpTag":
    case "deleteCorpTag":
    case "deleteCorpGroupTag":
      return await corpTag.main(event, db);
    case "getRoles":
    case "getRolesByRoleId":
    case "addRole":
    case "updateRole":
    case "deleteRole":
    case "updateRolesCorpId":
    case "checkRoleExistByIds":
      return await corpRole.main(event, db);
    case "addProject":
    case "updateProject":
    case "deleteProject":
    case "getProjectList":
    case "getProjectListByCateIds":
    case "getProjectListWithCates":
    case "addProjectCate":
    case "updateProjectCate":
    case "deleteProjectCate":
    case "getProjectCateList":
    case "sortProjectCate":
    case "ifProjectCateExist":
    case "getProjectAllCount":
    case "getProjectCreateTreatmentOrder":
    case "getProjectNames":
    case "getProjectDiscount":
    case "getDeptProjectCate":
    case "getProjectCateGroup":
    case "getProjectAllCateGroup":
      return await projectManage.main(event, db);
    case "getDeptList":
    case "addDept":
    case "updateDept":
    case "deleteDept":
    case "sortDeptList":
    case "ifCateExist":
    case "addDeptUserId":
    case "deleteDeptUserId":
    case "getUsersByDept":
    case "addDeptStaff":
    case "removeDeptStaff":
    case "getDeptStaff":
    case "updateStaffDept":
      return await deptList.main(event, db);
    case "getBehaviorStatistics":
    case "addBehaviorStatistics":
      return await behaviorStatistics.main(event, db);
    case "updateSuiteToken":
    case "getSuiteToken":
      return await wecomToken.main(event, db);
    case "getCorpCache":
    case "createCorpCache":
      return await corpCache.main(event, db);
    case "updatePassword":
    case "login":
      return await corpAccount.main(event, db);
    case "getProjectIntentList":
    case "addProjectIntent":
    case "updateProjectIntent":
    case "updateProjectIntentStatus":
    case "getProjectIntentNames":
    case "transformProjectData":
      return await projectIntent.main(event, db);
    case "getSourceList":
    case "updateSourceStatus":
    case "getSourceCateList":
    case "addSourceCate":
    case "deleteSourceCate":
    case "updateSourceCate":
    case "sortSourceCate":
    case "addSource":
    case "updateSource":
      return await informationSource.main(event, db);
    case "addHospitalArea":
    case "updateHospitalArea":
    case "deleteHospitalArea":
    case "getHospitalAreaList":
    case "getHospitalAreaById":
    case "checkAreaIdExists":
    case "sortHospitalAreaList":
      return await hospitalArea.main(event, db);
    case "addHlwDept":
    case "updateHlwDept":
    case "deleteHlwDept":
    case "getHlwDeptList":
    case "getHlwDeptById":
    case "getHlwDeptTree":
    case "checkHlwDeptIdExists":
    case "sortHlwDeptList":
    case "getHlwDeptsByParent":
      return await hlwDeptList.main(event, db);
    case "getHlwHospitalMembers":
    case "getHlwHospitalMemberById":
    case "getHlwHospitalMemberByUserId":
    case "getHlwHospitalMemberByWorkId":
    case "addHlwHospitalMember":
    case "updateHlwHospitalMember":
    case "removeHlwHospitalMember":
    case "deleteHlwHospitalMember":
    case "getHlwHospitalMembersByDept":
    case "getHlwHospitalMembersByJob":
    case "getRecommendedMembers":
    case "updateMemberRecommendStatus":
    case "batchUpdateMembers":
    case "searchMembers":
    case "getMembersByOutpatientDept":
    case "hlwHospitalMemberExist":
    case "getHlwHospitalMemberStats":
    case "batchUpdateMembersSortOrder":
      return await hlwHospital.main(event, db);
    case "addRecommendRecord":
    case "updateRecommendRecord":
    case "deleteRecommendRecord":
    case "getRecommendRecord":
    case "getRecommendRecordList":
      return await recommendRecord.main(event, db);
    default:
      return {
        success: false,
        message: "参数错误",
      };
  }
};

const jwt = require("../utils/jwt");
const member = require("../member");
const { getDatabase } = require("../mongodb");
const { ObjectId } = require("mongodb");
const multer = require("multer");
const timeout = require("connect-timeout");
const logger = require("../utils/logger");
const path = require("path");
const basePath = process.env.CONFIG_STATIC_BASE_URL;
const isBuilded = process.env.CONFIG_IS_BUNDLED === "YES" || false;
const dayjs = require("dayjs");
const dirPrefix = isBuilded ? "../" : "../../";

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 获取当前日期，格式为 YYYY-MM-DD
    const date = new Date();
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const dd = String(date.getDate()).padStart(2, "0");
    const dateDir = `${yyyy}-${mm}-${dd}`;
    const uploadDir = path.join(
      __dirname,
      `${dirPrefix}uploads/yizhipai`,
      dateDir
    );
    require("fs").mkdirSync(uploadDir, { recursive: true });
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(
      null,
      Date.now() +
        "-" +
        new ObjectId().toString() +
        path.extname(file.originalname)
    );
  },
});
const uploadCustomPhotos = multer({
  storage,
  limits: {
    fileSize: 20 * 1024 * 1024, // 单文件最大10MB
    files: 35, // 最多30张
    fieldSize: 300 * 1024 * 1024, // 总体最大300MB
  },
}).array("files", 35); // files为字段名，最多30张

async function handleUploadCustomPhotos({ files, body }) {
  // 组装返回数据
  const fileInfos = (files || []).map((f) => {
    const relativePath = path
      .relative(path.join(__dirname, dirPrefix), f.path)
      .replace(/\\/g, "/");
    return {
      originalname: f.originalname,
      filename: f.filename,
      size: f.size,
      path: `${basePath}${relativePath}`,
    };
  });

  return fileInfos;
}
exports.useYizhipai = (app) => {
  app.post("/getToken", async (req, res) => {
    const requestData = req.body;
    try {
      const account =
        typeof requestData.account === "string"
          ? requestData.account.trim()
          : "";
      const password =
        typeof requestData.password === "string"
          ? requestData.password.trim()
          : "";
      const organ =
        typeof requestData.organ === "string" ? requestData.organ.trim() : "";
      if (account && password) {
        const token = jwt.encodeJWT({
          userId: `THIRD_${account}`,
          corpId: organ,
        });
        res.json({
          statusCode: 200,
          message: "获取token成功",
          data: token,
        });
      } else {
        res.json({
          statusCode: 500,
          message: "账号或密码不能为空",
          data: null,
        });
      }
    } catch (err) {
      res.json({
        statusCode: 500,
        message: err.message || "获取token失败!",
      });
    }
  });

  app.post("/getCustomInfo", async (req, res) => {
    const requestData = req.body;
    try {
      const token = req.headers.token || "";
      const valid = jwt.verifyJWT(token);
      if (!valid) {
        return res.json({
          statusCode: 500,
          message: "登录信息已过期或者无效，请重新登录!",
        });
      }
      const searchKey =
        typeof requestData.searchKey === "string"
          ? requestData.searchKey.trim()
          : "";
      if (!searchKey) {
        return res.json({
          statusCode: 500,
          message: "查询关键字不能为空!",
        });
      }
      const db = await getDatabase("admin");
      const { success, data, message } = await member.main(
        { type: "searchCustomerByKeyword", keyword: searchKey },
        db
      );
      if (success) {
        res.json({
          statusCode: 200,
          message: "获取客户信息成功",
          data: data || [],
        });
      } else {
        res.json({
          statusCode: 500,
          message: message || "获取客户信息失败!",
        });
      }
    } catch (err) {
      res.json({
        statusCode: 500,
        message: err.message || "获取客户信息失败!",
      });
    }
  });

  app.post("/uploadCustomPhotos", timeout("180s"), async (req, res) => {
    const token = req.headers.token || "";
    if (!jwt.verifyJWT(token)) {
      logger.warn("图片上传失败，token无效");
      return res.status(401).json({
        statusCode: 401,
        message: "登录信息已过期或者无效，请重新登录!",
      });
    }
    uploadCustomPhotos(req, res, async function (err) {
      if (err) {
        logger.info(`接口请求:${JSON.stringify(requestData)}`);
        logger.error("图片上传失败,err.message");
        return res.json({
          statusCode: 500,
          message: "图片上传失败!",
          error: err.message,
        });
      }
      const requestData = req.body;
      const projectId =
        typeof requestData.projectId === "string"
          ? requestData.projectId.trim()
          : "";
      const groupId =
        typeof requestData.groupId === "string"
          ? requestData.groupId.trim()
          : "";
      const customerId =
        typeof requestData.customerId === "string"
          ? requestData.customerId.trim()
          : "";
      const projectTime = Number(requestData.projectTime) > 0 && dayjs(Number(requestData.projectTime)).isValid() ?
        dayjs(Number(requestData.projectTime)).valueOf() : "";
      const projectRemark =
        typeof requestData.projectRemark === "string"
          ? requestData.projectRemark.trim()
          : "";
      const timeInterval = [0, 1, 2].includes(Number(requestData.timeInterval)) ? Number(requestData.timeInterval) : ""
      const extraInfos =
        typeof requestData.extraInfos === "string"
          ? requestData.extraInfos.trim()
          : "";
      const partial = ['true', 'false'].includes(requestData.partial) ? requestData.partial === 'true' : ""
      const selectProId =
        typeof requestData.selectProId === "string"
          ? requestData.selectProId.trim()
          : "";
      if (!customerId) {
        logger.info(`接口请求:${JSON.stringify(requestData)}`);
        logger.info(`接口输出:客户id不能为空`);
        return res.json({
          statusCode: 500,
          message: "客户id不能为空!",
        });
      }
      if (!Array.isArray(req.files) || req.files.length === 0) {
        logger.info(`接口请求:${JSON.stringify(requestData)}`);
        logger.info("图片上传失败,没有上传文件");
        return res.json({
          statusCode: 500,
          message: "没有上传文件!",
        });
      }
      const db = await getDatabase("admin");
      const { success, message } = await member.main(
        {
          type: "doesCustomerExist",
          _id: customerId,
        },
        db
      );
      if (!success) {
        logger.info(`接口请求:${JSON.stringify(requestData)}`);
        logger.info(`接口输出:${message || "客户不存在!"}`);
        return res.json({
          statusCode: 500,
          message: message || "客户不存在!",
        });
      }
      try {
        const fileInfos = await handleUploadCustomPhotos({
          files: req.files || [],
        });
        const result = await db
          .collection("yizhipai-custom-photos")
          .insertOne({
            projectId,
            groupId,
            customerId,
            projectTime,
            projectRemark,
            timeInterval,
            extraInfos,
            partial,
            selectProId,
            files: fileInfos,
            createTime: Date.now()
          });
        logger.info(`接口请求:${JSON.stringify(requestData)}`);
        logger.info(`图片上传成功,${result.insertedId}`);
        res.json({
          statusCode: 200,
          message: "图片上传成功",
        });
      } catch (err) {
        logger.info(`接口请求:${JSON.stringify(requestData)}`);
        logger.info(`图片上传处理异常,${err.message || "图片上传失败!"}`);
        res.json({
          statusCode: 500,
          message: err.message || "图片上传失败!",
        });
      }
    });
  });
  app.post("/getYoucanData/getYizhipaiPhotos", async (req, res) => {
    const requestData = req.body;

    const customerId = typeof requestData.customerId === "string" ? requestData.customerId.trim() : "";
    if (!customerId) {
      return res.json({ success: false, message: '客户id不能为空' })
    }
    const query = { customerId };
    if (Array.isArray(requestData.timeIntervals)) {
      query.timeInterval = { $in: requestData.timeIntervals }
    }
    const db = await getDatabase("admin");
    const data = await db.collection("yizhipai-custom-photos").find(query, { projection: { timeInterval: 1, 'files.path': 1, 'files.originalname': 1, createTime: 1 } }).sort({ createTime: -1 }).toArray();
    return res.json({ success: true, data, message: '查询成功' })
  });
};

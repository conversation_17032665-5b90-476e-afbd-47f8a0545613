// 测试修复后的搜索函数参数处理
const corpMember = require('./corp/corp-member/index');

console.log('测试参数处理修复...');

// 模拟请求数据结构
const testContext = {
  type: "searchCorpMembers",
  params: {
    corpId: "wwe3fb2faa52cf9dfb",
    mobile: "13455667788"
  },
  pageSize: 20,
  page: 1,
  corpId: "wwe3fb2faa52cf9dfb"
};

console.log('测试数据:', JSON.stringify(testContext, null, 2));

// 测试参数解构
const testParameterExtraction = () => {
  console.log('\n=== 测试参数解构 ===');
  
  const { page = 1, pageSize = 20, params = {}, corpId: contextCorpId } = testContext;
  const {
    corpId: paramsCorpId,
    anotherName,
    mobile,
    deptIds,
    job
  } = params;
  
  const corpId = contextCorpId || paramsCorpId;
  
  console.log('解构结果:');
  console.log('- page:', page);
  console.log('- pageSize:', pageSize);
  console.log('- contextCorpId:', contextCorpId);
  console.log('- paramsCorpId:', paramsCorpId);
  console.log('- 最终corpId:', corpId);
  console.log('- mobile:', mobile);
  console.log('- anotherName:', anotherName);
  console.log('- deptIds:', deptIds);
  console.log('- job:', job);
  
  // 构建查询条件
  const query = { corpId };
  
  if (mobile && mobile.trim()) {
    const mobileRegex = new RegExp(mobile.trim(), 'i');
    query.mobile = mobileRegex;
    console.log('- mobile查询条件:', query.mobile);
  }
  
  console.log('最终查询条件:', query);
};

testParameterExtraction();

// 测试实际函数调用（不连接数据库）
const testFunctionCall = async () => {
  console.log('\n=== 测试函数调用 ===');
  
  // 模拟没有数据库连接的情况
  const originalDb = global.db;
  global.db = null;
  
  try {
    const result = await corpMember.searchCorpMembers(testContext);
    console.log('函数调用结果:', result);
  } catch (error) {
    console.log('预期的数据库错误:', error.message);
  } finally {
    global.db = originalDb;
  }
};

testFunctionCall();

const winston = require("winston");
const path = require("path");
const fs = require("fs");
const DailyRotateFile = require("winston-daily-rotate-file");
const appRoot = require("app-root-path").path;

// 清理15天前的日志文件夹
function cleanOldLogs() {
  const logsBaseDir = path.join(appRoot, "logs");

  // 确保日志基础目录存在
  if (!fs.existsSync(logsBaseDir)) return;

  // 获取15天前的日期
  const fifteenDaysAgo = new Date();
  fifteenDaysAgo.setDate(fifteenDaysAgo.getDate() - 15);
  const cutoffDate = fifteenDaysAgo.toISOString().split("T")[0]; // 格式：YYYY-MM-DD

  try {
    // 读取logs目录下的所有文件夹
    const logDirs = fs.readdirSync(logsBaseDir);

    // 遍历所有日志文件夹
    logDirs.forEach((dir) => {
      // 检查文件夹名是否是有效日期格式
      if (/^\d{4}-\d{2}-\d{2}$/.test(dir)) {
        // 如果文件夹日期早于截止日期，则删除
        if (dir < cutoffDate) {
          const dirPath = path.join(logsBaseDir, dir);
          fs.rmSync(dirPath, { recursive: true, force: true });
          console.log(`已删除旧日志文件夹: ${dir}`);
        }
      }
    });
  } catch (err) {
    console.error("清理旧日志文件失败:", err); 
  }
}

// 执行清理
cleanOldLogs();

// 获取当前日期，用于创建日志文件夹
const currentDate = new Date().toISOString().split("T")[0]; // 格式：YYYY-MM-DD
const logDir = path.join(appRoot, "logs", currentDate); // 日志目录，按日期分文件夹

// 确保日志目录存在，如果不存在则创建
if (!fs.existsSync(logDir)) {
  try {
    fs.mkdirSync(logDir, { recursive: true });
  } catch (err) {
    console.error("日志文件夹创建失败:", err);
  }
}

// 日志文件路径
const logFilePath = path.join(logDir, "app-%DATE%.log");

// 创建一个日志记录器
const logger = winston.createLogger({
  level: "info", // 默认日志级别
  format: winston.format.combine(
    winston.format.timestamp({
      format: () => new Date().toLocaleString(), // 使用本地时间
    }),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} ${level}: ${message}`;
    })
  ),
  transports: [
    // 输出到控制台
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(), // 控制台输出带颜色
        winston.format.simple() // 简单的日志输出格式
      ),
    }),

    // 输出到按日期分目录的文件
    new DailyRotateFile({
      filename: logFilePath,
      datePattern: "YYYY-MM-DD", // 文件名中的日期格式
      maxSize: "20m", // 限制单个日志文件大小为20MB
      maxFiles: "14d", // 保留14天的日志文件
    }),
  ],
});

function info(item, item1 = "") {
  let title = typeof item === "object" ? JSON.stringify(item) : item;
  let title2 = typeof item1 === "object" ? JSON.stringify(item1) : item1;
  const title3 = title2 ? `${title},${title2}` : title;
  logger.info(title3);
}

function error(item, item1 = "") {
  let title = typeof item === "object" ? JSON.stringify(item) : item;
  let title2 = typeof item1 === "object" ? JSON.stringify(item1) : item1;
  const title3 = title2 ? `${title},${title2}` : title;
  logger.error(title3);
}

function warn(item, item1 = "") {
  let title = typeof item === "object" ? JSON.stringify(item) : item;
  let title2 = typeof item1 === "object" ? JSON.stringify(item1) : item1;
  const title3 = title2 ? `${title},${title2}` : title;
  logger.warn(title3);
}

// 设置定时任务，每天执行一次清理
setInterval(cleanOldLogs, 24 * 60 * 60 * 1000);

module.exports = {
  info,
  error,
  warn,
};

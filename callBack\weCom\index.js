const request = require("../request");
const { corpUrlMap, suiteSecretList, fixedUrls } = require("./config");
const logger = require("../../utils/logger");
const api = require("../../api.js");
let baseURL = "";
let useApiMode = false;

/**
 * 通用请求函数
 * @param {Object} params - 请求参数
 * @param {string} url - 请求URL (仅在useApiMode为false时使用)
 * @returns {Promise<Object>} - 返回请求结果
 */
async function makeRequest(params, type) {
  try {
    logger.info("============>使用baseURL模式", useApiMode);
    if (useApiMode) {
      if (type === "weCom") return await api.getWecomApi(params);
      else if (type === "corp") return await api.getCorpApi(params);
    } else {
      return await request.post(`${baseURL}getYoucanData/${type}`, params);
    }
  } catch (error) {
    return {
      success: false,
      error,
    };
  }
}

/**
 *
 * @param {*} e
 * ToUesrname
 * AgentID
 * Encrypt
 * SuiteTicket
 * InfoType 推送类型
 */
exports.getWeComData = async (e) => {
  logger.info("收到企业微信回调", {
    AuthCorpId: e.AuthCorpId,
    InfoType: e.InfoType,
  });

  if (e.AuthCorpId && corpUrlMap[e.AuthCorpId]) {
    baseURL = corpUrlMap[e.AuthCorpId];
    useApiMode = false;
    logger.info("使用baseURL模式", { baseURL, AuthCorpId: e.AuthCorpId });
  } else {
    useApiMode = true;
    logger.info("使用API模式", {
      hasAuthCorpId: !!e.AuthCorpId,
      hasCorpUrlMapping: !!corpUrlMap[e.AuthCorpId],
      isProduction: process.env.NODE_ENV === "production",
      AuthCorpId: e.AuthCorpId,
    });
  }
  if (
    e.InfoType &&
    e.InfoType === "suite_ticket" &&
    e.SuiteId !== "ww9ffaf7918c964aa4"
  ) {
    // 获取到SuiteTicket
    // 测试环境不调用 getSuiteAccessToken
    await getSuiteAccessToken(e);
    // 通过 SuiteTicket 获取第三方应用 token
  } else if (e.InfoType && e.InfoType === "create_auth") {
    if (e.SuiteId !== "ww9ffaf7918c964aa4") await getCorpAuthCode(e);
  } else if (e.InfoType && e.InfoType === "cancel_auth") {
    if (e.SuiteId !== "ww9ffaf7918c964aa4") await cancelCorpAuth(e);
  } else if (e.InfoType && e.InfoType === "conversation_new_message") {
  } else if (
    e.InfoType &&
    e.InfoType === "change_external_contact" &&
    e.ChangeType === "add_external_contact"
  ) {
    logger.info("授权企业中配置了客户联系功能的成员添加外部联系人时", e);
    const addRes = await addExternalContact(e);
    logger.info("添加外部联系人回调", addRes);
  } else if (
    e.InfoType &&
    e.InfoType === "change_external_contact" &&
    e.ChangeType === "del_external_contact"
  ) {
    logger.info("删除外部联系人触发", e);
    await removeWechatFriend(e);
  } else if (
    e.InfoType &&
    e.InfoType === "change_external_contact" &&
    e.ChangeType === "edit_external_contact"
  ) {
    logger.info("编辑外部联系人触发", e);
    const editRes = await editExternalContact(e);
    logger.info("编辑外部联系人回调", editRes);
  } else if (e.InfoType && e.InfoType === "change_external_tag") {
    logger.info("编辑客户标签触发", e);
    await syncCorpTag(e);
  } else if (e.Event && e.Event === "program_notify") {
    logger.info("程序模板消息回调", e);
  }
};

async function getCorpAuthCode(e) {
  const params = {
    type: "getCorpAuthCode",
    authCode: e.AuthCode,
    SuiteId: e.SuiteId,
  };
  // 会话存档授权
  if (e.SuiteId === "ww9ffaf7918c964aa4" && e.AuthCorpId) {
    url = `${baseURL}getYoucanData/sessionArchive`;
    params.type = "createSessionArchiveAuth";
    params.corpId = e.AuthCorpId;
    delete params.authCode;
    delete params.SuiteId;
  }
  return await makeRequest(params, "weCom");
}
async function addExternalContact(e) {
  const params = {
    type: "addExternalContact",
    CorpID: e.AuthCorpId,
    State: e.State,
    ExternalUserID: e.ExternalUserID,
    UserID: e.UserID,
    WelcomeCode: e.WelcomeCode,
  };

  return await makeRequest(params, "weCom");
}
async function editExternalContact(e) {
  const params = {
    type: "editExternalContact",
    corpId: e.AuthCorpId,
    externalUserId: e.ExternalUserID,
    userId: e.UserID,
  };

  return await makeRequest(params, "weCom");
}

async function syncCorpTag(e) {
  const params = {
    type: "syncCorpTag",
    corpId: e.AuthCorpId,
    externalUserId: e.ExternalUserID,
    tagType: e.TagType,
    id: e.Id,
    changeType: e.ChangeType,
  };

  return await makeRequest(params, "weCom");
}

async function cancelCorpAuth(e) {
  const params = {
    type: "updateCorp",
    corpId: e.AuthCorpId,
    params: {
      permanent_code: "",
    },
  };

  return await makeRequest(params, "corp");
}

async function removeWechatFriend(e) {
  const params = {
    type: "removeWechatFriend",
    corpId: e.AuthCorpId,
    external_userid: e.ExternalUserID,
    userid: e.UserID,
  };

  return await makeRequest(params, "corp");
}

// 获取应用token
async function getSuiteAccessToken(e) {
  const params = {
    type: "getSuiteAccessToken",
    SuiteId: e.SuiteId,
    SuiteTicket: e.SuiteTicket,
    suiteSecret: suiteSecretList[e.SuiteId],
    suiteType: "suiteToken",
  };

  console.log("获取suite_access调用传参数", params);
  logger.info("获取suite_access调用传参数", params);
  // 特殊处理：同时发送两个请求
  await api.getWecomApi(params);

  const url = `${fixedUrls.hz}getYoucanData/weCom`;
  try {
    const res = await api.getWecomApi(params);
    console.log("医客通获取suite_access调用传参数", res);
    logger.info("医客通获取suite_access调用传参数", res);
    // 同时发送两个请求
    if (e.SuiteId === "wwc1c7baebf62eab4a") {
      const res1 = await request.post(url, params);
      console.log("杭整整形获取suite_access调用传参数", res1);
      logger.info("杭整整形获取suite_access调用传参数", res1);
    }
  } catch (error) {
    return {
      success: false,
      error,
    };
  }
}

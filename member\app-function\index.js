const api = require("../../api");
async function getCorpMemberByTeamsAndJobs({ corpId, teamIds, jobs }) {
  let result = await api.getCorpApi({
    type: "getCorpMemberByTeamsAndJobs",
    corpId,
    teamIds,
    jobs,
  });

  return result.data;
}

async function createGroupmsgTask({ corpId, params }) {
  let result = await api.getGroupMsgApi({
    type: "createGroupmsgTask",
    corpId,
    params,
  });
  return result;
}

async function getWecomGroupmsgSendResult(data) {
  let result = await api.getGroupMsgApi({
    type: "getWecomGroupmsgSendResult",
    ...data,
  });
  return result;
}

async function getAccessToken({ corpId }) {
  let result = await api.getWecomApi({
    type: "getAccessToken",
    corpId,
  });
  return result.accessToken;
}

async function addServiceRecord(params) {
  let result = await api.getTodoApi({
    type: "addServiceRecord",
    ...params,
  });
  return result;
}

async function executeManagementPlanTodo(params) {
  let result = await api.getTodoApi({
    type: "executeManagementPlanTodo",
    ...params,
  });
  return result;
}

module.exports = {
  getCorpMemberByTeamsAndJobs,
  createGroupmsgTask,
  getWecomGroupmsgSendResult,
  getAccessToken,
  addServiceRecord,
  executeManagementPlanTodo,
};

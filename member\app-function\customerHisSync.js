const api = require("../../api");
const funName = "customerHisSync";
async function getHisFeeRecord({ corpId, customerNumber, startTime, endTime }) {
  return await api.getCustomerHisSyncApi({
    corpId,
    customerNumber,
    startTime,
    endTime,
    type: "getHisFeeRecord",
  });
}

async function getHisInHospitalRecord({
  corpId,
  customerNumber,
  startTime,
  endTime,
}) {
  return await api.getCustomerHisSyncApi({
    type: "getHisInHospitalRecord",
    corpId,
    idNo: customerNumber,
    startTime,
    endTime,
  });
}
async function getHisOutHospitalRecord({
  corpId,
  customerNumber,
  startTime,
  endTime,
}) {
  return await api.getCustomerHisSyncApi({
    type: "getHisOutHospitalRecord",
    corpId,
    idNo: customerNumber,
    startTime,
    endTime,
  });
}
module.exports = {
  getHisFeeRecord,
  getHisInHospitalRecord,
  getHisOutHospitalRecord,
};

const { getDatabase } = require("../../mongodb");
const tencentIM = require("../tencent-im");
/**
 *
 * orderStatus  completed(已完成) processing(处理中) pending(待处理) cancelled(已取消) finished(已结束)
 * syncOtherMachine 若不希望将消息同步至 From_Account，则 SyncOtherMachine 填写2；若希望将消息同步至 From_Account，则 SyncOtherMachine 填写1。
 * MsgType TIMTextElem（文本消息）  TIMLocationElem（位置消息） TIMFaceElem（表情消息） TIMCustomElem（自定义消息） TIMSoundElem（语音消息） TIMImageElem（图像消息） TIMFileElem（文件消息） TIMVideoFileElem（视频消息）
 * MsgContent.Text 文本消息内容
 * MsgContent.Data 自定义消息类型 SUBMITMEDICALADVICE(提交医嘱)  FINISHED(问诊结束)   COMPLETED(问诊完成)  CANCELLED(问诊取消)   STARTCONSULT(开始问诊)   ACCEPTCONSULT(接诊)
 * MsgContent.Desc notification
 *
 */
const handEvent = {
  finished: "FINISHED", // 问诊结束
  completed: "COMPLETED", // 问诊完成
  cancelled: "CANCELLED", // 问诊取消
  startConsult: "STARTCONSULT", // 开始问诊
  acceptConsult: "ACCEPTCONSULT", // 接诊
  submitMedicalAdvice: "SUBMITMEDICALADVICE", // 提交医嘱
};
exports.updateOrderStatusAndSendNotification = async (item) => {
  const { orderId, doctorCode, corpId, msgType, expireTime } = item;
  const db = await getDatabase("Internet-hospital");
  let orderStatus = "";
  let notification = "";
  let syncOtherMachine = 2;
  let formAccount = "";
  let toAccount = "";
  switch (msgType) {
    case "submitMedicalAdvice":
      // 提交医嘱 更新问诊状态为已完成 completed 并向医生发送一条系统消息
      orderStatus = "completed";
      formAccount = orderId;
      toAccount = doctorCode;
      notification = "";
      break;
    case "FINISHED":
      // 问诊结束 更新问诊状态为已结束 finished 并向医生和患者同时发送一条系统消息
      orderStatus = "finished";
      formAccount = orderId;
      toAccount = doctorCode;
      syncOtherMachine = 1;
      notification = "";
      break;
    case "COMPLETED":
      // 问诊完成 更新问诊状态为已完成 completed 并向医生发送一条系统消息,  医嘱已提交
      orderStatus = "completed";
      formAccount = orderId;
      toAccount = doctorCode;
      notification = "";
      break;
    case "CANCELLED":
      // 问诊取消 一般为超时未处理, 更新问诊状态为已取消 cancelled 并向患者发送一条系统消息
      orderStatus = "cancelled";
      formAccount = doctorCode;
      toAccount = orderId;
      syncOtherMachine = 1;
      notification = "";
      break;
    case "ACCEPTCONSULT":
      // 接诊 更新问诊状态为处理中 processing 并向患者发送一条系统消息
      orderStatus = "processing";
      formAccount = doctorCode;
      toAccount = orderId;
      syncOtherMachine = 2;
      notification = "医生已接受您的问诊请求";
      break;
    default:
      break;
  }
  let query = {
    orderStatus,
    updateTime: Date.now(),
  };
  if (expireTime) query.expireTime = expireTime;
  // 当医生开始处理, 开始加过期时间, 过期时间为30分钟
  await db.collection("consult-order").updateOne(
    { orderId },
    {
      $set: {
        orderStatus,
        updateTime: Date.now(),
      },
    }
  );
  await tencentIM({
    type: "sendSystemNotification",
    corpId,
    formAccount,
    toAccount,
    SyncOtherMachine: syncOtherMachine,
    msgBody: [
      {
        MsgType: "TIMCustomElem",
        MsgContent: {
          Data: msgType, //medical advice
          Desc: "notification",
          Ext: notification,
        },
      },
    ],
  });
};

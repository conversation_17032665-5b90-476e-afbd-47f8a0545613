const request = require("../request");
const api = require("../../api");
// 获取应用token
async function getSuiteAccessToken(e) {
  const { SuiteId, SuiteTicket, suiteSecret } = e;
  // 第三方应用凭证借口
  let url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_suite_token";
  let params = {
    suite_id: SuiteId,
    suite_secret: suiteSecret,
    suite_ticket: SuiteTicket,
  };
  let res = await request.main(url, params, "POST");
  const result = await api.getCorpApi({
    type: "updateSuiteToken",
    suiteToken: res.suite_access_token,
  });
  return {
    success: true,
    message: res,
    result,
  };
}

exports.getSuiteAccessToken = getSuiteAccessToken;

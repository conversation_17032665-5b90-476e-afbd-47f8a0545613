const schedule = require("node-schedule");
const corpId = ""; // 私有化部署的corpId
const api = require("./api");
const logger = require("./utils/logger");

exports.initSchedule = async () => {
  logger.info("定时任务触发");
  batchStorageSessionTrigger();
  triggeredAt7am();
  triggeredAt6am();
  triggeredAt8am();
};

async function batchStorageSessionTrigger() {
  schedule.scheduleJob("0 0 1 * * *", async () => {
    try {
      await updateAppointmentStatusToFailToHospital();
      await batchStorageSession();
      await batchCreateGroupMsgTaskByTodo();
    } catch (error) {
      logger.error("定时任务执行失败:", error);
    }
  });
}

function triggeredAt6am() {
  schedule.scheduleJob("0 0 6 * * *", async () => {
    try {
      await customerSopTaskTrigger();
    } catch (error) {
      logger.error("6点定时任务执行失败:", error);
    }
  });
}

// 凌晨7点触发
function triggeredAt7am() {
  schedule.scheduleJob("0 0 7 * * *", async () => {
    try {
      await updateExpireStatus();
      await pushtoDoCountToCorpApp();
      await getAllCorpYesterdayBehaviorData();
    } catch (error) {
      logger.error("7点定时任务执行失败:", error);
    }
  });
}

function triggeredAt8am() {
  schedule.scheduleJob("0 0 8 * * *", async () => {
    try {
      await createCurrentGroupMsgTask();
      await stopExpireGroupmsgTask();
    } catch (error) {
      logger.error("8点定时任务执行失败:", error);
    }
  });
}

// function triggeredForManagementPlan() {
//   schedule.scheduleJob("0 30 6 * * *", async () => {  // 例如设置在6:30执行
//     try {
//       await managementPlanTaskToEvents();
//     } catch (error) {
//       logger.error('管理计划任务执行失败:', error);
//     }
//   });
// }

async function batchStorageSession() {
  const params = {
    type: "batchAllCorpGetSessionArchive",
    corpId,
  };
  logger.info("batchStorageSession", params);
  const res = await api.getSessionArchive(params);
  logger.info(res);
}

// 更新待办事项过期状态
async function updateExpireStatus() {
  const params = {
    type: "updateExpireStatus",
    corpId,
  };
  const res = await api.getTodoApi(params);
  logger.info(res);
}
//管理计划任务每日定时触发生成待办事件
// managementPlanTaskToEvents
// async function managementPlanTaskToEvents() {
//   const params = {
//     type: "managementPlanTaskToEvents",
//     corpId,
//   };
//   const res = await api.getTodoApi(params);
//   logger.info(res);
// }
async function customerSopTaskTrigger() {
  const params = {
    type: "customerSopTaskTrigger",
    corpId,
  };
  const res = await api.getMemberApi(params);
  logger.info(res);
}

// 推送待办任务到应用
async function pushtoDoCountToCorpApp() {
  const params = {
    type: "pushtoDoCountToCorpApp",
    corpId,
  };
  const res = await api.getTodoApi(params);
  logger.info(res);
}
// 创建当前群发任务
async function createCurrentGroupMsgTask() {
  const params = {
    type: "createCurrentGroupMsgTask", // 修正为与函数名称匹配的type
    corpId,
  };
  const res = await api.getGroupMsgApi(params);
  logger.info(res);
}

// 停止过期的群发任务
async function stopExpireGroupmsgTask() {
  const params = {
    type: "stopExpireGroupmsgTask",
    corpId,
  };
  const res = await api.getGroupMsgApi(params);
  logger.info(res);
}

async function getAllCorpYesterdayBehaviorData() {
  const params = {
    type: "getAllCorpYesterdayBehaviorData",
    corpId,
  };
  const res = await api.getWecomApi(params);
  logger.info(res);
}

async function batchCreateGroupMsgTaskByTodo() {
  const params = {
    type: "batchCreateGroupMsgTaskByTodo",
    corpId,
  };
  const res = await api.getTodoApi(params);
  logger.info(res);
}

async function updateAppointmentStatusToFailToHospital() {
  const params = {
    type: "updateAppointmentStatusToFailToHospital",
  };
  const res = await api.getMemberApi(params);
  logger.info(res);
}

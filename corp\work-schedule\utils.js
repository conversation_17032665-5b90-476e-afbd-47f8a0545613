const types = require("./enum");
const WorkTypeEnum = types.WorkTypeEnum;
exports.verifyData = function (payload) {
  const { corpId, title, workType, workTime, workTimeStamp, memberId, teamId, teamName, workContent = "", isTodo, todoTime, taskContent = "", sendContent = "", pannedEventSendFile, advanceDay = "", externalUserId = "", customerName = "", advanceDayStr = "", doctorUserId = "", doctorWorkId = "", userId } = payload;
  let message = "";
  if (!title || title.trim() === "") {
    message = "标题不能为空";
  } else if (!WorkTypeEnum[workType]) {
    message = "日程类型无效";
  } else if (!workTime) {
    message = "执行时间不能为空";
  } else if (!(workTimeStamp > new Date().getTime())) {
    message = "执行时间不能是过去时间";
  } else if (!memberId && workType === "visit-reg") {
    message = "就诊人不能为空";
  }
  // else if (!teamId) {
  //   message = `${workType === 'visit-reg' ? '就诊' : '执行'}团队不能为空`
  // }
  else if (!workContent || workContent.trim() === "") {
    // message = '日程内容不能为空'
  } else if (!isTodo) {
    message = "请选择是否转待办单";
  } else if (isTodo === "YES" && !advanceDay && advanceDay !== 0) {
    message = "待办生效时间不能为空";
  } else if (isTodo === "YES" && !(todoTime > new Date().getTime())) {
    message = "待办生效时间不能是过去时间";
  } else if (isTodo === "YES" && (!taskContent || taskContent.trim() === "")) {
    message = "请输入待办内容";
  }
  if (message) {
    return { success: false, message };
  }
  return {
    success: true,
    data: {
      corpId,
      title,
      workType,
      workTime,
      workTimeStamp,
      memberId,
      teamId,
      userId,
      workContent,
      isTodo,
      advanceDay,
      taskContent,
      todoTime,
      externalUserId,
      customerName,
      teamName,
      advanceDayStr,
      doctorUserId,
      doctorWorkId,
      sendContent,
      pannedEventSendFile,
    },
  };
};

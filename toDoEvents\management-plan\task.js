const dayjs = require("dayjs");
const memberPlan = require("./member-plan");
const todoEvents = require("../to-do-events");
const utils = require("../utils");
const common = require("../../common");
let db = null;
let currentCorpId = null;
exports.main = async (content, DB) => {
  db = DB;
  currentCorpId = content.corpId;
  switch (content.type) {
    case "getPlanTask":
      return await this.getPlanTask(content);
    case "createPlanTask":
      return await this.createPlanTask(content);
    case "removePlanTask":
      return await this.removePlanTask(content);
    case "updatePlanTask":
      return await this.updatePlanTask(content);
    case "batchUpdateManageTask":
      return await this.batchUpdateManageTask(content);
    case "removeTaskForStopPlan":
      return await this.removeTaskForStopPlan(content);
    case "updateExecutedTaskStatus":
      return await updateExecutedTaskStatus(content);
    case "managementPlanTaskToEvents":
      return await managementPlanTaskToEvents(content);
  }
};

// 根据customerId , managementPlanEventId 获取任务
exports.getPlanTask = async (context) => {
  const { customerId, planId, memberPlanId } = context;
  try {
    let taskList = await db
      .collection("management-plan-task")
      .aggregate([
        {
          $match: {
            customerId,
            planId,
            memberPlanId,
          },
        },
        {
          $lookup: {
            from: "to-do-events",
            let: {
              taskId: "$taskId",
              memberPlanId: "$memberPlanId",
              customerId: "$customerId",
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$taskId", "$$taskId"] },
                      { $eq: ["$memberPlanId", "$$memberPlanId"] },
                      { $eq: ["$customerId", "$$customerId"] },
                    ],
                  },
                },
              },
            ],
            as: "events",
          },
        },
      ])
      .toArray();

    return {
      success: true,
      data: taskList,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 创建计划任务
exports.createPlanTask = async (context) => {
  const { params } = context;
  const {
    corpId,
    taskId,
    planId,
    planName,
    creatorUserId,
    customerId,
    customerName,
    sendContent,
    executeTeamId,
    executeTeamName,
    executorUserId,
    customerUserId,
    planExecutionTime,
    managementPlanEventId,
    eventType,
    taskContent,
    taskTime,
    timeType,
    memberPlanId,
    pannedEventSendFile = {},
  } = params;
  let query = {
    _id: common.generateRandomString(24),
    corpId,
    taskId,
    planId,
    planName,
    creatorUserId,
    customerId,
    customerName,
    sendContent,
    executeTeamId,
    executorUserId,
    customerUserId,
    managementPlanEventId,
    executeTeamName,
    pannedEventSendFile,
    eventType,
    taskContent,
    taskTime,
    timeType,
    memberPlanId,
    taskStatus: "unexecuted",
    createTime: dayjs().valueOf(),
  };
  const date = dayjs(planExecutionTime);
  if (timeType) {
    query["planExecutionTime"] = date.add(taskTime, timeType).valueOf();
  } else {
    query["planExecutionTime"] = date.add(taskTime, "day").valueOf();
  }
  try {
    // 判断 date 是否小于当前时间
    if (dayjs().isAfter(query["planExecutionTime"], "day")) {
      return {
        success: false,
        message: "计划执行时间不能小于当天",
      };
    }
    await db.collection("management-plan-task").insertOne(query);
    // 如果计划执行时间是当天，立即执行
    if (dayjs().isSame(query["planExecutionTime"], "day")) {
      await updateExecutedTaskStatus({ _id: query._id });
      await todoEvents.main(
        {
          type: "createEvents",
          params: query,
          corpId: currentCorpId,
        },
        db
      );
    }
    return {
      success: true,
      data: query._id,
      showTime: query["planExecutionTime"],
      flag: dayjs().isAfter(query["planExecutionTime"], "day"),
      message: "新增成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "新增失败",
    };
  }
};

// 删除未执行完节任务
exports.removeTaskForStopPlan = async (context) => {
  const { customerId, planId, corpId } = context;
  await db.collection("management-plan-task").deleteMany({
    corpId,
    customerId,
    planId,
    taskStatus: "unexecuted",
  });
};

// 删除任务
exports.removePlanTask = async (context) => {
  const { id, memberPlanId } = context;
  if (!id) {
    return {
      success: false,
      message: "任务id未传",
    };
  }
  let res = await db.collection("management-plan-task").deleteOne({ _id: id });
  await memberPlan.main(
    {
      type: "stopMemberMangePlan",
      memberPlanId,
      corpId: currentCorpId,
    },
    db
  );
  return {
    success: true,
    data: res,
    message: "删除成功",
  };
};

// 编辑任务
exports.updatePlanTask = async (context) => {
  const { id, params } = context;
  const {
    customerName,
    executionTime,
    executorUserId,
    executeTeamId,
    sendContent,
    taskContent,
    taskTime,
    eventStatus,
    planExecutionTime,
    pannedEventSendFile = {},
    timeType,
    eventType,
  } = params;
  const obj = {
    customerName,
    executionTime,
    executorUserId,
    executeTeamId,
    sendContent,
    taskContent,
    taskTime,
    timeType,
    eventStatus,
    planExecutionTime,
    pannedEventSendFile,
    eventType,
  };
  const query = {
    updateTime: dayjs().valueOf(),
  };
  for (let key in obj) {
    if (obj[key] || obj[key] == 0) query[key] = obj[key];
  }
  try {
    const res = await db
      .collection("management-plan-task")
      .updateOne({ _id: id }, { $set: query });
    // 如果计划执行时间是当天，立即执行
    const currentTask = await db
      .collection("management-plan-task")
      .findOne({ _id: id });
    if (
      (currentTask.timeType === "day" || !currentTask.timeType) &&
      dayjs().isSame(currentTask.planExecutionTime, "day")
    ) {
      await updateExecutedTaskStatus({ _id: id });
      await todoEvents.main(
        {
          type: "createEvents",
          params: currentTask,
          corpId: currentCorpId,
        },
        db
      );
    }
    return {
      success: true,
      data: res,
      currentTask,
      message: "编辑成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "编辑失败",
    };
  }
};

// 批量修改任务
exports.batchUpdateManageTask = async (context) => {
  const { customerIds, currentTeamId, params, corpId } = context;
  try {
    let query = {
      corpId,
      customerId: { $in: customerIds },
      taskStatus: "unexecuted",
      executeTeamId: currentTeamId,
    };
    const res = await db
      .collection("management-plan-task")
      .updateMany(query, { $set: params });
    return {
      success: true,
      data: res,
      message: "修改成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "修改失败",
    };
  }
};

/**
 * 更新任务状态为已执行
 * @param {string} _id 任务id
 * @returns
 */
async function updateExecutedTaskStatus({ _id }) {
  try {
    let res = await db
      .collection("management-plan-task")
      .updateOne({ _id }, { $set: { taskStatus: "executed" } });
    return {
      success: true,
      data: res,
      message: "修改成功",
    };
  } catch {
    return {
      success: false,
      message: "修改失败",
    };
  }
}

/**
 * 管理计划生成待办
 * @returns
 */
async function managementPlanTaskToEvents({ corpId }) {
  // 获取当天
  const todayTimeStart = dayjs().startOf("day").valueOf();
  const todayTimeEnd = dayjs().endOf("day").valueOf();
  let params = {
    planExecutionTime: { $gte: todayTimeStart, $lte: todayTimeEnd },
    taskStatus: "unexecuted",
  };
  if (corpId) params["corpId"] = corpId;
  const fetchData = async (page, pageSize, db) => {
    let data = await db
      .collection("management-plan-task")
      .find(params)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    return data;
  };
  let taskList = await utils.getAllData(fetchData, db);
  console.log("当天管理计划任务", taskList);
  if (!taskList && !Array.isArray(taskList)) return false;
  let arr = taskList.map((item) => {
    return tasktoEvents(item);
  });
  for (let i = 0; i < arr.length; i += 10) {
    const promiseArr = arr.slice(i, i + 10);
    await Promise.all(promiseArr);
  }
  return {
    success: true,
    message: "生成待办成功",
  };
}

// 管理计划任务生成待办
async function tasktoEvents(params) {
  // 任务创建时间
  await updateExecutedTaskStatus(params);
  await todoEvents.main(
    {
      type: "createEvents",
      params,
      corpId: currentCorpId,
    },
    db
  );
}

const dayjs = require("dayjs");

module.exports = async (item, db) => {
  switch (item.statsType) {
    case 'todayOrder':
      return await todayOrder(item, db);
    default:
      return { success: false, message: '未知统计类型' }
  }
};

async function todayOrder(params, db) {
  try {
    const data = await db.collection('consult-order').aggregate([
      { $match: { createTime: { $gte: dayjs().startOf('day').valueOf() } } },
      {
        $group: {
          _id: {
            orderStatus: "$orderStatus",
            payStatus: "$payStatus"
          },
          count: { $sum: 1 }  // 统计每个组合的数量
        }
      },
      {
        $project: {
          _id: 0,  // 如果不需要显示 _id
          orderStatus: "$_id.orderStatus",
          payStatus: "$_id.payStatus",
          count: 1
        }
      }
    ]).toArray();
    return { success: true, message: '查询成功', data }
  } catch (err) {
    return { success: false, message: err.message }
  }
}
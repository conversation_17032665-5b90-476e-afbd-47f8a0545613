const customer = require("../customer");
const utils = require("../utils");
const dayjs = require("dayjs");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "batchRemoveMember":
      return await this.batchRemoveMember(content);
    case "batchUpdateCustomerTeamIds":
      return await this.batchUpdateCustomerTeamIds(content);
  }
};

exports.batchRemoveMember = async (content) => {
  const { customerIds, corpId } = content;
  try {
    // 删除客户时, 去除客户的标签
    await deleteCustomerRemark(content);
    const res = await db.collection("member").deleteMany({
      corpId,
      _id: { $in: customerIds },
    });
    return {
      success: true,
      data: res,
      message: "客户删除成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "客户删除失败",
    };
  }
};

async function deleteCustomerRemark(context) {
  const { corpId, customerIds } = context;
  // 获取有外部联系人Id的客户
  const customerList = await db
    .collection("member")
    .find({
      corpId,
      _id: { $in: customerIds },
      externalUserId: { $ne: null, $ne: "", $exists: true },
    })
    .toArray();

  if (customerList.length === 0) return;

  // 相同的externalUserId 做去重
  const handler = async (item) => {
    await customer.main(
      {
        type: "editExternalContactRemark",
        corpId: item.corpId,
        externalUserId: item.externalUserId,
        deleteMemberId: item._id,
      },
      db
    );
  };
  await utils.processInBatches(customerList, handler, 10);
}

/**
 * 批量分配团队
 * @param {*} context
 * corpId 机构id
 * customerIds 客户id列表
 * batchTransferType 批量分配类型 replace 替换 merge 合并
 * params 更新参数
 * mergeField 合并字段
 */
exports.batchUpdateCustomerTeamIds = async (context) => {
  const { corpId, customerIds, operationType, teamIds, userId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!Array.isArray(customerIds) || customerIds.length === 0)
    return { success: false, message: "客户id不能为空" };

  // 批量生成服务记录
  await batchCreateServiceRecord({
    corpId,
    customerIds,
    teamIds,
    userId,
    operationType,
  });

  if (operationType === "replace") {
    await batchReplaceTeamdIds(context);
  } else if (operationType === "merge") {
    const res = await db
      .collection("member")
      .find({
        _id: { $in: customerIds },
        corpId,
      })
      .toArray();
    await batchMergeTeamIds(res, teamIds);
  }
  return { success: true, message: "批量分配团队成功" };
};

// 批量生成服务记录
async function batchCreateServiceRecord({
  corpId,
  customerIds,
  teamIds,
  userId,
  operationType,
}) {
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!Array.isArray(customerIds) || customerIds.length === 0)
    return { success: false, message: "客户id不能为空" };

  for (const customerId of customerIds) {
    await createServiceRecordTask({
      corpId,
      customerId,
      teamIds,
      userId,
      operationType,
    });
  }
}

async function createServiceRecordTask({
  corpId,
  teamIds,
  customerId,
  userId,
  operationType,
}) {
  const member = await db.collection("member").findOne({ _id: customerId });
  if (!member) return;

  // 添加服务记录
  let query = {
    _id: common.generateRandomString(24),
    eventType: "",
    customerId,
    executorUserId: userId,
    creatorUserId: userId,
    corpId,
    customerName: member.name,
    createTime: dayjs().valueOf(),
    executionTime: dayjs().valueOf(),
    customerUserId: member.externalUserId,
  };

  if (!userId) return;

  let removeTeamIds = [];
  if (!member.teamId) member.teamId = [];
  if (typeof member.teamId === "string") member.teamId = [member.teamId];
  let addTeamIds = teamIds.filter((item) => !member.teamId.includes(item));

  if (operationType === "replace") {
    removeTeamIds = member.teamId.filter((item) => !teamIds.includes(item));
  }

  if (addTeamIds.length > 0) {
    const teamNames = await getTeamNamesByTeamIds({
      corpId,
      teamIds: addTeamIds,
    });
    query.eventType = "adminAllocateTeams";
    query.taskContent = `管理员把${member.name}分配给“${teamNames.join(
      "、"
    )}“团队`;
    query.transferToTeamIds = addTeamIds;
    await db.collection("serviceRecord").insertOne(query);
  }

  if (removeTeamIds.length > 0) {
    const teamNames = await getTeamNamesByTeamIds({
      corpId,
      teamIds: removeTeamIds,
    });
    query._id = common.generateRandomString(24);
    query.eventType = "adminRemoveTeams";
    query.removeTeamIds = removeTeamIds;
    query.taskContent = `管理员解除${member.name}与”${teamNames.join(
      "、"
    )}“团队的关联`;
    await db.collection("serviceRecord").insertOne(query);
  }
}

async function getTeamNamesByTeamIds({ corpId, teamIds }) {
  const teams = await db
    .collection("team")
    .find({
      corpId,
      teamId: { $in: teamIds },
    })
    .toArray();
  return teams.map((item) => item.name);
}

// 批量替换团队
async function batchReplaceTeamdIds(context) {
  const { corpId, customerIds, teamIds } = context;
  await db
    .collection("member")
    .updateMany(
      { corpId, _id: { $in: customerIds } },
      { $set: { teamId: teamIds } }
    );
}

// 批量合并团队
async function batchMergeTeamIds(customerList, teamIds) {
  const bulkOps = customerList.map((item) => {
    let { _id, teamId = [] } = item;
    if (typeof teamId === "string") teamId = [teamId];
    const newTeamIds = Array.from(new Set([...teamId, ...teamIds]));
    return {
      updateOne: {
        filter: { _id },
        update: { $set: { teamId: newTeamIds } },
      },
    };
  });

  try {
    await db.collection("member").bulkWrite(bulkOps);
    return { success: true, message: "修改成功" };
  } catch (error) {
    return { success: false, message: "修改失败" };
  }
}

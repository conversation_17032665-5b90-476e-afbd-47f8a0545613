const sourceCate = require('./cate');
const dayjs = require('dayjs');
const common = require('../../common');
let db = null;

exports.main = async (content, mongodb) => {
  db = mongodb;
  switch (content.type) {
    case 'addInfoSource':
      return await exports.addInfoSource(content);
    case 'updateInfoSource':
      return await exports.updateInfoSource(content);
    case 'deleteInfoSource':
      return await exports.deleteInfoSource(content);
    case 'getInfoSourceList':
      return await exports.getInfoSourceList(content);
    case 'addInfoSourceCate':
    case 'updateInfoSourceCate':
    case 'deleteInfoSourceCate':
    case 'getInfoSourceCateList':
    case 'sortInfoSourceCate':
    case 'ifInfoCateExist':
      return await sourceCate.main(content, db);
  }
};

// 新增信息来源
exports.addInfoSource = async (content) => {
  const { corpId, params } = content;
  if (!corpId) return { success: false, message: '机构id不能为空' };
  try {
    const res = await db.collection('info-source-list').insertOne({
      _id: common.generateRandomString(24),
      corpId,
      ...params,
      status: 'enable',
      createTime: dayjs().valueOf()
    });
    return { success: true, message: '新增成功', data: res.insertedId };
  } catch (e) {
    return { success: false, message: '新增失败' };
  }
};

// 更新信息来源
exports.updateInfoSource = async (content) => {
  const { corpId, params, id } = content;
  if (!corpId) return { success: false, message: '机构id不能为空' };
  try {
    await db.collection('info-source-list').updateOne({ _id: id }, { $set: params });
    return { success: true, message: '更新成功' };
  } catch (error) {
    return { success: false, message: '更新失败' };
  }
};

// 删除信息来源
exports.deleteInfoSource = async (content) => {
  const { corpId, id } = content;
  if (!corpId) return { success: false, message: '机构id不能为空' };
  if (!id) return { success: false, message: '信息来源id不能为空' };
  try {
    await db.collection('info-source-list').deleteOne({ corpId, _id: id });
    return { success: true, message: '删除成功' };
  } catch (e) {
    return { success: false, message: '删除失败' };
  }
};

// 获取信息来源列表 分页
exports.getInfoSourceList = async (content) => {
  const { corpId, cateIds, name, page, pageSize, status } = content;
  if (!corpId) return { success: false, message: '机构id不能为空' };
  let query = { corpId };
  if (typeof name === 'string' && name.trim()) {
    const str = name.trim().replace(/[.*+?^=!:${}()|\[\]\/\\]/g, '\\$&');
    query.name = new RegExp(str, 'i');
  }
  if (Array.isArray(cateIds)) query.cateIdGroup = { $in: cateIds };
  if (status) query.status = status;
  try {
    const total = await db.collection('info-source-list').find(query).count();
    const pages = Math.ceil(total / pageSize);
    const data = await db.collection('info-source-list').aggregate([
      { $match: query },
      { $sort: { createTime: -1 } },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize },
      {
        $lookup: {
          from: 'info-source-cate',
          localField: 'cateIdGroup',
          foreignField: '_id',
          as: 'cates'
        }
      },
      {
        $project: {
          _id: 1,
          corpId: 1,
          name: 1,
          description: 1,
          status: 1,
          cateIdGroup: 1,
          createTime: 1,
          cates: {
            _id: 1,
            label: 1,
            level: 1,
            parentId: 1
          }
        }
      }
    ]).toArray();
    return { success: true, message: '获取成功', list: data, total, pages };
  } catch (e) {
    return { success: false, message: e.message || '获取失败' };
  }
};

const envFile = `.env.${process.env.NODE_ENV || "development"}`;
require("dotenv").config({ path: envFile });

const express = require("express");
const cors = require("cors");
const timeout = require("connect-timeout");
const mongodb = require("./mongodb");
const upload = require("./upload");
const bodyParser = require("body-parser");
const corsUtils = require("./utils/cors");
const logger = require("./utils/logger");
const member = require("./member");
const todo = require("./toDoEvents");
const groupmsg = require("./groupmsg");
const corp = require("./corp");
const knowledgeBase = require("./knowledgeBase");
const survery = require("./survery");
const weCom = require("./weCom");
const system = require("./system");
const dataConversion = require("./dataConversion");
const sessionArchive = require("./sessionArchive");
const customerHisSync = require("./customerHisSync");
const hlw = require("./hlw");
const trigger = require("./trigger");
const consultTrigger = require("./hlw/consult-order/trigger");
const callBack = require("./callBack");
const alipayApi = require("./alipayApi");
const gfyWechat = require("./gfy-wechat");
const platform = require("./platform");
const path = require("path");
const jwt = require("./utils/jwt");
const yizhipai = require("./yizhipai");
const app = express();
const PORT = process.env.CONFIG_NODE_PORT || 8080;
const { getDatabase, connectToMongoDB } = require("./mongodb");
const authMiddleware = require("./middleware/auth");
// 设置CORS中间件，允许所有来源的请求
app.use(cors(corsUtils.cors));
app.use(timeout("180s")); // 设置超时时间为30秒
// 设置请求头中间件，允许自定义请求头（如果需要的话）

app.use((req, res, next) => {
  res.header(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, X-Custom-Header"
  );
  next();
});

// 解析JSON格式的请求体
app.use(bodyParser.json());
// 解析urlencoded格式的请求体
app.use(bodyParser.urlencoded({ extended: true }));
app.use(authMiddleware);
app.use(bodyParser.text({ type: "*/xml" }));
// GET请求处理器
app.get("/", async (req, res) => {
  res.json({ message: "GET request received" });
});

// 连接数据库
connectToMongoDB();

app.get("/zyt/stats", async (req, res) => {
  try {
    const db = await getDatabase("Internet-hospital");
    const item = await hlw({ type: "hlwStats", statsType: req.query.type }, db);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err: err.message,
    });
  }
});

app.post("/getYoucanData/refreshToken", async (req, res) => {
  const requestData = req.body;
  const refreshToken =
    typeof requestData.refreshToken === "string"
      ? requestData.refreshToken
      : "";
  const token = typeof requestData.token === "string" ? requestData.token : "";
  if (refreshToken && token) {
    try {
      const tokenData = jwt.decodeJWT(token);
      const refreshTokenData = jwt.decodeJWT(refreshToken);
      if (refreshTokenData) {
        const notExpired = refreshTokenData.exp * 1000 - Date.now() > 0; // 计算 token 是否过期
        if (!notExpired) {
          return res.status(401).json({ error: "刷新令牌已过期" });
        }
      }
      if (
        tokenData.userId &&
        tokenData.corpId &&
        tokenData.userId === refreshTokenData.userId &&
        tokenData.corpId === refreshTokenData.corpId
      ) {
        const newToken = jwt.encodeJWT({
          userId: tokenData.userId,
          corpId: tokenData.corpId,
        });
        const newRefreshToken = jwt.encodeJWT(
          { userId: tokenData.userId, corpId: tokenData.corpId },
          "7d"
        ); // 设置新的刷新令牌有效期为7天
        return res.json({
          success: true,
          message: "刷新令牌成功",
          token: newToken,
          refreshToken: newRefreshToken,
        });
      }
    } catch (err) {}
  }
  return res.status(401).json({ error: "无效的token" });
});

// POST请求处理器
app.post("/getYoucanData/member", async (req, res) => {
  const requestData = req.body;
  logger.info(requestData);
  try {
    const db = await getDatabase("admin");
    let item = await member.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.post("/getYoucanData/todo", async (req, res) => {
  const requestData = req.body;
  try {
    const db = await getDatabase("admin");
    let item = await todo.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
    });
  }
});

app.post("/getYoucanData/platform", async (req, res) => {
  const requestData = req.body;
  try {
    const db = await getDatabase("corp");
    const item = await platform.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.post("/getYoucanData/groupmsg", async (req, res) => {
  const requestData = req.body;
  try {
    const db = await getDatabase("admin");
    let item = await groupmsg.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.post("/getYoucanData/corp", async (req, res) => {
  const requestData = req.body;
  try {
    const db = await getDatabase("corp");
    const item = await corp.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.post("/getYoucanData/knowledgeBase", async (req, res) => {
  const requestData = req.body;
  try {
    const db = await getDatabase("corp");
    const item = await knowledgeBase.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.post("/getYoucanData/survery", async (req, res) => {
  const requestData = req.body;
  try {
    const db = await getDatabase("corp");
    const item = await survery.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.post("/getYoucanData/system", async (req, res) => {
  const requestData = req.body;
  const db = await getDatabase("corp");
  const item = await system.main(requestData, db);
  logger.info(`接口请求:${JSON.stringify(requestData)}`);
  logger.info(`接口输出:${JSON.stringify(item)}`);
  res.json(item);
});

app.post("/getYoucanData/sessionArchive", async (req, res) => {
  const requestData = req.body;
  try {
    const db = await getDatabase("corp");
    const item = await sessionArchive.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.post("/getYoucanData/weCom", async (req, res) => {
  const requestData = req.body;
  try {
    const item = await weCom.main(requestData);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.post("/getYoucanData/customerHisSync", async (req, res) => {
  const requestData = req.body;
  try {
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    const item = await customerHisSync.main(requestData);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

// 添加静态HTML文件访问路径
app.use("/", express.static(path.join(__dirname, "./static")));

app.post("/getAlipayData", async (req, res) => {
  const requestData = req.body;
  try {
    const item = await alipayApi.main(requestData);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.post("/getYoucanData/hlw", async (req, res) => {
  const requestData = req.body;
  try {
    const db = await getDatabase("Internet-hospital");
    const item = await hlw(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err: err.message,
    });
  }
});

app.post("/getYoucanData/dataConversion", async (req, res) => {
  const requestData = req.body;
  try {
    const db = await getDatabase("admin");
    const item = await dataConversion.main(requestData, db);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err: err.message,
    });
  }
});

app.post("/getYoucanData/gfy-wechat", async (req, res) => {
  const requestData = req.body;
  try {
    const item = await gfyWechat.main(requestData);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.json(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err: err.message,
    });
  }
});

app.post("/IMCallBack", async (req, res) => {
  const requestData = req.body;
  try {
    const db = await getDatabase("Internet-hospital");
    await hlw(
      {
        type: "addChatMsg",
        params: requestData,
      },
      db
    );
    res.json({
      success: true,
      message: "请求成功",
    });
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err: err.message,
    });
  }
});

app.get("/callback/data", async (req, res) => {
  const requestData = req.query;
  console.log(requestData);
  try {
    const item = await callBack.httpGet(requestData);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.send(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.get("/callback/command", async (req, res) => {
  const requestData = req.query;
  try {
    const item = await callBack.httpGet(requestData);
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.send(item);
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.post("/callback/command", async (req, res) => {
  const requestData = req.body;
  try {
    logger.info(`接口请求:${JSON.stringify(requestData)}`);
    const item = await callBack.httpPost(requestData);

    logger.info(`接口输出:${JSON.stringify(item)}`);
    res.send("success");
  } catch (err) {
    res.json({
      success: false,
      message: "请求失败!",
      err,
    });
  }
});

app.use("/uploads", express.static(path.join(__dirname, "../uploads")));

// 文件上传接口
upload.uploadFile(app);
yizhipai.useYizhipai(app);
// 启动服务器
app.listen(PORT, "0.0.0.0", () => {
  logger.info(`Server is running on http://localhost:${PORT}`);
});

// 定时任务
trigger.initSchedule();

// consultTrigger({
//   type: "recoverDelayedTasks",
// });

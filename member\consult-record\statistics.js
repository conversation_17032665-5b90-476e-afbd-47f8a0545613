/**
 * 咨询记录统计模块 - 性能优化说明
 *
 * 针对 getBillRecordsWithMemberDetails 查询超时问题的优化方案：
 *
 * 1. 分页优化：
 *    - 添加了分页参数支持 (page, pageSize)
 *    - 限制单次查询最大数据量
 *    - 使用 $facet 同时获取总数和数据
 *
 * 2. 聚合管道优化：
 *    - 优化 $lookup 操作，只查询需要的字段
 *    - 使用 pipeline 形式的 $lookup 提高性能
 *    - 添加 $sort 确保分页结果一致性
 *
 * 3. 高性能优化版本 (getBillRecordsWithMemberDetailsOptimized)：
 *    - 避免大型 JOIN 操作，使用分批查询策略
 *    - 先查 bill-record，再分批查 member 表
 *    - 在应用层合并数据，减少数据库压力
 *
 * 4. 推荐的数据库索引：
 *    bill-record 集合：
 *    - { corpId: 1, createTime: -1 }
 *    - { corpId: 1, counselorUserId: 1 }
 *    - { corpId: 1, introducerUserId: 1 }
 *    - { corpId: 1, consultStage: 1 }
 *    - { customerId: 1 }
 *
 *    member 集合：
 *    - { _id: 1 } (默认已存在)
 *    - { _id: 1, customerSource: 1 } (复合索引优化投影查询)
 *
 * 5. 使用建议：
 *    - 数据量 < 5万条：使用 getBillRecordsWithMemberDetails
 *    - 数据量 >= 5万条：使用 getBillRecordsWithMemberDetailsOptimized
 *    - 必须缩小日期范围或添加更多筛选条件
 *    - 建议页面大小不超过 2000 条
 */

const dayjs = require("dayjs");
const api = require("../../api");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getConsultantStatistics":
      return await getConsultantStatistics(content);
    case "getConsultantSalesRanking":
      return await getConsultantSalesRanking(content);
    case "getDoctorPerformance":
      return await getDoctorPerformance(content);
    case "getBillRecordsWithMemberDetails":
      return await getBillRecordsWithMemberDetails(content);
    case "getBillRecordsWithMemberDetailsOptimized":
      return await getBillRecordsWithMemberDetailsOptimized(content);
    case "getEConsultStatistics":
      return await getEConsultStatistics(content);
    case "getEConsultProjectStatistics":
      return await getEConsultProjectStatistics(content);
    case "getProjectStatistics":
      return await getProjectStatistics(content);
    case "getConsultantSourceStatistics": // 新增入口
      return await getConsultantSourceStatistics(content);
    case "getProjectConsultStatistics": // 新增现场咨询项目统计入口
      return await getProjectConsultStatistics(content);
    case "getConsultRecords":
      return await getConsultRecords(content);
    case "getDoctorDeductRecords":
      return await getDoctorDeductRecords(content);
  }
};

async function getConsultantStatistics(content) {
  try {
    const {
      corpId,
      startDate,
      endDate,
      consultantFilter,
      projectIds,
      infoSource,
    } = content;
    let matchStage = {
      corpId,
      counselorUserId: { $exists: true, $ne: null },
      importTag: { $exists: false },
    };

    if (Array.isArray(consultantFilter) && consultantFilter.length) {
      matchStage.counselorUserId = { $in: consultantFilter };
    }
    if (Array.isArray(projectIds) && projectIds.length) {
      matchStage["projectIds"] = { $in: projectIds };
    }
    if (Array.isArray(infoSource) && infoSource.length) {
      matchStage["source"] = { $in: infoSource };
    }
    // 添加日期筛选条件
    if (startDate || endDate) {
      matchStage.createTime = {};
      if (startDate)
        matchStage.createTime.$gte = dayjs(startDate).startOf("day").valueOf();
      if (endDate)
        matchStage.createTime.$lte = dayjs(endDate).endOf("day").valueOf();
    }
    const records = await db
      .collection("consult-record")
      .aggregate(
        [
          { $match: matchStage },
          {
            $group: {
              _id: "$counselorUserId",
              consultCount: { $sum: 1 },
              successCount: {
                $sum: { $cond: [{ $gt: ["$tradeAmount", 0] }, 1, 0] },
              },
              successAmount: {
                $sum: {
                  $cond: [{ $gt: ["$tradeAmount", 0] }, "$tradeAmount", 0],
                },
              },
              netAmount: {
                $sum: {
                  $subtract: [
                    {
                      $cond: [{ $gt: ["$tradeAmount", 0] }, "$tradeAmount", 0],
                    },
                    "$consumeCount",
                  ],
                },
              },
              firstVisitCount: {
                $sum: {
                  $cond: [{ $eq: ["$consultStage", "firstVisit"] }, 1, 0],
                },
              },
              returnVisitCount: {
                $sum: {
                  $cond: [{ $eq: ["$consultStage", "returnVisit"] }, 1, 0],
                },
              },
              moreConsumedCount: {
                $sum: {
                  $cond: [{ $eq: ["$consultStage", "moreConsumed"] }, 1, 0],
                },
              },
              // 修改各阶段交易金额统计
              firstVisitAmount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "firstVisit"] },
                      ],
                    },
                    "$tradeAmount",
                    0,
                  ],
                },
              },
              returnVisitAmount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "returnVisit"] },
                      ],
                    },
                    "$tradeAmount",
                    0,
                  ],
                },
              },
              moreConsumedAmount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "moreConsumed"] },
                      ],
                    },
                    "$tradeAmount",
                    0,
                  ],
                },
              },
              // 修改各阶段成交次数统计
              firstVisitSuccessCount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "firstVisit"] },
                      ],
                    },
                    1,
                    0,
                  ],
                },
              },
              returnVisitSuccessCount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "returnVisit"] },
                      ],
                    },
                    1,
                    0,
                  ],
                },
              },
              moreConsumedSuccessCount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "moreConsumed"] },
                      ],
                    },
                    1,
                    0,
                  ],
                },
              },
            },
          },
          {
            $project: {
              _id: 0,
              consultant: "$_id",
              consultCount: 1,
              successCount: 1,
              successAmount: 1,
              netAmount: 1,
              firstVisitCount: 1,
              returnVisitCount: 1,
              moreConsumedCount: 1,
              // 新增各阶段交易金额输出
              firstVisitAmount: 1,
              returnVisitAmount: 1,
              moreConsumedAmount: 1,
              // 新增各阶段成交次数输出
              firstVisitSuccessCount: 1,
              returnVisitSuccessCount: 1,
              moreConsumedSuccessCount: 1,
              avgOrderValue: {
                $cond: [
                  { $eq: ["$successCount", 0] },
                  0,
                  { $divide: ["$successAmount", "$successCount"] },
                ],
              },
            },
          },
          { $sort: { successAmount: -1 } },
        ],
        {
          allowDiskUse: true, // 允许使用磁盘处理大型聚合操作
          cursor: { batchSize: 1000 }, // 增加批处理大小
        }
      )
      .toArray();

    return { success: true, data: records, message: "查询成功" };
  } catch (error) {
    return { success: false, message: error.message || "查询失败" };
  }
}

async function getConsultantSalesRanking(content) {
  try {
    const {
      corpId,
      startDate,
      endDate,
      consultantFilter,
      projectIds,
      consultStages,
      projectCategories,
    } = content;
    let matchStage = {
      corpId,
      counselorUserId: { $exists: true, $ne: null },
      importTag: { $exists: false },
    };

    if (Array.isArray(consultStages) && consultStages.length) {
      matchStage["consultStage"] = { $in: consultStages };
    }
    if (Array.isArray(consultantFilter) && consultantFilter.length) {
      matchStage.counselorUserId = { $in: consultantFilter };
    }
    if (Array.isArray(projectIds) && projectIds.length) {
      matchStage["projectIds"] = { $in: projectIds };
    }
    if (Array.isArray(projectCategories) && projectCategories.length) {
    }
    // 添加日期筛选条件
    if (startDate || endDate) {
      matchStage.createTime = {};
      if (startDate)
        matchStage.createTime.$gte = dayjs(startDate).startOf("day").valueOf();
      if (endDate)
        matchStage.createTime.$lte = dayjs(endDate).endOf("day").valueOf();
    }
    const records = await db
      .collection("consult-record")
      .aggregate(
        [
          { $match: matchStage },
          {
            $group: {
              _id: "$counselorUserId",
              consultCount: { $sum: 1 },
              successCount: {
                $sum: { $cond: [{ $gt: ["$tradeAmount", 0] }, 1, 0] },
              },
              successAmount: {
                $sum: {
                  $cond: [{ $gt: ["$tradeAmount", 0] }, "$tradeAmount", 0],
                },
              },
              netAmount: {
                $sum: {
                  $cond: [{ $gt: ["$tradeAmount", 0] }, "$tradeAmount", 0],
                },
              },
            },
          },
          {
            $project: {
              _id: 0,
              consultant: "$_id",
              consultCount: 1,
              successCount: 1,
              successAmount: 1,
              netAmount: 1,
              successRate: {
                $cond: [
                  { $eq: ["$consultCount", 0] },
                  0,
                  { $divide: ["$successCount", "$consultCount"] },
                ],
              },
              avgOrderValue: {
                $cond: [
                  { $eq: ["$successCount", 0] },
                  0,
                  { $divide: ["$successAmount", "$successCount"] },
                ],
              },
            },
          },
          { $sort: { successAmount: -1 } },
        ],
        {
          allowDiskUse: true,
          cursor: { batchSize: 1000 },
        }
      )
      .toArray();

    return { success: true, data: records, message: "查询成功" };
  } catch (error) {
    return { success: false, message: error.message || "查询失败" };
  }
}
async function getDoctorPerformance(content) {
  try {
    const { corpId, startDate, endDate, projectIds, receptionDoctorUserId } =
      content;
    let matchStage = {
      corpId,
      receptionDoctorUserId: { $exists: true, $ne: null },
      importTag: { $exists: false },
    };

    if (Array.isArray(receptionDoctorUserId) && receptionDoctorUserId.length) {
      matchStage.receptionDoctorUserId = { $in: receptionDoctorUserId };
    }
    if (Array.isArray(projectIds) && projectIds.length) {
      matchStage["projectIds"] = { $in: projectIds };
    }
    // 添加日期筛选条件
    if (startDate || endDate) {
      matchStage.createTime = {};
      if (startDate) {
        matchStage.createTime.$gte = dayjs(startDate).startOf("day").valueOf();
      }
      if (endDate) {
        matchStage.createTime.$lte = dayjs(endDate).endOf("day").valueOf();
      }
    }
    const records = await db
      .collection("consult-record")
      .aggregate(
        [
          { $match: matchStage },
          {
            $group: {
              _id: "$receptionDoctorUserId", // 按照接诊医生ID分组
              consultCount: { $sum: 1 },
              successCount: {
                $sum: { $cond: [{ $gt: ["$tradeAmount", 0] }, 1, 0] },
              },
              successAmount: {
                $sum: {
                  $cond: [{ $gt: ["$tradeAmount", 0] }, "$tradeAmount", 0],
                },
              },
              consumeCount: { $sum: "$consumeCount" }, // 使用consumeCount字段作为消费次数
              netAmount: {
                $sum: {
                  $subtract: [
                    {
                      $cond: [{ $gt: ["$tradeAmount", 0] }, "$tradeAmount", 0],
                    },
                    "$consumeCount", // 从交易金额中减去消费次数
                  ],
                },
              },
              firstVisitCount: {
                $sum: {
                  $cond: [{ $eq: ["$consultStage", "firstVisit"] }, 1, 0],
                },
              },
              returnVisitCount: {
                $sum: {
                  $cond: [{ $eq: ["$consultStage", "returnVisit"] }, 1, 0],
                },
              },
              moreConsumedCount: {
                $sum: {
                  $cond: [{ $eq: ["$consultStage", "moreConsumed"] }, 1, 0],
                },
              },
              // 修改各阶段交易金额统计
              firstVisitAmount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "firstVisit"] },
                      ],
                    },
                    "$tradeAmount",
                    0,
                  ],
                },
              },
              returnVisitAmount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "returnVisit"] },
                      ],
                    },
                    "$tradeAmount",
                    0,
                  ],
                },
              },
              moreConsumedAmount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "moreConsumed"] },
                      ],
                    },
                    "$tradeAmount",
                    0,
                  ],
                },
              },
              // 修改各阶段成交次数统计
              firstVisitSuccessCount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "firstVisit"] },
                      ],
                    },
                    1,
                    0,
                  ],
                },
              },
              returnVisitSuccessCount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "returnVisit"] },
                      ],
                    },
                    1,
                    0,
                  ],
                },
              },
              moreConsumedSuccessCount: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ["$tradeAmount", 0] },
                        { $eq: ["$consultStage", "moreConsumed"] },
                      ],
                    },
                    1,
                    0,
                  ],
                },
              },
            },
          },
          {
            $project: {
              _id: 0,
              receptionDoctorUserId: "$_id",
              consultCount: 1,
              successCount: 1,
              successAmount: 1,
              consumeCount: 1, // 输出消费次数而非退款金额
              netAmount: 1,
              firstVisitCount: 1,
              returnVisitCount: 1,
              moreConsumedCount: 1,
              // 新增各阶段交易金额输出
              firstVisitAmount: 1,
              returnVisitAmount: 1,
              moreConsumedAmount: 1,
              // 新增各阶段成交次数输出
              firstVisitSuccessCount: 1,
              returnVisitSuccessCount: 1,
              moreConsumedSuccessCount: 1,
            },
          },
          { $sort: { successAmount: -1 } },
        ],
        {
          allowDiskUse: true,
          cursor: { batchSize: 1000 },
        }
      )
      .toArray();

    return { success: true, data: records, message: "查询成功" };
  } catch (error) {
    return { success: false, message: error.message || "查询失败" };
  }
}

async function getBillRecordsWithMemberDetails(content) {
  try {
    const {
      payDate,
      corpId,
      consultantFilter,
      developerFilter,
      consultStages,
    } = content;

    // 构建bill-record表的匹配条件
    const billMatch = {
      corpId,
      importTag: { $exists: false },
    };

    // 设置日期范围
    if (payDate && Array.isArray(payDate) && payDate.length === 2) {
      billMatch.createTime = {
        $gte: dayjs(payDate[0]).startOf("day").valueOf(),
        $lte: dayjs(payDate[1]).endOf("day").valueOf(),
      };
    }

    if (Array.isArray(consultStages) && consultStages.length) {
      billMatch["consultStage"] = { $in: consultStages };
    }

    if (
      consultantFilter &&
      Array.isArray(consultantFilter) &&
      consultantFilter.length
    ) {
      billMatch.counselorUserId = { $in: consultantFilter };
    }

    if (
      developerFilter &&
      Array.isArray(developerFilter) &&
      developerFilter.length
    ) {
      billMatch.introducerUserId = { $in: developerFilter };
    }

    // 简化的聚合管道 - 查询所有数据
    const pipeline = [
      { $match: billMatch },
      // 添加排序
      { $sort: { createTime: -1, _id: 1 } },
      // 优化的lookup - 只获取需要的字段
      {
        $lookup: {
          from: "member",
          let: { customerId: "$customerId" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$_id", "$$customerId"] },
              },
            },
            // 只投影需要的字段，减少数据传输
            {
              $project: {
                _id: 1,
                customerSource: 1,
                inHospitalTimes: 1,
              },
            },
          ],
          as: "memberInfo",
        },
      },
      // 处理关联结果
      {
        $addFields: {
          customerSource: {
            $let: {
              vars: {
                memberData: { $arrayElemAt: ["$memberInfo", 0] },
              },
              in: "$$memberData.customerSource",
            },
          },
          inHospitalTimes: {
            $let: {
              vars: {
                memberData: { $arrayElemAt: ["$memberInfo", 0] },
              },
              in: "$$memberData.inHospitalTimes",
            },
          },
        },
      },
      // 移除不需要的字段
      {
        $project: {
          memberInfo: 0,
        },
      },
    ];

    console.log(
      `[getBillRecordsWithMemberDetails] 开始查询所有数据: corpId=${corpId}`
    );
    const startTime = Date.now();

    const records = await db
      .collection("bill-record")
      .aggregate(pipeline, {
        allowDiskUse: true,
        maxTimeMS: 120000, // 增加超时时间到2分钟
        cursor: { batchSize: 1000 },
      })
      .toArray();

    const endTime = Date.now();
    console.log(
      `[getBillRecordsWithMemberDetails] 查询完成: 耗时${endTime - startTime}ms，返回${records.length}条记录`
    );

    return {
      success: true,
      data: records,
      total: records.length,
      message: `查询成功，返回${records.length}条记录`,
    };
  } catch (error) {
    console.error(`[getBillRecordsWithMemberDetails] 查询失败:`, error);

    // 根据错误类型返回不同的错误信息
    if (error.name === "MongoServerError" && error.code === 16992) {
      return {
        success: false,
        message: "查询超时，请缩小日期范围或添加更多筛选条件",
      };
    }

    return {
      success: false,
      message: error.message || "查询失败，请检查参数并稍后重试",
    };
  }
}

// 高性能版本：分批查询，避免大型JOIN操作
async function getBillRecordsWithMemberDetailsOptimized(content) {
  try {
    const {
      payDate,
      corpId,
      consultantFilter,
      developerFilter,
      consultStages,
      page = 1,
      pageSize = 1000,
      limit = 10000,
      useCache = false, // 是否使用缓存
      batchSize = 500, // 分批大小
    } = content;

    // 输入验证
    const currentPage = Math.max(1, parseInt(page));
    const currentPageSize = Math.min(Math.max(10, parseInt(pageSize)), 2000); // 降低最大页面大小
    const maxLimit = Math.min(parseInt(limit) || 10000, 20000); // 降低最大限制
    const currentBatchSize = Math.min(parseInt(batchSize) || 500, 1000);

    // 构建匹配条件
    const billMatch = {
      corpId,
      importTag: { $exists: false },
    };

    if (payDate && Array.isArray(payDate) && payDate.length === 2) {
      billMatch.createTime = {
        $gte: dayjs(payDate[0]).startOf("day").valueOf(),
        $lte: dayjs(payDate[1]).endOf("day").valueOf(),
      };
    }

    if (Array.isArray(consultStages) && consultStages.length) {
      billMatch["consultStage"] = { $in: consultStages };
    }

    if (
      consultantFilter &&
      Array.isArray(consultantFilter) &&
      consultantFilter.length
    ) {
      billMatch.counselorUserId = { $in: consultantFilter };
    }

    if (
      developerFilter &&
      Array.isArray(developerFilter) &&
      developerFilter.length
    ) {
      billMatch.introducerUserId = { $in: developerFilter };
    }

    console.log(
      `[getBillRecordsOptimized] 开始优化查询: corpId=${corpId}, page=${currentPage}, pageSize=${currentPageSize}`
    );
    const startTime = Date.now();

    // 策略1: 先获取总数（不做JOIN）
    const totalCount = await db
      .collection("bill-record")
      .countDocuments(billMatch, {
        maxTimeMS: 10000, // 10秒超时
      });

    if (totalCount === 0) {
      return {
        success: true,
        data: [],
        pagination: {
          page: currentPage,
          pageSize: currentPageSize,
          total: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false,
        },
        message: "未找到符合条件的记录",
      };
    }

    // 策略2: 分页获取bill-record数据（不做JOIN）
    const skip = (currentPage - 1) * currentPageSize;
    const billRecords = await db
      .collection("bill-record")
      .find(billMatch, {
        maxTimeMS: 15000, // 15秒超时
        batchSize: Math.min(currentPageSize, 1000),
      })
      .sort({ createTime: -1, _id: 1 })
      .skip(skip)
      .limit(Math.min(currentPageSize, maxLimit - skip))
      .toArray();

    if (billRecords.length === 0) {
      return {
        success: true,
        data: [],
        pagination: {
          page: currentPage,
          pageSize: currentPageSize,
          total: totalCount,
          totalPages: Math.ceil(totalCount / currentPageSize),
          hasNextPage: false,
          hasPrevPage: currentPage > 1,
        },
        message: "该页面无数据",
      };
    }

    // 策略3: 批量获取客户来源信息
    const customerIds = [
      ...new Set(
        billRecords.map((record) => record.customerId).filter(Boolean)
      ),
    ];

    const customerSourceMap = new Map();

    // 分批查询member表，避免一次查询过多数据
    for (let i = 0; i < customerIds.length; i += currentBatchSize) {
      const batchIds = customerIds.slice(i, i + currentBatchSize);

      const memberData = await db
        .collection("member")
        .find(
          { _id: { $in: batchIds } },
          {
            projection: { _id: 1, customerSource: 1, inHospitalTimes: 1 },
            maxTimeMS: 10000, // 10秒超时
          }
        )
        .toArray();

      // 构建映射表
      memberData.forEach((member) => {
        customerSourceMap.set(member._id?.toString(), {
          customerSource: member.customerSource,
          inHospitalTimes: member.inHospitalTimes,
        });
      });
    }

    // 策略4: 合并数据
    const enrichedRecords = billRecords.map((record) => {
      const memberData = customerSourceMap.get(record.customerId?.toString());
      return {
        ...record,
        customerSource: memberData?.customerSource || null,
        inHospitalTimes: memberData?.inHospitalTimes || null,
      };
    });

    const endTime = Date.now();
    console.log(
      `[getBillRecordsOptimized] 查询完成: 耗时${endTime - startTime}ms, 处理${
        billRecords.length
      }条记录`
    );

    // 计算分页信息
    const totalPages = Math.ceil(totalCount / currentPageSize);
    const hasNextPage = currentPage < totalPages;
    const hasPrevPage = currentPage > 1;

    return {
      success: true,
      data: enrichedRecords,
      pagination: {
        page: currentPage,
        pageSize: currentPageSize,
        total: totalCount,
        totalPages,
        hasNextPage,
        hasPrevPage,
      },
      performance: {
        queryTime: endTime - startTime,
        recordsProcessed: billRecords.length,
        customersLooked: customerIds.length,
      },
      message: `查询成功，返回${enrichedRecords.length}条记录，总计${totalCount}条`,
    };
  } catch (error) {
    console.error(`[getBillRecordsOptimized] 查询失败:`, error);

    if (error.name === "MongoServerError") {
      if (error.code === 16992) {
        return {
          success: false,
          message: "查询超时，请缩小日期范围或减少页面大小",
        };
      }
      if (error.code === 17261) {
        return {
          success: false,
          message: "内存不足，请减少查询范围或使用更小的页面大小",
        };
      }
    }

    return {
      success: false,
      message: error.message || "查询失败，请检查参数并稍后重试",
    };
  }
}

// 添加 getEConsultStatistics 函数的实现
async function getEConsultStatistics(content) {
  try {
    const { corpId, startDate, endDate } = content;

    let matchStage = {
      corpId,
      importTag: { $exists: false },
    };

    if (startDate || endDate) {
      matchStage.createTime = {};
      if (startDate) {
        matchStage.createTime.$gte = dayjs(startDate).startOf("day").valueOf();
      }
      if (endDate) {
        matchStage.createTime.$lte = dayjs(endDate).endOf("day").valueOf();
      }
    }

    const records = await db
      .collection("e-consult-record")
      .aggregate(
        [
          { $match: matchStage },
          {
            $group: {
              _id: null,
              totalCount: { $sum: 1 },
              totalAmount: { $sum: "$tradeAmount" },
            },
          },
        ],
        {
          allowDiskUse: true,
          cursor: { batchSize: 1000 },
        }
      )
      .toArray();

    return {
      success: true,
      data: records.length > 0 ? records[0] : { totalCount: 0, totalAmount: 0 },
      message: "查询成功",
    };
  } catch (error) {
    return { success: false, message: error.message || "查询失败" };
  }
}

// 新函数：网店咨询项目统计，查询e-consult-record并关联member表
async function getEConsultProjectStatistics(content) {
  try {
    const {
      corpId,
      startDate,
      endDate,
      projectIds,
      infoSource,
      developerUserIds,
    } = content;
    // 构建e-consult-record表的匹配条件
    let matchStage = {
      corpId,
      importTag: { $exists: false },
    };

    // 添加日期筛选条件
    if (startDate || endDate) {
      matchStage.createTime = {};
      if (startDate) {
        matchStage.createTime.$gte = dayjs(startDate).startOf("day").valueOf();
      }
      if (endDate) {
        matchStage.createTime.$lte = dayjs(endDate).endOf("day").valueOf();
      }
    }

    // 添加项目筛选条件
    if (Array.isArray(projectIds) && projectIds.length) {
      matchStage["projectIds"] = { $in: projectIds };
    }

    if (Array.isArray(infoSource) && infoSource.length) {
      matchStage["source"] = { $in: infoSource };
    }

    if (Array.isArray(developerUserIds) && developerUserIds.length) {
      matchStage["userId"] = { $in: developerUserIds };
    }

    // 使用聚合管道查询，并与member表关联
    const records = await db
      .collection("e-consult-record")
      .aggregate(
        [
          { $match: matchStage },
          {
            $lookup: {
              from: "member",
              let: { customerId: "$customerId" },
              pipeline: [
                {
                  $match: {
                    $expr: { $eq: ["$_id", "$$customerId"] },
                  },
                },
                {
                  $project: {
                    _id: 1,
                    counselorRecord: 1,
                    inHospitalTimes: 1,
                  },
                },
              ],
              as: "memberInfo",
            },
          },
          {
            $lookup: {
              from: "consume-record",
              localField: "customerId",
              foreignField: "customerId",
              as: "consumeRecord",
            },
          },
          {
            $addFields: {
              counselorRecord: {
                $arrayElemAt: ["$memberInfo.counselorRecord", 0],
              },
              inHospitalTimes: {
                $arrayElemAt: ["$memberInfo.inHospitalTimes", 0],
              },
              consumeRecordCount: { $size: "$consumeRecord" },
            },
          },
          {
            $project: {
              memberInfo: 0,
              consumeRecord: 0, // 只保留数量，移除详细记录
            },
          },
        ],
        {
          allowDiskUse: true,
          cursor: { batchSize: 1000 },
        }
      )
      .toArray();

    return { success: true, data: records, message: "查询成功" };
  } catch (error) {
    return { success: false, message: error.message || "查询失败" };
  }
}

// 新增按项目维度统计的函数
async function getProjectStatistics(content) {
  try {
    const { corpId, startDate, endDate, consultantFilter, projectIds } =
      content;
    let matchStage = {
      corpId,
      projectIds: { $exists: true, $ne: [] }, // 确保存在项目ID数组且不为空
      importTag: { $exists: false },
    };

    if (Array.isArray(consultantFilter) && consultantFilter.length) {
      matchStage.counselorUserId = { $in: consultantFilter };
    }

    // 添加日期筛选条件
    if (startDate || endDate) {
      matchStage.createTime = {};
      if (startDate)
        matchStage.createTime.$gte = dayjs(startDate).startOf("day").valueOf();
      if (endDate)
        matchStage.createTime.$lte = dayjs(endDate).endOf("day").valueOf();
    }

    // 创建pipeline数组
    let pipeline = [
      { $match: matchStage },
      // 拆分项目ID数组
      { $unwind: "$projectIds" },
    ];

    // 在unwind后添加projectIds筛选，这样可以正确筛选拆分后的projectIds
    if (Array.isArray(projectIds) && projectIds.length) {
      pipeline.push({
        $match: { projectIds: { $in: projectIds } },
      });
    }

    // 添加剩余的聚合步骤
    pipeline = pipeline.concat([
      // 按项目ID分组统计
      {
        $group: {
          _id: "$projectIds",
          consultCount: { $sum: 1 },
          successCount: {
            $sum: { $cond: [{ $gt: ["$tradeAmount", 0] }, 1, 0] },
          },
          successAmount: {
            $sum: {
              $cond: [{ $gt: ["$tradeAmount", 0] }, "$tradeAmount", 0],
            },
          },
          netAmount: {
            $sum: {
              $subtract: [
                {
                  $cond: [{ $gt: ["$tradeAmount", 0] }, "$tradeAmount", 0],
                },
                "$consumeCount",
              ],
            },
          },
          firstVisitCount: {
            $sum: {
              $cond: [{ $eq: ["$consultStage", "firstVisit"] }, 1, 0],
            },
          },
          returnVisitCount: {
            $sum: {
              $cond: [{ $eq: ["$consultStage", "returnVisit"] }, 1, 0],
            },
          },
          moreConsumedCount: {
            $sum: {
              $cond: [{ $eq: ["$consultStage", "moreConsumed"] }, 1, 0],
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          projectId: "$_id",
          consultCount: 1,
          successCount: 1,
          successAmount: 1,
          netAmount: 1,
          firstVisitCount: 1,
          returnVisitCount: 1,
          moreConsumedCount: 1,
          successRate: {
            $cond: [
              { $eq: ["$consultCount", 0] },
              0,
              { $divide: ["$successCount", "$consultCount"] },
            ],
          },
          avgOrderValue: {
            $cond: [
              { $eq: ["$successCount", 0] },
              0,
              { $divide: ["$successAmount", "$successCount"] },
            ],
          },
        },
      },
      { $sort: { successAmount: -1 } },
    ]);

    const records = await db
      .collection("consult-record")
      .aggregate(pipeline, {
        allowDiskUse: true,
        cursor: { batchSize: 1000 },
      })
      .toArray();

    return { success: true, data: records, message: "查询成功" };
  } catch (error) {
    return { success: false, message: error.message || "查询失败" };
  }
}

async function getConsultantSourceStatistics(content) {
  try {
    const {
      corpId,
      startDate,
      endDate,
      consultantFilter,
      projectIds,
      infoSource,
      customerSource,
      developerUserIds,
      consultStages,
      visitStatus,
      tradeStatus,
    } = content;
    let matchStage = {
      corpId,
      source: { $exists: true }, // 确保存在来源字段
      importTag: { $exists: false },
    };

    if (Array.isArray(consultantFilter) && consultantFilter.length) {
      matchStage.counselorUserId = { $in: consultantFilter };
    }

    if (Array.isArray(projectIds) && projectIds.length) {
      matchStage["projectIds"] = { $in: projectIds };
    }

    if (Array.isArray(infoSource) && infoSource.length) {
      matchStage["source"] = { $in: infoSource };
    }
    if (Array.isArray(developerUserIds) && developerUserIds.length) {
      matchStage["introducerUserId"] = { $in: developerUserIds };
    }
    if (Array.isArray(customerSource) && customerSource.length) {
      matchStage["customerSource"] = { $in: customerSource };
    }
    if (Array.isArray(consultStages) && consultStages.length) {
      matchStage["consultStage"] = { $in: consultStages };
    }
    if (Array.isArray(visitStatus) && visitStatus.length) {
      matchStage["visitStatus"] = { $in: visitStatus };
    }
    if (tradeStatus === "traded") matchStage.tradeAmount = { $ne: 0 };
    if (tradeStatus === "untraded") matchStage.tradeAmount = { $eq: 0 };

    if (startDate || endDate) {
      matchStage.createTime = {};
      if (startDate)
        matchStage.createTime.$gte = dayjs(startDate).startOf("day").valueOf();
      if (endDate)
        matchStage.createTime.$lte = dayjs(endDate).endOf("day").valueOf();
    }

    // 聚合流水线 - 修改为按来源字段分组
    const pipeline = [
      { $match: matchStage },
      // 按来源字段分组统计
      {
        $group: {
          _id: "$source", // 以来源为分组键
          // 来院人次
          visitCount: { $sum: 1 },
          // 成交人次
          successCount: {
            $sum: { $cond: [{ $gt: ["$tradeAmount", 0] }, 1, 0] },
          },
          // 未成交人次
          failCount: {
            $sum: { $cond: [{ $gt: ["$tradeAmount", 0] }, 0, 1] },
          },
          // 消费总额
          totalAmount: {
            $sum: { $cond: [{ $gt: ["$tradeAmount", 0] }, "$tradeAmount", 0] },
          },
        },
      },
      // 计算合计
      {
        $group: {
          _id: null,
          totalVisitCount: { $sum: "$visitCount" },
          totalSuccessCount: { $sum: "$successCount" },
          totalFailCount: { $sum: "$failCount" },
          totalAmount: { $sum: "$totalAmount" },
          sources: {
            // 改为sources数组
            $push: {
              source: "$_id", // 改为source字段
              visitCount: "$visitCount",
              successCount: "$successCount",
              failCount: "$failCount",
              totalAmount: "$totalAmount",
            },
          },
        },
      },
      // 创建最终结果
      {
        $project: {
          _id: 0,
          totalVisitCount: 1,
          totalSuccessCount: 1,
          totalFailCount: 1,
          totalAmount: 1,
          sources: {
            // 改为sources
            $map: {
              input: "$sources",
              as: "source",
              in: {
                source: "$$source.source", // 改为source字段
                visitCount: "$$source.visitCount",
                successCount: "$$source.successCount",
                failCount: "$$source.failCount",
                totalAmount: "$$source.totalAmount",
                visitRate: {
                  $cond: [
                    { $eq: ["$totalVisitCount", 0] },
                    0,
                    { $divide: ["$$source.visitCount", "$totalVisitCount"] },
                  ],
                },
                successRate: {
                  $cond: [
                    { $eq: ["$totalVisitCount", 0] },
                    0,
                    { $divide: ["$$source.successCount", "$totalVisitCount"] },
                  ],
                },
                failRate: {
                  $cond: [
                    { $eq: ["$totalVisitCount", 0] },
                    0,
                    { $divide: ["$$source.failCount", "$totalVisitCount"] },
                  ],
                },
              },
            },
          },
        },
      },
    ];
    const result = await db
      .collection("consult-record")
      .aggregate(pipeline, {
        allowDiskUse: true,
        cursor: { batchSize: 1000 },
      })
      .toArray();

    return {
      success: true,
      data:
        result.length > 0
          ? result[0]
          : {
              totalVisitCount: 0,
              totalSuccessCount: 0,
              totalFailCount: 0,
              totalAmount: 0,
              sources: [], // 改为sources空数组
            },
      message: "查询成功",
    };
  } catch (error) {
    return { success: false, message: error.message || "查询失败" };
  }
}

// 新增现场咨询项目情况统计函数
async function getProjectConsultStatistics(content) {
  try {
    const {
      corpId,
      startDate,
      endDate,
      consultantFilter,
      projectIds,
      developerUserIds,
      consultStages,
      visitStatus,
      tradeStatus,
      infoSource,
    } = content;
    let matchStage = {
      corpId,
      projectIds: { $exists: true, $ne: [] }, // 确保存在项目ID数组且不为空
      importTag: { $exists: false },
    };

    if (Array.isArray(consultantFilter) && consultantFilter.length) {
      matchStage.counselorUserId = { $in: consultantFilter };
    }

    // 初始筛选不包含projectIds的筛选，将在unwind后筛选
    if (Array.isArray(developerUserIds) && developerUserIds.length) {
      matchStage["introducerUserId"] = { $in: developerUserIds };
    }
    if (Array.isArray(consultStages) && consultStages.length) {
      matchStage["consultStage"] = { $in: consultStages };
    }
    if (Array.isArray(visitStatus) && visitStatus.length) {
      matchStage["visitStatus"] = { $in: visitStatus };
    }
    if (Array.isArray(infoSource) && infoSource.length) {
      matchStage["source"] = { $in: infoSource };
    }
    if (tradeStatus === "traded") matchStage.tradeAmount = { $ne: 0 };
    if (tradeStatus === "untraded") matchStage.tradeAmount = { $eq: 0 };
    // 添加日期筛选条件
    if (startDate || endDate) {
      matchStage.createTime = {};
      if (startDate)
        matchStage.createTime.$gte = dayjs(startDate).startOf("day").valueOf();
      if (endDate)
        matchStage.createTime.$lte = dayjs(endDate).endOf("day").valueOf();
    }

    // 构建聚合管道
    let pipeline = [
      { $match: matchStage },
      // 拆分项目ID数组
      { $unwind: "$projectIds" },
    ];

    // 在unwind后添加projectIds筛选
    if (Array.isArray(projectIds) && projectIds.length) {
      pipeline.push({
        $match: { projectIds: { $in: projectIds } },
      });
    }

    pipeline = pipeline.concat([
      // 按项目ID和咨询师ID分组统计
      {
        $group: {
          _id: {
            projectId: "$projectIds",
            counselorUserId: "$counselorUserId",
          },
          consultCount: { $sum: 1 }, // 咨询人次
          // 初诊人次
          firstVisitCount: {
            $sum: {
              $cond: [{ $eq: ["$consultStage", "firstVisit"] }, 1, 0],
            },
          },
          // 复诊人次
          returnVisitCount: {
            $sum: {
              $cond: [{ $eq: ["$consultStage", "returnVisit"] }, 1, 0],
            },
          },
          // 再消费人次
          moreConsumedCount: {
            $sum: {
              $cond: [{ $eq: ["$consultStage", "moreConsumed"] }, 1, 0],
            },
          },
          // 成交人次
          successCount: {
            $sum: { $cond: [{ $gt: ["$tradeAmount", 0] }, 1, 0] },
          },
          // 未成交人次
          failCount: {
            $sum: { $cond: [{ $gt: ["$tradeAmount", 0] }, 0, 1] },
          },
          // 成交金额
          successAmount: {
            $sum: {
              $cond: [{ $gt: ["$tradeAmount", 0] }, "$tradeAmount", 0],
            },
          },
          // 各阶段成交人次
          firstVisitSuccessCount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gt: ["$tradeAmount", 0] },
                    { $eq: ["$consultStage", "firstVisit"] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          returnVisitSuccessCount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gt: ["$tradeAmount", 0] },
                    { $eq: ["$consultStage", "returnVisit"] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          moreConsumedSuccessCount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gt: ["$tradeAmount", 0] },
                    { $eq: ["$consultStage", "moreConsumed"] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          // 各阶段成交金额
          firstVisitAmount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gt: ["$tradeAmount", 0] },
                    { $eq: ["$consultStage", "firstVisit"] },
                  ],
                },
                "$tradeAmount",
                0,
              ],
            },
          },
          returnVisitAmount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gt: ["$tradeAmount", 0] },
                    { $eq: ["$consultStage", "returnVisit"] },
                  ],
                },
                "$tradeAmount",
                0,
              ],
            },
          },
          moreConsumedAmount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gt: ["$tradeAmount", 0] },
                    { $eq: ["$consultStage", "moreConsumed"] },
                  ],
                },
                "$tradeAmount",
                0,
              ],
            },
          },
        },
      },
      // 计算率和比例
      {
        $project: {
          _id: 0,
          projectId: "$_id.projectId",
          counselorUserId: "$_id.counselorUserId",
          consultCount: 1,
          // 初诊人次及占比
          firstVisitCount: 1,
          firstVisitRate: {
            $cond: [
              { $eq: ["$consultCount", 0] },
              0,
              { $divide: ["$firstVisitCount", "$consultCount"] },
            ],
          },
          // 复诊人次及占比
          returnVisitCount: 1,
          returnVisitRate: {
            $cond: [
              { $eq: ["$consultCount", 0] },
              0,
              { $divide: ["$returnVisitCount", "$consultCount"] },
            ],
          },
          // 再消费人次及占比
          moreConsumedCount: 1,
          moreConsumedRate: {
            $cond: [
              { $eq: ["$consultCount", 0] },
              0,
              { $divide: ["$moreConsumedCount", "$consultCount"] },
            ],
          },
          // 成交相关数据
          successCount: 1,
          failCount: 1,
          successRate: {
            $cond: [
              { $eq: ["$consultCount", 0] },
              0,
              { $divide: ["$successCount", "$consultCount"] },
            ],
          },
          successAmount: 1,
          // 客单价
          avgOrderValue: {
            $cond: [
              { $eq: ["$successCount", 0] },
              0,
              { $divide: ["$successAmount", "$successCount"] },
            ],
          },
          // 各阶段成交数据
          firstVisitSuccessCount: 1,
          firstVisitAmount: 1,
          firstVisitSuccessRate: {
            $cond: [
              { $eq: ["$firstVisitCount", 0] },
              0,
              { $divide: ["$firstVisitSuccessCount", "$firstVisitCount"] },
            ],
          },
          returnVisitSuccessCount: 1,
          returnVisitAmount: 1,
          returnVisitSuccessRate: {
            $cond: [
              { $eq: ["$returnVisitCount", 0] },
              0,
              {
                $divide: ["$returnVisitSuccessCount", "$returnVisitCount"],
              },
            ],
          },
          moreConsumedSuccessCount: 1,
          moreConsumedAmount: 1,
          moreConsumedSuccessRate: {
            $cond: [
              { $eq: ["$moreConsumedCount", 0] },
              0,
              {
                $divide: ["$moreConsumedSuccessCount", "$moreConsumedCount"],
              },
            ],
          },
        },
      },
      { $sort: { consultCount: -1 } }, // 按咨询人次降序排序
    ]);

    const records = await db
      .collection("consult-record")
      .aggregate(pipeline, {
        allowDiskUse: true,
        cursor: { batchSize: 1000 },
      })
      .toArray();

    return { success: true, data: records, message: "查询成功" };
  } catch (error) {
    return { success: false, message: error.message || "查询失败" };
  }
}

async function getConsultRecords(content) {
  let cursor;
  try {
    const {
      corpId,
      startDate,
      endDate,
      consultantFilter,
      projectIds,
      developerUserIds,
      consultStages,
      visitStatus,
      tradeStatus,
    } = content;

    let matchStage = {
      corpId,
      importTag: { $exists: false },
    };

    if (Array.isArray(consultantFilter) && consultantFilter.length) {
      matchStage.counselorUserId = { $in: consultantFilter };
    }

    // 初始筛选不包含projectIds的筛选，将在unwind后筛选
    if (Array.isArray(developerUserIds) && developerUserIds.length) {
      matchStage["introducerUserId"] = { $in: developerUserIds };
    }
    if (Array.isArray(consultStages) && consultStages.length) {
      matchStage["consultStage"] = { $in: consultStages };
    }
    if (Array.isArray(visitStatus) && visitStatus.length) {
      matchStage["visitStatus"] = { $in: visitStatus };
    }
    if (Array.isArray(projectIds) && projectIds.length) {
      matchStage["projectIds"] = { $in: projectIds };
    }
    if (tradeStatus === "traded") matchStage.tradeAmount = { $ne: 0 };
    if (tradeStatus === "untraded") matchStage.tradeAmount = { $eq: 0 };
    // 添加日期筛选条件
    if (startDate || endDate) {
      matchStage.createTime = {};
      if (startDate)
        matchStage.createTime.$gte = dayjs(startDate).startOf("day").valueOf();
      if (endDate)
        matchStage.createTime.$lte = dayjs(endDate).endOf("day").valueOf();
    }
    cursor = db.collection("consult-record").find(matchStage, {
      batchSize: 1000,
      noCursorTimeout: true,
    });

    const records = await cursor.toArray();
    return { success: true, data: records, message: "查询成功" };
  } catch (error) {
    return { success: false, message: error.message || "查询失败" };
  } finally {
    // 确保关闭游标以释放资源
    if (cursor && typeof cursor.close === "function") {
      await cursor
        .close()
        .catch((err) => console.error("关闭游标时出错:", err));
    }
  }
}

// 获取医生扣记录并关联会员表客户来源信息
async function getDoctorDeductRecords(content) {
  try {
    const {
      corpId,
      startDate,
      endDate,
      doctorUserIds,
      customerSource,
      projectIds,
      assistantDoctorFilter,
      deptFilter,
      doctorFilter,
    } = content;

    // 构建基础匹配条件
    let matchStage = {
      corpId,
      deductStatus: "deducted",
      importTag: { $exists: false },
    };
    // 添加日期筛选条件
    if (startDate || endDate) {
      matchStage.treatmentTime = {};
      if (startDate) {
        matchStage.treatmentTime.$gte = dayjs(startDate)
          .startOf("day")
          .valueOf();
      }
      if (endDate) {
        matchStage.treatmentTime.$lte = dayjs(endDate).endOf("day").valueOf();
      }
    }
    // 添加医生ID筛选
    if (Array.isArray(doctorUserIds) && doctorUserIds.length) {
      matchStage.doctorUserId = { $in: doctorUserIds };
    }

    // 添加项目ID筛选
    if (Array.isArray(projectIds) && projectIds.length) {
      matchStage.projectId = { $in: projectIds };
    }
    if (Array.isArray(assistantDoctorFilter) && assistantDoctorFilter.length) {
      matchStage.assistantDoctors = { $in: assistantDoctorFilter };
    }
    if (Array.isArray(deptFilter) && deptFilter.length) {
      matchStage.treatmentDept_id = { $in: deptFilter };
    }
    if (Array.isArray(doctorFilter) && doctorFilter.length) {
      matchStage.treatmentDoctorUserId = { $in: doctorFilter };
    }

    // 使用聚合管道查询
    const pipeline = [
      { $match: matchStage },
      // 关联会员表获取客户来源信息
      {
        $lookup: {
          from: "member",
          localField: "customerId",
          foreignField: "_id",
          as: "memberInfo",
        },
      },
      // 处理关联结果
      {
        $addFields: {
          customerSource: {
            $let: {
              vars: {
                memberData: { $arrayElemAt: ["$memberInfo", 0] },
              },
              in: "$$memberData.customerSource",
            },
          },
          customerNumber: {
            $let: {
              vars: {
                memberData: { $arrayElemAt: ["$memberInfo", 0] },
              },
              in: "$$memberData.customerNumber",
            },
          },
          customerSex: {
            $let: {
              vars: {
                memberData: { $arrayElemAt: ["$memberInfo", 0] },
              },
              in: "$$memberData.sex",
            },
          },
          customerName: {
            $let: {
              vars: {
                memberData: { $arrayElemAt: ["$memberInfo", 0] },
              },
              in: "$$memberData.name",
            },
          },
        },
      },
      // 如果有客户来源筛选条件，则继续过滤
      ...(Array.isArray(customerSource) && customerSource.length
        ? [
            {
              $match: {
                customerSource: { $in: customerSource },
              },
            },
          ]
        : []),
      // 移除不需要的大数据字段
      {
        $project: {
          memberInfo: 0, // 移除完整的会员信息，只保留提取出的customerSource
        },
      },
    ];

    const records = await db
      .collection("deduct-record")
      .aggregate(pipeline, {
        allowDiskUse: true,
        cursor: { batchSize: 1000 },
      })
      .toArray();

    return { success: true, data: records, message: "查询成功" };
  } catch (error) {
    return { success: false, message: error.message || "查询失败" };
  }
}

const request = require("../request");
const token = require("../token");
exports.main = async (context) => {
  switch (context.type) {
    case "addContactWay":
      return await addContactWay(context);
    case "delContactWay":
      return await delContactWay(context);
    case "updateContactWay":
      return await updateContactWay(context);
    default:
      return {
        success: false,
        message: "未找到对应方法",
      };
  }
};
async function addContactWay(context) {
  let {
    corpId,
    corpUserId,
    qrcodeId = "",
    qrCodetype = 1,
    skipVerify = 1,
    unionid = "",
  } = context;
  if (skipVerify == 1) {
    skipVerify = true;
  } else if (skipVerify == 2) {
    skipVerify = false;
  }
  if (!Array.isArray(corpUserId)) {
    corpUserId = [corpUserId];
  }
  const accessToken = await token.getToken({
    corpid: corpId,
  });
  let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/add_contact_way?access_token=${accessToken}`;
  let params = {
    type: qrCodetype,
    scene: 2,
    is_temp: false,
    user: corpUserId,
    skipVerify,
  };
  if (unionid) params.state = unionid;
  if (qrcodeId) params.state = qrcodeId;
  let { qr_code, errcode, config_id, errmsg } = await request.main(
    url,
    params,
    "POST"
  );
  let response = {};
  switch (errcode) {
    case 0:
      response = {
        success: true,
        message: "获取成功",
        data: qr_code,
        configId: config_id,
        errcode,
      };
      break;
    case 40098:
      response = {
        success: false,
        message: "该员工企业微信未认证,请先认证",
      };
      break;
    case 84074:
      response = {
        success: false,
        message: "没有外部联系人权限,请联系管理员开通权限",
      };
      break;
    case 41054:
      response = {
        success: false,
        message: "该用户尚未激活,请成员登录企业微信，且已实名认证",
      };
      break;
    default:
      response = {
        success: false,
        message: errmsg,
        errcode,
      };
      break;
  }
  return response;
}

async function delContactWay(context) {
  try {
    let { corpId, configId } = context;
    const accessToken = await token.getToken({
      corpid: corpId
    });
    let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/del_contact_way?access_token=${accessToken}`;
    let params = {
      config_id: configId,
    };
    let { errcode } = await request.main(url, params, "POST");
    return {
      success: true,
      message: "删除成功",
      errcode,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
}
async function updateContactWay(context) {
  try {
    let {
      corpId,
      configId,
      corpUserId,
      skipVerify = 1,
    } = context && context.params;
    if (skipVerify == 1) {
      skipVerify = true;
    } else if (skipVerify == 2) {
      skipVerify = false;
    }
    const accessToken = await token.getToken({
      corpid: corpId,
    });
    let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/update_contact_way?access_token=${accessToken}`;
    let params = {
      config_id: configId,
      user: corpUserId,
      skip_verify: skipVerify,
    };
    let { errcode } = await request.main(url, params, "POST");
    return {
      success: true,
      message: "更新成功",
      errcode,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
}

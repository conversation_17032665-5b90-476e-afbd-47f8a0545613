const dayjs = require("dayjs");
const utils = require("../utils");
const common = require("../../common");
const api = require("../../api");
let db = null;
let currentCorpId = null;

exports.main = async (content, DB) => {
  db = DB;
  currentCorpId = content.corpId;
  switch (content.type) {
    case "getCustomerType":
      return await exports.getCustomerType(content);
    case "getPannedEvent":
      return await exports.getPannedEvent(content);
    case "getPannedEventById":
      return await exports.getPannedEventById(content);
    case "createPannedEvent":
      return await exports.createPannedEvent(content);
    case "updatePannedEvent":
      return await exports.updatePannedEvent(content);
    case "removePannedEvent":
      return await exports.removePannedEvent(content);
    case "getPannedEventResult":
      return await exports.getPannedEventResult(content);
    case "planEventCreateTodoEvent":
      return await planEventCreateTodoEvent(content);
  }
};

let MAX_LIMIT = 100;

// 获取客户类型
exports.getCustomerType = async (content) => {
  try {
    let res = await db.collection("customer-type").find().toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 获取团队列表
async function getTeamList(executorUserId, corpId) {
  const result = await api.getCorpApi({
    type: "getTeamBymember",
    corpUserId: executorUserId,
    corpId,
  });
  if (result && Array.isArray(result.data) && result.data.length > 0)
    return result.data.map((item) => item.teamId);
  else return [];
}

// 获取计划事件
exports.getPannedEvent = async (content) => {
  const { params, teamId } = content;
  const { corpId, userId, ...rest } = params;
  let query = {
    corpId,
    ...rest,
  };
  if (!teamId) {
    const teamIds = await getTeamList(userId, corpId);
    query["teamId"] = { $in: teamIds };
  } else {
    query["teamId"] = teamId;
  }
  try {
    let data = await db
      .collection("panned-event")
      .find(query)
      .sort({ createTime: -1 })
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 根据ID获取计划事件
exports.getPannedEventById = async (content) => {
  const { id } = content;
  try {
    let data = await db.collection("panned-event").findOne({ _id: id });
    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 创建计划事件
exports.createPannedEvent = async (content) => {
  const { params } = content;
  const {
    corpId,
    pannedEventName,
    memberIds,
    planTime,
    pannedSendContent,
    pannedCreateMember,
    customerType,
    executionSatus,
    teamId,
    teamName,
    tagsName,
    taskContent,
    tagIds,
    excludeTagIds,
    excludeTagsName,
    pannedEventSendFile,
    taskPurpose,
  } = params;
  let item = {
    _id: common.generateRandomString(24),
    corpId,
    pannedEventName,
    memberIds,
    pannedSendContent,
    pannedEventSendFile,
    pannedCreateMember,
    customerType,
    executionSatus,
    teamId,
    teamName,
    tagsName,
    planTime,
    taskContent,
    tagIds,
    excludeTagIds,
    excludeTagsName,
    customersTotal: 0,
    taskPurpose,
    createTime: new Date().getTime(),
  };
  try {
    let res = await db.collection("panned-event").insertOne(item);
    return {
      success: true,
      message: "添加成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: "添加失败",
    };
  }
};

// 更新计划事件
exports.updatePannedEvent = async (content) => {
  const { params, id } = content;
  params["updateTime"] = new Date().getTime();
  try {
    let res = await db
      .collection("panned-event")
      .updateOne({ _id: id }, { $set: params });
    return {
      success: true,
      message: "更新成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: "更新失败",
    };
  }
};

// 删除计划事件
exports.removePannedEvent = async (content) => {
  const { id } = content;
  try {
    await db.collection("panned-event").deleteOne({ _id: id });
    return {
      success: true,
      message: "删除成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "删除失败",
    };
  }
};

// 获取计划事件结果
exports.getPannedEventResult = async (content) => {
  try {
    const { params, corpId } = content;
    const item = await db.collection("panned-event").findOne(params);
    const customCount = await getOnInhopsitlAllMember(item);
    let toDoEvents = await getTodoEventsByPannedEventId(item._id, corpId);
    const distributedTaskCount = toDoEvents.length;
    const executedTaskCount = toDoEvents.filter(
      (item) => item.eventStatus === "treated"
    ).length;
    const feedbackTaskCount = toDoEvents.filter(
      (item) => item["isFeedback"]
    ).length;
    return {
      success: true,
      message: "获取成功",
      data: {
        toDoEvents,
        customCount,
        distributedTaskCount,
        executedTaskCount,
        feedbackTaskCount,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 获取未到院客户数
async function getOnInhopsitlAllMember(item) {
  const { planTime, corpId, tagIds, teamId } = item;
  const { startTime, endTime } = getStartAndEndTime(planTime);
  const query = {
    createTime: { $gte: startTime, $lte: endTime },
    tagIds: { $in: tagIds },
    corpId,
    teamId,
    isRecord: { $ne: true },
  };
  let allData = [];
  let skip = 0;
  let total = await db.collection("member").countDocuments(query);
  do {
    const res = await db
      .collection("member")
      .find(query)
      .skip(skip)
      .limit(MAX_LIMIT)
      .toArray();
    allData = allData.concat(res);
    skip += MAX_LIMIT;
  } while (allData.length < total);
  return allData.length;
}

// 获取开始和结束时间
function getStartAndEndTime(day) {
  const laterTime = dayjs().subtract(Number(day), "day");
  const startTime = laterTime.startOf("day").valueOf();
  const endTime = laterTime.endOf("day").valueOf();
  return {
    startTime,
    endTime,
  };
}

// 根据计划事件ID获取待办事件
async function getTodoEventsByPannedEventId(id, corpId) {
  let allData = [];
  let page = 1;
  let result = await api.getTodoApi({
    type: "getTodoEventsByPannedEventId",
    pannedEventId: id,
    corpId,
    page,
    pageSize: MAX_LIMIT,
  });
  allData = result.data;
  do {
    page++;
    let { result } = await api.getTodoApi({
      type: "getTodoEventsByPannedEventId",
      pannedEventId: id,
      corpId,
      page,
      pageSize: MAX_LIMIT,
    });
    allData = allData.concat(result.data);
  } while (allData.length < result.total);
  return allData;
}

// 计划事项创建待办事项
async function planEventCreateTodoEvent({ corpId }) {
  let query = {
    executionSatus: true,
  };
  if (corpId) query["corpId"] = corpId;
  const fetchData = async (page, pageSize, db) => {
    let data = await db
      .collection("panned-event")
      .find(query)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    return data;
  };
  let list = await utils.getAllData(fetchData, db);
  console.log(" 获取到所有计划事项", list);
  for (let params of list) {
    let customers = await matchCustomer(params);
    console.log(" 获取到所有客户", customers);
    let arr = customers.map((item) => {
      const { _id, name, externalUserId } = item;
      params["pannedEventId"] = params._id;
      params["customerId"] = _id;
      params["customerName"] = name;
      params["externalUserId"] = externalUserId;
      params["executorUserId"] = "";
      if (
        item.personResponsibles &&
        Array.isArray(item.personResponsibles) &&
        item.personResponsibles.length > 0
      ) {
        let obj = item.personResponsibles.find(
          (item) => item.teamId === params.teamId
        );
        if (obj) params["executorUserId"] = obj.corpUserId;
      }
      return createEvents(params);
    });
    for (let i = 0; i < arr.length; i += 10) {
      const promiseArr = arr.slice(i, i + 10);
      await Promise.all(promiseArr);
    }
  }
  return {
    success: true,
    message: "创建成功",
  };
}

// 创建事件
async function createEvents(params) {
  console.log("获取到所有客户待办的", params);
  let res = await api.getTodoApi({
    type: "createEvents",
    params,
    corpId: currentCorpId,
  });
  console.log(res);
}

// 匹配客户
async function matchCustomer(item) {
  console.log(`开始匹配客户`, item);
  const { planTime, corpId, tagIds, teamId, excludeTagIds, customerType } =
    item;
  const { startTime, endTime } = getStartAndEndTime(planTime);
  const params = {
    createTime: { $gte: startTime, $lte: endTime },
    corpId,
    teamId,
    customerStage: customerType,
  };
  let tagIdsArr = [];
  if (Array.isArray(tagIds) && tagIds.length > 0) {
    tagIdsArr = [{ tagIds: { $all: tagIds } }];
  } else if (Array.isArray(excludeTagIds) && excludeTagIds.length > 0) {
    tagIdsArr = [{ tagIds: { $nin: excludeTagIds } }];
  } else if (tagIds.length > 0 && excludeTagIds.length > 0) {
    tagIdsArr = [
      { tagIds: { $all: tagIds } },
      { tagIds: { $nin: excludeTagIds } },
    ];
  }
  const fetchData = async (page, pageSize, db) => {
    let data = await db
      .collection("member")
      .find({ $and: [...tagIdsArr, params] })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    return data;
  };
  let members = await utils.getAllData(fetchData, db);
  if (members.length > 0) await updateCustomersTotal(item, members.length);
  return members;
}

// 更新客户总数
async function updateCustomersTotal(item, num) {
  let { _id, customersTotal } = item;
  customersTotal += num;
  await db
    .collection("panned-event")
    .updateOne({ _id }, { $set: { customersTotal } });
}

let db = null;
exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getPageDisease":
      return await exports.getPageDisease(content);
    case "getDisease":
      return await exports.getDisease(content);
  }
};

exports.getPageDisease = async (context) => {
  try {
    let { diseaseName, page, pageSize } = context;
    const query = {};
    if (typeof diseaseName === "string" && diseaseName.trim() !== "") {
      query.diseaseName = new RegExp(diseaseName.trim(), "i"); // 使用正则表达式进行模糊查询
    }
    const total = await db.collection("diseases").countDocuments(query); // 获取总数
    pageSize = Number.isInteger(pageSize) && pageSize > 0 ? pageSize : 20;
    page = Number.isInteger(page) && page > 0 ? page : 1;
    const pages = Math.ceil(total / pageSize);

    const res = await db
      .collection("diseases")
      .find(query)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    return {
      success: true,
      message: "获取成功",
      list: res,
      total,
      pages,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

exports.getDisease = async (context) => {
  try {
    const query = {};
    if (
      typeof context.diseaseName === "string" &&
      context.diseaseName.trim() !== ""
    ) {
      query.diseaseName = new RegExp(context.diseaseName.trim(), "i"); // 使用正则表达式进行模糊查询
    }
    const res = await db.collection("diseases").find(query).toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

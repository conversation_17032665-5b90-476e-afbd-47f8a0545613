const disease = require("./disease");
const welcome = require("./welcome");
const commonWords = require("./common-words");
const article = require("./article");
const region = require("./region");
const group = require("./group");
const staffQrcode = require("./market-center/staffQrcode");
const qrcodeStatistics = require("./market-center/qrcodeStatistics");
const rate = require("./rate");
exports.main = async (event, db) => {
  switch (event.type) {
    case "getDisease":
    case "getPageDisease":
      return await disease.main(event, db);
    case "getWelcomeList":
    case "getWelcome":
    case "updateWelcome":
    case "removeWelcome":
      return await welcome.main(event, db);
    case "getCommonWordsCates":
    case "removeCommonWordsCate":
    case "setCommonWordsCate":
    case "setCommonWords":
    case "getCommonWordsList":
    case "removeCommonWords":
    case "addCorpCommonWordCate":
    case "deleteCorpCommonWordCate":
    case "deleteUserCommonWordCate":
    case "getCorpCommonWordCate":
    case "getUserCommonWordCate":
    case "insertCateFromOldCate":
    case "updateCorpCommonWordCate":
    case "updateUserCommonWordCate":
    case "sortCorpCommonWordCate":
    case "addUserCommonWordCate":
    case "getCommonWordsCount":
      return await commonWords.main(event, db);
    case "getArticleCates":
    case "removeArticleCate":
    case "setArticle":
    case "setArticleCate":
    case "getArticle":
    case "getArticleList":
    case "getArticleByIds":
    case "removeArticle":
    case "toggleArticleStatus":
    case "addArticleSendRecord":
    case "addArticleReadRecord":
    case "getArticleStats":
    case "getArticleTrend":
    case "getSendDetail":
    case "getReadDetail":
    case "getArticleStatus":
    case "initCorpArticleCate":
    case "addArticleCate":
    case "updateArticleCate":
    case "deleteArticleCate":
    case "getArticleCateList":
    case "sortArticleCate":
    case "getArticleListReadStats":
    case "getArticleCount":
      return await article.main(event, db);
    case "createGroup":
    case "getGroups":
    case "removeGroup":
    case "updateGroup":
    case "addGroupIdForMember":
      return await group.main(event, db);
    case "getRegion":
      return await region.getRegion(event, db);
    case "getStaffQrcode":
    case "removeStaffQrcode":
    case "addStaffQrcode":
    case "getStaffQrCodeDetail":
    case "updateStaffQrcode":
    case "getPersonalQrCode":
    case "getPersonalQrcodeList":
    case "getStaffQrcodeByQrcodeId":
    case "addStaffQrcodeCate":
    case "updateStaffQrcodeCate":
    case "deleteStaffQrcodeCate":
    case "getStaffQrcodeCateList":
    case "initCorpStaffQrcodeCate":
    case "sortStaffQrcodeCate":
    case "getStaffQrcodeCount":
      return await staffQrcode.main(event, db);
    case "getQrcodeCustomerCount":
    case "addQrcodeCustomerCount":
    case "getQrcodeCustomer":
    case "getQrcodeStaticsticsSort":
    case "getQrcodeCustomerStatsByCateId":
      return await qrcodeStatistics.main(event, db);
    case "getCorpRateConfig":
    case "addRateTag":
    case "deleteRateTag":
    case "updateRateTagText":
    case "addRateRecord":
    case "getRateRecord":
    case "submitRateRecord":
    case "getCorpRateRecord":
    case "getMemberRateRecord":
    case "getCorpRateStats":
    case 'getRateRecordCount':
      return await rate.main(event, db);
  }
};

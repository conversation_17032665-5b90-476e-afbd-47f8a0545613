const request = require("../request");
const accessToken = require("../token");
const api = require("../../api");
/**
 * 合并机构标签
 *
 * @param {*} context
 */
exports.getCorpTagList = async (context) => {
  const { corpId } = context;
  let res = await this.getWeComCorpTag({ corpId });
  if (res.errcode == 0) {
    let { tag_group } = res;
    for (let item of tag_group) {
      let { group_id, group_name, order, tag } = item;
      await api.getCorpApi({
        type: "addCorpGroupTag",
        corpId,
        groupId: group_id,
        groupName: group_name,
        createType: "corpAsync",
        order,
        tag,
      });
    }
  }
  return {
    success: true,
    message: "同步成功",
  };
};
/**
 * 获取企业微信标签
 * @param {string} env 环境ID
 * @param {string} corpId 企业微信ID
 * @returns
 **/
exports.getWeComCorpTag = async ({ env, corpId }) => {
  let corpRes = await api.getCorpApi({
    type: "getCorpInfo",
    corpId,
  });
  let corp = corpRes.data[0];
  let access_token = await accessToken.getToken({
    corpId: corpId,
    permanentCode: corp.permanent_code,
  });
  let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_corp_tag_list?access_token=${access_token}`;
  let res = await request.main(url, null, "GET");
  return res;
};

/**
 * 合并企业微信标签
 * @param {string} corpId 企业微信ID
 * @param {string} externalUserId 外部联系人ID
 * @param {string} env 环境ID
 * @param {string} tagType 变更类型 删除标签时，此项为tag，删除标签组时，此项为tag_group
 * @param {string} id 标签ID 标签或标签组的ID
 * @param {string} changeType 标签类型 delete-删除  update-更新  create-新增
 */
exports.syncCorpTag = async ({ corpId, env, tagType, id, changeType }) => {
  let res = await this.getWeComCorpTag({ env, corpId });
  if (res.errcode == 0) {
    let { tag_group } = res;
    if (tag_group.length == 0) {
      if (tagType == "tag_group" && changeType == "delete") {
        return await updateGropTagGroup({ changeType, id, corpId, env });
      }
    }
    for (let item of tag_group) {
      // 标签组
      let { group_id, tag } = item;
      if (
        (tagType == "tag_group" &&
          (group_id == id || changeType == "delete")) ||
        (changeType == "shuffle" && group_id == id)
      ) {
        return await updateGropTagGroup({ changeType, id, corpId, env, item });
      }
      if (tagType == "tag" && changeType == "delete") {
        return await updateGropTagGroup({
          changeType: "update",
          id: group_id,
          corpId,
          env,
          item,
        });
      }
      for (let tagItem of tag) {
        if (tagType == "tag" && tagItem.id == id) {
          console.log("修改标签tag", {
            changeType: "update",
            id: group_id,
            corpId,
            env,
          });
          return await updateGropTagGroup({
            changeType: "update",
            id: group_id,
            corpId,
            env,
            item,
          });
        }
      }
    }
  }
};

/**
 * 同步企业微信标签组
 * @param {string} corpId 企业微信ID
 * @param {string} env 环境ID
 * @param {string} tagType 标签类型 delete-删除  update-更新  create-新增
 * @param {string} id 标签组的ID
 */
async function updateGropTagGroup({ changeType, id, corpId, env, item = {} }) {
  try {
    let type = "";
    let params = {
      corpId,
      groupId: id,
      groupName: item.group_name,
      order: item.order,
      tag: item.tag,
    };
    if (changeType == "delete") {
      type = "deleteCorpGroupTag";
    } else if (changeType == "update" || changeType == "shuffle") {
      // 更新标签组
      type = "updateCorpGroupTag";
    } else if (changeType == "create") {
      // 新建标签组
      type = "addCorpGroupTag";
      params["createType"] = "corpAsync";
    }
    return await api.getCorpApi({
      type,
      ...params,
    });
  } catch (e) {
    return {
      success: false,
      message: "同步企业微信标签组失败",
    };
  }
}
/**
 * 同步企业微信标签
 * @param {string} corpId 企业微信ID
 * @param {string} env 环境ID
 * @param {string} tagType 标签类型 delete-删除  update-更新  create-新增
 * @param {string} id 标签的ID
 * @param {string} groupId 标签组的ID
 */
async function updateGropTag({ changeType, id, corpId, env, item, groupId }) {
  try {
    let type = "";
    if (changeType == "delete") {
      // 删除标签
      type = "deleteCorpTag";
    } else if (changeType == "update") {
      type = "updateCorpTag";
    } else if (changeType == "create") {
      // 新建标签
      type = "addCorpTagToGroup";
    }
    return await api.getCorpApi({
      type: type,
      corpid: corpId,
      groupId,
      id,
      env,
      name: item.name,
      order: item.order,
    });
  } catch (e) {
    return {
      success: false,
      message: "同步企业微信标签失败",
    };
  }
}

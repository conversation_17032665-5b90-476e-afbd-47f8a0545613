const user = require("./sys-user");
const role = require("./sys-role");
const menu = require("./sys-menu");
const template = require("./sys-template");
exports.main = async (event, db) => {
  switch (event.type) {
    case "getUserInfo":
    case "getCorpUsers":
    case "getMenuList":
      return await user.main(event, db);
    case "getRoles":
    case "addRole":
    case "updateRole":
    case "deleteRole":
    case "getRoleListByIds":
    case "getRolesByRoleId":
      return await role.main(event, db);
    case "getMenus":
    case "getMenusByMenuIds":
    case "setCorpMenuConfig":
      return await menu.main(event, db);
    case "getPlatFormTemplate":
      return await template.getPlatFormTemplate(event, db);
  }
};

const request = require("../request");
const accessToken = require("../token");
exports.main = async (context) => {
  let { access_token = "", corpId, permanentCode, suiteToken } = context;
  if (!access_token) {
    console.log("获取access_token", access_token);
    access_token = await accessToken.getToken({
      corpId: corpId,
      permanentCode: permanentCode,
      suiteToken,
    });
    console.log("获取access_token", access_token);
    context.accesstoken = access_token;
  }
  switch (context.type) {
    case "setSessoinPublicKey":
      return await setSessoinPublicKey(context);
    case "getSessionAuthUserList":
      return await getSessionAuthUserList(context);
    case "getSessionSearchMsg":
      return await getSessionSearchMsg(context);
    case "syncCallProgram":
      return await syncCallProgram(context);
    case "asyncProgramTask":
      return await asyncProgramTask(context);
    default: {
      return {
        success: false,
        message: "未找到对应的操作类型",
      };
    }
  }
};

async function setSessoinPublicKey({
  publicKeyVersion,
  publicKey,
  accesstoken,
}) {
  let url = `https://qyapi.weixin.qq.com/cgi-bin/chatdata/set_public_key?access_token=${accesstoken}`;
  let res = await request.main(
    url,
    { public_key: publicKey, public_key_ver: publicKeyVersion },
    "POST"
  );
  if (res.errcode === 0) {
    return {
      success: true,
      message: "设置成功",
      publicKeyVersion,
    };
  } else if (res.errcode === 40212) {
    publicKeyVersion++;
    return await setSessoinPublicKey({
      publicKeyVersion,
      publicKey,
      accesstoken,
    });
  } else {
    return {
      success: false,
      errcode: res.errcode,
      message: res.errmsg,
    };
  }
}

async function getSessionAuthUserList(item) {
  const { cursor, limit, accesstoken } = item;
  let url = `https://qyapi.weixin.qq.com/cgi-bin/chatdata/get_auth_user_list?access_token=${accesstoken}`;
  let query = {};
  if (cursor) query.cursor = cursor;
  if (limit) {
    query.limit = Number(limit);
  }
  let res = await request.main(url, query, "POST");
  if (res.errcode === 0) {
    return {
      success: true,
      message: "获取成功",
      data: res.auth_user_list,
      next_cursor: res.next_cursor,
      has_more: res.has_more,
    };
  } else {
    return {
      success: false,
      message: res.errmsg,
      has_more: false,
      next_cursor: "",
      data: [],
    };
  }
}

async function getSessionSearchMsg(item) {
  const { keyword, limit, cursor, accesstoken } = item;
  let query = {
    query_word: keyword,
    limit,
  };
  if (cursor) query.cursor = cursor;
  let url = `https://qyapi.weixin.qq.com/cgi-bin/chatdata/search_msg?access_token=${accesstoken}`;
  let res = await request.main(url, query, "POST");
  if (res.errcode !== 0) {
    let message = res.errmsg;
    if (res.errcode === 40058) {
      message = "搜索的关键字不能少于1个字";
    }
    return {
      success: false,
      message: message,
    };
  } else {
    return {
      success: true,
      message: "获取成功",
      data: res.msg_list,
      next_cursor: res.next_cursor,
      has_more: res.has_more,
    };
  }
}
async function syncCallProgram(item) {
  const { accesstoken, program_id, ability_id, request_data, notify_id } = item;
  let query = {
    program_id,
    ability_id,
    request_data: JSON.stringify(request_data),
  };

  if (notify_id) query.notify_id = notify_id;
  console.log("syncCallProgram query", query);
  let url = `https://qyapi.weixin.qq.com/cgi-bin/chatdata/sync_call_program?access_token=${accesstoken}`;
  let res = await request.main(url, query, "POST");
  if (res.errcode !== 0) {
    return {
      success: false,
      message: res.errmsg,
      errcode: res.errcode,
    };
  } else {
    const data = res.response_data ? JSON.parse(res.response_data) : {};
    return {
      success: true,
      message: "调用成功",
      data,
    };
  }
}

async function asyncProgramTask(item) {
  let { jobid, accesstoken } = item;
  const query = {
    jobid,
  };
  let url = `https://qyapi.weixin.qq.com/cgi-bin/chatdata/async_program_task?access_token=${accesstoken}`;
  let res = await request.main(url, query, "POST");
  if (res.errcode !== 0) {
    return {
      success: false,
      message: res.errmsg,
      errcode: res.errcode,
    };
  } else {
    return {
      success: true,
      message: "调用成功",
      ...res,
    };
  }
}

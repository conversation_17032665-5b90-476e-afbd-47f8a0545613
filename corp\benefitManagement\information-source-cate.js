let db = null;
const { ObjectId } = require("mongodb"); // 添加 ObjectId 导入
exports.main = async (context, mongodb) => {
  db = mongodb;
  switch (context.type) {
    case "addSourceCate":
      return await addSourceCate(context);
    case "updateSourceCate":
      return await updateSourceCate(context);
    case "deleteSourceCate":
      return await deleteSourceCate(context);
    case "getSourceCateList":
      return await getSourceCateList(context);
    case "sortSourceCate":
      return await sortSourceCate(context);
  }
};

async function addSourceCate(context) {
  let { corpId, label, parentId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    if (parentId) {
      const parent = await db
        .collection("information-source-cate")
        .findOne({ _id: new ObjectId(parentId), corpId });
      if (!parent) return { success: false, message: "父级分类不存在" };
      if (![1, 2, 3].includes(parent.level))
        return { success: false, message: "父级分类层级错误" };
      const parentGroup = [...(parent.parentGroup || []), parent._id];
      const res = await db.collection("information-source-cate").insertOne({
        corpId,
        label,
        level: parent.level + 1,
        parentId,
        createTime: Date.now(),
        parentGroup,
      });
      return { success: true, data: res, message: "添加分类成功" };
    }
    const payload = {
      corpId,
      label,
      level: 1,
      createTime: Date.now(),
      parentGroup: [],
    };
    const res = await db
      .collection("information-source-cate")
      .insertOne(payload);
    return { success: true, data: res, message: "添加分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function updateSourceCate(context) {
  let { corpId, id: _id, label } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  if (typeof label !== "string" || label.trim() === "")
    return { success: false, message: "分类名称无效" };
  if (label.trim().length > 10)
    return { success: false, message: "分类名称不能超过10个字" };
  try {
    const cate = await db
      .collection("information-source-cate")
      .findOne({ _id: new ObjectId(_id), corpId });
    if (cate) {
      const payload = { label: label.trim() };
      await db
        .collection("information-source-cate")
        .updateOne({ _id: new ObjectId(_id), corpId }, { $set: payload });
      return { success: true, message: "更新分类成功" };
    }
    return { success: false, message: "分类不存在" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function deleteSourceCate(context) {
  let { corpId, id: _id } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "分类id不能为空" };
  try {
    const cate = await db
      .collection("information-source-cate")
      .findOne({ _id: new ObjectId(_id), corpId });
    if (!cate) return { success: false, message: "分类不存在" };
    const parentGroup = [...(cate.parentGroup || []), _id];
    const childrenQuery = parentGroup.reduce(
      (acc, cur, idx) => {
        acc[`parentGroup.${idx}`] = cur; // 查询子分类
        return acc;
      },
      { corpId }
    );
    const query = { $or: [{ corpId, _id: new ObjectId(_id) }, childrenQuery] };
    const list = await db
      .collection("information-source-cate")
      .find(query, { projection: { _id: 1 } })
      .toArray();
    const ids = list.map((i) => i._id);
    const res2 = await db
      .collection("information-source-list")
      .updateMany(
        { corpId, sourceCateIdGroup: { $in: ids } },
        { $pull: { sourceCateIdGroup: { $in: ids } } }
      );
    const res = await db
      .collection("information-source-cate")
      .deleteMany({ corpId, _id: { $in: ids } });
    return {
      success: true,
      data: {
        cateDeleted: res.deletedCount,
        sourceDeleted: res2.modifiedCount,
      },
      message: "删除分类成功",
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function getSourceCateList(context) {
  let { corpId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const list = await db
      .collection("information-source-cate")
      .find({ corpId, init: { $exists: false } })
      .sort({ createTime: 1 })
      .limit(10000)
      .toArray();
    if (list.length) return { success: true, list, message: "获取分类成功" };
    const initRecord = await db
      .collection("information-source-cate")
      .findOne({ corpId, init: true });
      
    if (initRecord) return { success: true, list: [], message: "获取分类成功" };
    await db
      .collection("information-source-cate")
      .insertOne({ corpId, init: true });
    await db
      .collection("information-source-cate")
      .insertOne({ corpId, label: "默认", level: 1, createTime: Date.now() });
    const newlist = await db
      .collection("information-source-cate")
      .find({ corpId, init: { $exists: false } })
      .sort({ createTime: 1 })
      .limit(10000)
      .toArray();
    return { success: true, list: newlist, message: "获取分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

async function sortSourceCate(context) {
  const { corpId, sortData } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  const arr = Array.isArray(sortData)
    ? sortData.filter(
        (i) =>
          i._id && typeof i.sort === "number" && i.sort % 1 === 0 && i.sort >= 0
      )
    : [];
  if (arr.length === 0) return { success: false, message: "参数错误" };

  const bulkOps = arr.map((item) => ({
    updateOne: {
      filter: { _id: new ObjectId(item._id), corpId, parentId: { $exists: false } },
      update: { $set: { sort: item.sort } },
    },
  }));

  try {
    const res = await db
      .collection("information-source-cate")
      .bulkWrite(bulkOps);
    if (res.modifiedCount === arr.length) {
      return { success: true, message: "操作成功" };
    } else {
      return { success: false, message: "部分更新失败" };
    }
  } catch (e) {
    return { success: false, message: e.message };
  }
}

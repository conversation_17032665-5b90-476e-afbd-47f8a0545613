const dayjs = require("dayjs");
const common = require("../../common");
const { projectNameObj, infoObj } = require("./hz-intent");
let db = null;
exports.main = async (content, mongodb) => {
  db = mongodb;
  switch (content.type) {
    case "getProjectIntentList":
      return await exports.getProjectIntentList(content);
    case "addProjectIntent":
      return await exports.addProjectIntent(content);
    case "updateProjectIntent":
      return await exports.updateProjectIntent(content);
    case "updateProjectIntentStatus":
      return await exports.updateProjectIntentStatus(content);
    case "getProjectIntentNames":
      return await getProjectIntentNames(content);
    case "getProjectIntentsByIds":
      return await exports.getProjectIntentsByIds(content);
    case "transformProjectData":
      return await exports.transformProjectData(content);
  }
};

// 获取项目意向列表
exports.getProjectIntentList = async (content) => {
  const {
    corpId,
    pageNum = 1,
    pageSize = 10,
    status,
    deptId,
    keyword,
  } = content;
  try {
    const query = { corpId };
    if (status) {
      query.status = status;
    }
    if (deptId) {
      query.deptId = deptId;
    }
    if (keyword) {
      query.$or = [
        { projectName: { $regex: keyword, $options: "i" } },
        { pinyinCode: { $regex: keyword, $options: "i" } },
      ];
    }

    const total = await db.collection("project-intent").countDocuments(query);
    const list = await db
      .collection("project-intent")
      .find(query)
      .sort({ status: 1, createTime: -1 }) // active排前，inactive排后，然后按创建时间降序
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .toArray();

    return {
      success: true,
      data: {
        list,
        total,
        pageNum,
        pageSize,
      },
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

async function getProjectIntentNames(ctx) {
  const { corpId, ids } = ctx;
  if (!corpId || !Array.isArray(ids) || ids.length === 0)
    return { success: false, msg: "缺少参数" };
  try {
    const data = await db
      .collection("project-intent")
      .find(
        { corpId, _id: { $in: ids } },
        { projection: { projectName: 1, _id: 1, deptId: 1 } }
      )
      .limit(ids.length || 1)
      .toArray();
    return { success: true, data };
  } catch (e) {
    return { success: false, msg: e.message, data: [] };
  }
}

// 添加项目意向
exports.addProjectIntent = async (content) => {
  const {
    corpId,
    projectName,
    deptName,
    deptId,
    pinyinCode,
    projectCode,
    status = "active", // 默认为启用状态，使用英文表示
  } = content;
  try {
    // 验证必填字段
    if (!projectName) {
      return {
        success: false,
        message: "项目名称不能为空",
      };
    }
    if (!deptName) {
      return {
        success: false,
        message: "所属科室不能为空",
      };
    }

    const now = Date.now();
    const result = await db.collection("project-intent").insertOne({
      corpId,
      projectName,
      deptName,
      deptId,
      pinyinCode,
      status,
      projectCode,
      _id: common.generateRandomString(24),
      createTime: now,
      updateTime: now,
    });

    return {
      success: true,
      data: result.insertedId,
      message: "添加成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 编辑项目意向
exports.updateProjectIntent = async (content) => {
  const { corpId, id, projectName, deptId, deptName, pinyinCode, projectCode } =
    content;
  try {
    // 验证必填字段
    if (!projectName) {
      return {
        success: false,
        message: "项目名称不能为空",
      };
    }
    if (!deptId) {
      return {
        success: false,
        message: "所属科室不能为空",
      };
    }

    // 验证项目意向是否存在
    const intent = await db.collection("project-intent").findOne({
      corpId,
      _id: id,
    });

    if (!intent) {
      return {
        success: false,
        message: "项目意向不存在",
      };
    }

    await db.collection("project-intent").updateOne(
      { corpId, _id: id },
      {
        $set: {
          projectName,
          deptId,
          deptName,
          pinyinCode,
          projectCode,
          updateTime: Date.now(),
        },
      }
    );

    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 修改项目意向状态
exports.updateProjectIntentStatus = async (content) => {
  const { corpId, id, status } = content;
  try {
    // 验证状态值
    if (status !== "active" && status !== "inactive") {
      return {
        success: false,
        message: "状态值无效",
      };
    }

    // 验证项目意向是否存在
    const intent = await db.collection("project-intent").findOne({
      corpId,
      _id: id,
    });

    if (!intent) {
      return {
        success: false,
        message: "项目意向不存在",
      };
    }

    await db.collection("project-intent").updateOne(
      { corpId, _id: id },
      {
        $set: {
          status,
          updateTime: Date.now(),
        },
      }
    );

    return {
      success: true,
      message: status === "active" ? "启用成功" : "停用成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 根据ids获取所有项目
exports.getProjectIntentsByIds = async (content) => {
  const { corpId, ids } = content;

  // 参数校验
  if (!corpId || !Array.isArray(ids) || ids.length === 0) {
    return {
      success: false,
      message: "参数错误",
      data: [],
    };
  }
  try {
    // 分批次查询以避免可能的数据库限制
    const batchSize = 100; // 每批查询的ID数量
    let allProjects = [];
    // 将IDs分批处理
    for (let i = 0; i < ids.length; i += batchSize) {
      const batchIds = ids.slice(i, i + batchSize);
      const batchProjects = await db
        .collection("project-intent")
        .find({ corpId, _id: { $in: batchIds } })
        .toArray();
      allProjects = allProjects.concat(batchProjects);
    }
    return {
      success: true,
      data: allProjects,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
      data: [],
    };
  }
};

// 数据转换：从老项目表获取数据并关联到新项目表
exports.transformProjectData = async (content) => {
  const { ids } = content;

  // 参数校验
  if (ids.length === 0) {
    return {
      success: false,
      message: "参数错误",
      data: [],
    };
  }

  try {
    // 1. 从老项目表获取数据，提取 projectName
    const oldProjects = await db
      .collection("project-list")
      .find({ _id: { $in: ids } })
      .toArray();

    if (!oldProjects || oldProjects.length === 0) {
      return {
        success: false,
        message: "未找到老项目数据",
        data: [],
      };
    }

    // 2. 提取所有 projectName 并通过 projectNameObj 进行转换
    const mappedProjectNames = oldProjects
      .map((project) => {
        const originalName = project.projectName;
        // 使用 projectNameObj 转换项目名称，如果没有对应的映射则保持原名
        return originalName
          ? projectNameObj[originalName] || originalName
          : null;
      })
      .filter(Boolean); // 过滤掉空值

    if (mappedProjectNames.length === 0) {
      return {
        success: false,
        message: "老项目数据中没有有效的项目名称",
        data: [],
      };
    }

    // 3. 根据转换后的 projectName 查询新项目表
    const newProjects = await db
      .collection("project-intent")
      .find({ projectName: { $in: mappedProjectNames } })
      .toArray();

    if (!newProjects || newProjects.length === 0) {
      return {
        success: false,
        message: "未找到匹配的新项目数据",
        data: [],
        mappedNames: mappedProjectNames, // 返回映射后的名称，方便调试
      };
    }

    // 4. 提取新项目表中的 _id
    const newProjectIds = newProjects.map((project) => project._id);

    return {
      success: true,
      data: newProjectIds,
      message: "数据转换成功",
      // 添加调试信息，帮助追踪转换过程
      debug: {
        originalCount: oldProjects.length,
        mappedCount: mappedProjectNames.length,
        matchedCount: newProjects.length,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
      data: [],
    };
  }
};

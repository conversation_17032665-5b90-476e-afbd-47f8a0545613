const common = require("../../common.js");
const api = require("../../api");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getUnionidToExternalUserid":
      return await exports.getUnionidToExternalUserid(content);
    case "addExternalUserIDAndUnionid":
      return await exports.addExternalUserIDAndUnionid(content);
    case "getUnionidByExternalUserID":
      return await exports.getUnionidByExternalUserID(content);
    case "getExternalUserIDByUnionid":
      return await exports.getExternalUserIDByUnionid(content);
  }
};

// unionid转userid
exports.getUnionidToExternalUserid = async (context) => {
  let { unionid, openid, corpId } = context;
  // 从数据库获取external_userid
  const data = await db
    .collection("unionid-and-externalUserId")
    .findOne({ corpId, unionid });
  if (data) {
    return {
      success: true,
      message: "获取成功",
      data: data.externalUserId,
    };
  }
  let result = await api.getWecomApi({
    type: "unionidToExternalUserid",
    unionid,
    openid,
    corpId,
  });
  if (result.success === false) return result;
  let externalUserId = result.data;
  if (externalUserId) {
    await exports.addExternalUserIDAndUnionid({
      corpId,
      unionid,
      externalUserId,
    });
  }
  return {
    success: true,
    message: "获取成功",
    data: externalUserId,
  };
};

exports.addExternalUserIDAndUnionid = async (context) => {
  let { corpId, unionid, externalUserId, corpUserId } = context;
  if (!unionid || !externalUserId) {
    return {
      success: false,
      message: "unionid或externalUserId不能为空",
    };
  }
  let dbCollection = db.collection("unionid-and-externalUserId");
  const params = {
    corpId,
    unionid,
    externalUserId,
  };
  const total = await dbCollection.countDocuments(params);
  if (total !== 0) {
    await addCorpUserIDAndUnionid(context);
    return {
      success: true,
      message: "已添加",
    };
  } else {
    corpUserId && (params["corpUserId"] = [corpUserId]);
    params["_id"] = common.generateRandomString(24);
    await dbCollection.insertOne(params);
    return {
      success: true,
      message: "添加成功",
    };
  }
};

exports.getUnionidByExternalUserID = async (context) => {
  let { corpId, externalUserId } = context;
  if (!corpId || !externalUserId) return { success: false };
  const item = await db.collection("unionid-and-externalUserId").findOne({
    corpId,
    externalUserId,
  });
  return {
    success: true,
    message: "获取成功",
    data: item,
  };
};

async function addCorpUserIDAndUnionid(context) {
  let { corpId, unionid, externalUserId, corpUserId } = context;
  const params = {
    corpId,
    unionid,
    externalUserId,
    corpUserId: corpUserId,
  };
  const total = await db
    .collection("unionid-and-externalUserId")
    .countDocuments(params);
  if (total === 0) {
    await db.collection("unionid-and-externalUserId").updateOne(
      {
        corpId,
        unionid,
        externalUserId,
      },
      { $push: { corpUserId: corpUserId } }
    );
  }
}

exports.getExternalUserIDByUnionid = async (context) => {
  let { corpId, unionid } = context;
  try {
    const data = await db.collection("unionid-and-externalUserId").findOne({
      corpId,
      unionid,
    });
    const item = data || {};
    return {
      success: true,
      message: "获取成功",
      data: item,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

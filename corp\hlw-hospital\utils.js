const dayjs = require("dayjs");

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 * @returns {boolean} 是否有效
 */
const isValidPhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱
 * @returns {boolean} 是否有效
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证院内工号格式
 * @param {string} workId - 院内工号
 * @returns {boolean} 是否有效
 */
const isValidWorkId = (workId) => {
  // 院内工号可以是数字、字母、中划线的组合，长度3-20位
  const workIdRegex = /^[A-Za-z0-9-]{3,20}$/;
  return workIdRegex.test(workId);
};

/**
 * 格式化日期时间
 * @param {number} timestamp - 时间戳
 * @param {string} format - 格式化字符串
 * @returns {string} 格式化后的日期
 */
const formatDateTime = (timestamp, format = "YYYY-MM-DD HH:mm:ss") => {
  return dayjs(timestamp).format(format);
};

/**
 * 获取当前时间戳
 * @returns {number} 时间戳
 */
const getCurrentTimestamp = () => {
  return new Date().getTime();
};

/**
 * 生成随机排序号
 * @returns {number} 随机排序号
 */
const generateRandomSortOrder = () => {
  return Math.floor(Math.random() * 1000000);
};

/**
 * 清理空值字段
 * @param {Object} obj - 对象
 * @returns {Object} 清理后的对象
 */
const cleanEmptyFields = (obj) => {
  const cleaned = {};
  for (const key in obj) {
    if (obj[key] !== null && obj[key] !== undefined && obj[key] !== '') {
      cleaned[key] = obj[key];
    }
  }
  return cleaned;
};

/**
 * 构建查询条件
 * @param {Object} params - 查询参数
 * @returns {Object} MongoDB查询条件
 */
const buildQueryConditions = (params) => {
  const conditions = {};
  
  // 基础字段
  if (params.corpId) conditions.corpId = params.corpId;
  if (params.workId) conditions.workId = params.workId;
  if (params.gender) conditions.gender = params.gender;
  if (params.dept) conditions.dept = params.dept;
  if (params.job) conditions.job = params.job;
  if (params.title) conditions.title = params.title;
  if (params.outpatientDept) conditions.outpatientDept = params.outpatientDept;
  if (params.recommend !== undefined) conditions.recommend = params.recommend;
  
  // 模糊搜索字段
  if (params.name) {
    conditions.name = { $regex: params.name, $options: "i" };
  }
  if (params.phone) {
    conditions.phone = { $regex: params.phone, $options: "i" };
  }
  if (params.expertise) {
    conditions.expertise = { $regex: params.expertise, $options: "i" };
  }
  
  // 时间范围查询
  if (params.createTimeStart || params.createTimeEnd) {
    conditions.createTime = {};
    if (params.createTimeStart) {
      conditions.createTime.$gte = params.createTimeStart;
    }
    if (params.createTimeEnd) {
      conditions.createTime.$lte = params.createTimeEnd;
    }
  }
  
  // 数组查询
  if (params.depts && Array.isArray(params.depts)) {
    conditions.dept = { $in: params.depts };
  }
  if (params.jobs && Array.isArray(params.jobs)) {
    conditions.job = { $in: params.jobs };
  }
  if (params.titles && Array.isArray(params.titles)) {
    conditions.title = { $in: params.titles };
  }
  
  return conditions;
};

/**
 * 构建排序条件
 * @param {Object} params - 排序参数
 * @returns {Object} MongoDB排序条件
 */
const buildSortConditions = (params) => {
  const sort = {};
  
  if (params.sortBy) {
    const sortOrder = params.sortOrder === 'desc' ? -1 : 1;
    sort[params.sortBy] = sortOrder;
  } else {
    // 默认排序
    sort.sortOrder = 1;
    sort.createTime = -1;
  }
  
  return sort;
};

/**
 * 构建聚合管道
 * @param {Object} params - 查询参数
 * @returns {Array} 聚合管道
 */
const buildAggregationPipeline = (params) => {
  const pipeline = [];
  
  // 匹配条件
  const matchConditions = buildQueryConditions(params);
  if (Object.keys(matchConditions).length > 0) {
    pipeline.push({ $match: matchConditions });
  }
  
  // 排序
  const sortConditions = buildSortConditions(params);
  pipeline.push({ $sort: sortConditions });
  
  // 分页
  if (params.page && params.pageSize) {
    const skip = (params.page - 1) * params.pageSize;
    pipeline.push({ $skip: skip });
    pipeline.push({ $limit: params.pageSize });
  }
  
  return pipeline;
};

/**
 * 验证必填字段
 * @param {Object} data - 数据对象
 * @param {Array} requiredFields - 必填字段数组
 * @returns {Object} 验证结果
 */
const validateRequiredFields = (data, requiredFields) => {
  const missingFields = [];
  
  for (const field of requiredFields) {
    if (!data[field] || data[field] === '') {
      missingFields.push(field);
    }
  }
  
  return {
    isValid: missingFields.length === 0,
    missingFields,
    message: missingFields.length > 0 ? `缺少必填字段: ${missingFields.join(', ')}` : ''
  };
};

/**
 * 验证人员数据
 * @param {Object} data - 人员数据
 * @param {boolean} isUpdate - 是否为更新操作
 * @returns {Object} 验证结果
 */
const validateMemberData = (data, isUpdate = false) => {
  const errors = [];
  
  // 必填字段验证
  if (!isUpdate) {
    const requiredFields = ['corpId', 'name', 'workId'];
    const validation = validateRequiredFields(data, requiredFields);
    if (!validation.isValid) {
      errors.push(validation.message);
    }
  }
  
  // 手机号验证
  if (data.phone && !isValidPhone(data.phone)) {
    errors.push('手机号格式不正确');
  }
  
  // 对外手机号验证
  if (data.publicPhone && !isValidPhone(data.publicPhone)) {
    errors.push('对外手机号格式不正确');
  }
  
  // 院内工号验证
  if (data.workId && !isValidWorkId(data.workId)) {
    errors.push('院内工号格式不正确（3-20位数字、字母、中划线组合）');
  }
  
  // 推荐状态验证
  if (data.recommend !== undefined && ![0, 1].includes(data.recommend)) {
    errors.push('推荐状态只能是0或1');
  }
  
  // 排序号验证
  if (data.sortOrder !== undefined && (isNaN(data.sortOrder) || data.sortOrder < 0)) {
    errors.push('排序号必须是非负数');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    message: errors.join('; ')
  };
};

/**
 * 分页查询辅助函数
 * @param {Object} db - 数据库连接
 * @param {string} collection - 集合名称
 * @param {Object} query - 查询条件
 * @param {Object} options - 查询选项
 * @returns {Object} 分页结果
 */
const paginateQuery = async (db, collection, query, options = {}) => {
  const {
    page = 1,
    pageSize = 10,
    sort = { sortOrder: 1, createTime: -1 },
    projection = {}
  } = options;
  
  const skip = (page - 1) * pageSize;
  
  // 获取总数
  const total = await db.collection(collection).countDocuments(query);
  const pages = Math.ceil(total / pageSize);
  
  // 获取数据
  const data = await db
    .collection(collection)
    .find(query, { projection })
    .sort(sort)
    .skip(skip)
    .limit(pageSize)
    .toArray();
  
  return {
    data,
    total,
    pages,
    page,
    pageSize,
    hasNext: page < pages,
    hasPrev: page > 1
  };
};

/**
 * 批量操作辅助函数
 * @param {Object} db - 数据库连接
 * @param {string} collection - 集合名称
 * @param {Array} operations - 操作数组
 * @returns {Object} 批量操作结果
 */
const bulkOperations = async (db, collection, operations) => {
  if (!Array.isArray(operations) || operations.length === 0) {
    return {
      success: false,
      message: '操作数组不能为空'
    };
  }
  
  try {
    const result = await db.collection(collection).bulkWrite(operations);
    return {
      success: true,
      message: '批量操作成功',
      data: result
    };
  } catch (error) {
    return {
      success: false,
      message: `批量操作失败: ${error.message}`,
      error
    };
  }
};

/**
 * 获取统计信息
 * @param {Object} db - 数据库连接
 * @param {string} collection - 集合名称
 * @param {Object} query - 查询条件
 * @param {string} groupBy - 分组字段
 * @returns {Array} 统计结果
 */
const getStatistics = async (db, collection, query, groupBy) => {
  const pipeline = [
    { $match: query },
    { $group: { _id: `$${groupBy}`, count: { $sum: 1 } } },
    { $sort: { count: -1 } }
  ];
  
  return await db.collection(collection).aggregate(pipeline).toArray();
};

module.exports = {
  isValidPhone,
  isValidEmail,
  isValidWorkId,
  formatDateTime,
  getCurrentTimestamp,
  generateRandomSortOrder,
  cleanEmptyFields,
  buildQueryConditions,
  buildSortConditions,
  buildAggregationPipeline,
  validateRequiredFields,
  validateMemberData,
  paginateQuery,
  bulkOperations,
  getStatistics
};

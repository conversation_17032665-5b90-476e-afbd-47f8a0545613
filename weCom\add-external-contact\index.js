const request = require("../request");
const accessToken = require("../token/index");
const information = require("../externalcontact-info/index");
const api = require("../../api");

//新增
exports.main = async (context) => {
  // 根据userID 获取到机构信息
  const { CorpID, UserID, State, ExternalUserID } = context;
  console.log("获取到的参数", context);
  let result = await api.getCorpApi({
    type: "getCorpMemberByUserId",
    corpId: CorpID,
    userId: UserID,
  });
  console.log("获取到的机构信息", result);

  if (result.data.length > 0) {
    let token = await accessToken.getToken({
      corpId: CorpID,
    });
    if (!token) {
      return {
        success: false,
        message: "获取ACCESS_TOKEN失败",
      };
    }
    let { success, data } = await information.getNameByexternalUserId({
      corpId: CorpID,
      access_token: token,
      externalUserId: ExternalUserID,
    });
    if (!success) {
      return {
        success: false,
        message: "获取外部联系人信息失败",
      };
    }
    const { name } = data && data.external_contact;
    await cacheWechatFriend({ externalUserIdInfo: data, CorpID, UserID });
    let qrcodeId = context.State && context.State.split("&")[1];
    if (qrcodeId) {
      const res = await activityQrcodetoArchives({
        CorpID,
        State,
        name,
        token,
        WelcomeCode: context.WelcomeCode,
        ExternalUserID,
        UserID,
      });
      return res;
    }
  }
};
// 存储添加的好友
async function cacheWechatFriend({ externalUserIdInfo, CorpID, UserID }) {
  const { follow_user, external_contact } = externalUserIdInfo;
  const followInfo = Array.isArray(follow_user)
    ? follow_user.find((item) => item.userid === UserID)
    : {};
  const { createtime, addWay, userid } = followInfo;
  const { type, ...reat } = external_contact;
  console.log("follow_user", followInfo);
  const res = await api.getCorpApi({
    type: "addWechatFriend",
    corpId: CorpID,
    addWay,
    createtime,
    userid,
    friendType: type,
    ...reat,
  });
  console.log("存储微信好友结果", res);
}

async function activityQrcodetoArchives(context) {
  const { CorpID, State, name, token } = context;

  const qrCodeRes = await api.getKnowledgeBaseApi({
    type: "getStaffQrcodeByQrcodeId",
    corpId: CorpID,
    qrcodeId: State,
  });
  if (!qrCodeRes.success) {
    return {
      success: false,
      message: "获取二维码信息失败",
    };
  }
  const qrCodeInfo = qrCodeRes.data;
  if (!qrCodeInfo) return;
  const { configId, qrCodeName } = qrCodeInfo;
  await addFriendRecord(context, configId, name, qrCodeName);
  // 发送欢迎语
  await sendWelocmeMsg(context, qrCodeInfo, token, name);
  // 建档
  if (qrCodeInfo.autoCreateCustomer === true) {
    const archiveRes = await createCustomerArchives(context, name, qrCodeInfo);
    if (!archiveRes && !archiveRes.success) {
      return {
        success: false,
        message: "建档失败",
      };
    }
  }
  return {
    success: true,
    message: "成功",
  };
}
// 建立档案
async function createCustomerArchives(context, name, qrCodeInfo) {
  const { ExternalUserID, CorpID, UserID } = context;
  const { tagIds, customerSource } = qrCodeInfo;
  const params = {
    tagIds,
    customerSource,
  };
  let res = await api.getMemberApi({
    type: "autoCreateMember",
    corpId: CorpID,
    externalUserId: ExternalUserID,
    name,
    params,
    userId: UserID,
  });
  return res;
}
// 记录获取加好友记录
async function addFriendRecord(context, configId, name, qrCodeName) {
  const { CorpID, ExternalUserID, UserID } = context;

  const params = {
    corpId: CorpID,
    configId,
    userId: UserID,
    externalUserId: ExternalUserID,
    qrCodeName,
    name,
  };
  console.log("记录获取加好友记录", params);
  // 获取到teamId
  let res = await api.getKnowledgeBaseApi({
    type: "addQrcodeCustomerCount",
    corpId: CorpID,
    userId: UserID,
    externalUserId: ExternalUserID,
    params,
  });
  return res;
}

async function sendWelocmeMsg(context, qrCodeInfo, ACCESS_TOKEN, nickName) {
  console.log("发送欢迎语", context);
  const { WelcomeCode } = context;
  const { fileList, welcomContent, archiveLink } = qrCodeInfo;
  if (!welcomContent) return;
  let url = `https://qyapi.weixin.qq.com/cgi-bin/externalcontact/send_welcome_msg?access_token=${ACCESS_TOKEN}`;
  let attachments = [];
  if (fileList) {
    attachments = fileList.map((item) => {
      let { type, file, URL } = item;
      const { subtitle, name, url } = file;
      let attachment = {};
      if (type === "image" && URL) {
        attachment = {
          msgtype: type,
          title: name,
          image: {
            pic_url: URL,
          },
        };
      } else if (type === "video" && URL) {
        attachment = {
          msgtype: type,
          title: name,
          video: {
            pic_url: URL,
          },
        };
      } else if (type === "link" && URL) {
        attachment = {
          msgtype: type,
          title: name,
          link: {
            title: name,
            picurl: URL,
            desc: subtitle,
            url,
          },
        };
      }
      return attachment;
    });
  }
  if (archiveLink) {
    attachments.push({
      msgtype: "link",
      title: archiveLink.name,
      link: {
        title: archiveLink.title,
        picurl: archiveLink.picurl || "",
        desc: archiveLink.desc || "",
        url: archiveLink.url,
      },
    });
  }
  let params = {
    welcome_code: WelcomeCode,
    text: {
      content: welcomContent.replaceAll("[微信昵称]", nickName || ""),
    },
    attachments,
  };
  let res = await request.main(url, params, "POST");
  console.log("欢迎语参数", params);
  console.log("发送欢迎语成功", res);
}

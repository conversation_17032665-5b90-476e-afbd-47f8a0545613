const dayjs = require('dayjs');
const request = require('../request');
const corpToken = require('../corp-token');



// 激活账号
exports.activateAccount = async (item, db) => {
  const { corpid, userid, orderType, env } = item;
  const provider_access_token = await corpToken.getProviderAccessToken();
  const { order_list } = await getOrderList(provider_access_token, corpid);
  if (order_list.length === 0) {
    return {
      success: false,
      message: '账号订单为空!',
    };
  }
  const { order_id } = order_list[order_list.length - 1];
  const { baseCodeList, hutongCodeList } = await getCodeList(provider_access_token, order_id, orderType);
  let title = '激活失败';
  if (orderType === 1 && baseCodeList && baseCodeList.length) {
    title = await loopCodeList(baseCodeList, provider_access_token, corpid, userid);
  }
  // orderType 等于2时 激活基础账号和互通账号
  if (orderType === 2 && hutongCodeList && hutongCodeList.length) {
    title = await loopCodeList(hutongCodeList, provider_access_token, corpid, userid);
  }
  return {
    success: true,
    message: title,
    hutongCodeList,
  };
};

// 获取订单列表
async function getOrderList(provider_access_token, corpid) {
  let url = `https://qyapi.weixin.qq.com/cgi-bin/license/list_order?provider_access_token=${provider_access_token}`;
  const params = {
    corpid,
  };
  let res = await request.main(url, params, 'POST');
  return res;
}

// 获取订单中的code列表
async function getCodeList(provider_access_token, order_id, orderType) {
  let url = `https://qyapi.weixin.qq.com/cgi-bin/license/list_order_account?provider_access_token=${provider_access_token}`;
  const params = {
    order_id,
  };
  let { account_list } = await request.main(url, params, 'POST');
  console.log(account_list);
  const baseCodeList = account_list.filter((item) => item.type === 1);
  const hutongCodeList = account_list.filter((item) => item.type === 2);
  if (orderType === 1 && baseCodeList && baseCodeList.length === 0) {
    return {
      success: false,
      message: '基础账号为空!',
    };
  }
  if (orderType === 2 && hutongCodeList && hutongCodeList.length === 0) {
    return {
      success: false,
      message: '互通账号为空!',
    };
  }
  return {
    baseCodeList,
    hutongCodeList,
  };
}

async function loopCodeList(codeList, provider_access_token, corpid, userid) {
  for (let i = 0; i < codeList.length; i++) {
    let { active_code } = codeList[i];
    let { errcode } = await activateAccountByCode(provider_access_token, active_code, corpid, userid);
    if (errcode === 0) {
      return '激活成功';
    }
  }
  return '激活失败';
}

async function activateAccountByCode(provider_access_token, active_code, corpid, userid) {
  let url = `https://qyapi.weixin.qq.com/cgi-bin/license/active_account?provider_access_token=${provider_access_token}`;
  const params = {
    active_code,
    corpid,
    userid,
  };
  let res = await request.main(url, params, 'POST');
  return res;
}

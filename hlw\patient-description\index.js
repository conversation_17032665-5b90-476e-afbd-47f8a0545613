const dayjs = require("dayjs");
const common = require("../../common");
let db = "";

module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "addPatientDescription":
      return await addPatientDescription(item);
    case "deletePatientDescription":
      return await deletePatientDescription(item);
    case "updatePatientDescription":
      return await updatePatientDescription(item);
    case "getPatientDescription":
      return await getPatientDescription(item);
  }
};
// 新增患者病情描述 数据库是 patient-description
/**
 * 
 * @param {*} item 
 * @param {string} item.patientId 患者id
 * @param {string} item.description 描述
 * @param {Array}   item.diseases 疾病描述
 * @param {Array}   item.symptoms 症状描述
 * @returns 
 */
async function addPatientDescription(item) {
  const { patientId, symptoms } = item;
  try {
    // 调用mongo 数据库新增
    const res = await db.collection("patient-description").insertOne({
      patientId,
      description,
      createTime: Date.now(),
    });
    return {
      success: true,
      message: "新增成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: "新增失败",
    };
  }
}

// 删除患者病情描述
async function deletePatientDescription(item) {
  const { patientId } = item;
  try {
    // 调用mongo 数据库删除
    const res = await db.collection("patient-description").deleteOne({
      patientId,
    });
    return {
      success: true,
      message: "删除成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: "删除失败",
    };
  }
}

// 更新患者病情描述

async function updatePatientDescription(item) {
  const { patientId, params } = item;
  try {
    // 调用mongo 数据库更新
    const res = await db
      .collection("patient-description")
      .updateOne(
        { patientId },
        { $set: { ...params, updateTime: Date.now() } }
      );
    return {
      success: true,
      message: "更新成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: "更新失败",
    };
  }
}
// 获取患者病情描述
async function getPatientDescription(item) {
  const { patientId } = item;
  try {
    // 调用mongo 数据库查询
    const res = await db
      .collection("patient-description")
      .findOne({ patientId });
    return {
      success: true,
      message: "查询成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: "查询失败",
    };
  }
}

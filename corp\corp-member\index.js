const corpRole = require("../corp-role");
const common = require("../../common");
const utils = require("../utils");
const dayjs = require("dayjs");
const api = require("../../api");
let db = null;

// 辅助函数：在移除成员后调整其他成员的排序
async function adjustSortOrderAfterRemoval(corpId, removedDeptIds, originalMember) {
  try {
    for (const deptId of removedDeptIds) {
      const removedSortOrder = originalMember.hlwSortOrder?.[deptId];

      if (removedSortOrder && removedSortOrder > 0) {
        console.log(`[DEBUG] 调整科室 ${deptId} 中排序大于 ${removedSortOrder} 的成员排序`);

        // 将该科室中排序号大于被移除成员的其他成员排序号减1
        const adjustResult = await db.collection("corp-member").updateMany(
          {
            corpId,
            hlwDeptIds: deptId,
            [`hlwSortOrder.${deptId}`]: { $gt: removedSortOrder }
          },
          {
            $inc: { [`hlwSortOrder.${deptId}`]: -1 },
            $set: { updateTime: new Date().getTime() }
          }
        );

        console.log(`[DEBUG] 科室 ${deptId} 排序调整完成，影响 ${adjustResult.modifiedCount} 个成员`);
      }
    }
  } catch (error) {
    console.error(`[ERROR] 调整排序时发生错误:`, error);
  }
}

exports.main = async (event, mongodb) => {
  db = mongodb;
  switch (event.type) {
    case "getCorpMember":
      return await exports.getCorpMember(event);
    case "getCorpMemberByUserId":
      return await exports.getCorpMemberByUserId(event);
    case "getCorpMemberData":
      return await exports.getCorpMemberData(event);
    case "getCorpMemberJob":
      return await exports.getCorpMemberJob(event);
    case "getCorpMemberAndCustomorCount":
      return await exports.getCorpMemberAndCustomorCount(event);
    case "getCorpMainMember":
      return await exports.getCorpMainMember(event);
    case "getRolesMemberList":
      return await exports.getRolesMemberList(event);
    case "getSuperAdmin":
      return await exports.getSuperAdmin(event);
    case "addCorpMember":
      return await exports.addCorpMember(event);
    case "updateCorpMember":
      return await exports.updateCorpMember(event);
    case "removeCorpMember":
      return await exports.removeCorpMember(event);
    case "updateCorpMemberByUserId":
      return await exports.updateCorpMemberByUserId(event);
    case "removeAccount":
      return await exports.removeAccount(event);
    case "getNotOpenedAccount":
      return await exports.getNotOpenedAccount(event);
    case "getOpenedAccount":
      return await exports.getOpenedAccount(event);
    case "getCustomMemberInfo":
      return await exports.getCustomMemberInfo(event);
    case "getCorpUsers":
      return await exports.getCorpUsers(event);
    case "getCorpMemberByTeamsAndJobs":
      return await exports.getCorpMemberByTeamsAndJobs(event);
    case "corpMemberExist":
      return await exports.corpMemberExist(event);
    case "checkAdminRole":
      return await exports.checkAdminRole(event);
    case "getCorpMemberHomepageInfo":
      return await exports.getCorpMemberHomepageInfo(event);
    case "addWechatFriend":
      return await exports.addWechatFriend(event);
    case "removeWechatFriend":
      return await exports.removeWechatFriend(event);
    case "getWechatFriends":
      return await getWechatFriends(event);
    case "batchUpdateWechatFriends":
      return await exports.batchUpdateWechatFriends(event);
    case "getExternalUserIdByUserId":
      return await getExternalUserIdByUserId(event);
    case "getCorpMemberByJobs":
      return await exports.getCorpMemberByJobs(event);
    case "getAllCorpMember":
      return await exports.getAllCorpMember(event);
    case "getCorpMemberByHlwDept":
      return await exports.getCorpMemberByHlwDept(event);
    case "addHlwMembers":
      return await exports.addHlwMembers(event);
    case "batchUpdateSortOrder":
      return await exports.batchUpdateSortOrder(event);
    case "getHlwDeptMembersWithSort":
      return await exports.getHlwDeptMembersWithSort(event);
    case "reorderHlwDeptMembers":
      return await exports.reorderHlwDeptMembers(event);
    case "removeHlwMemberFromDept":
      return await exports.removeHlwMemberFromDept(event);
    case "searchHlwMembers":
      return await exports.searchHlwMembers(event);
    case "searchCorpMembers":
      return await exports.searchCorpMembers(event);
    case "searchOpenedAccounts":
      return await exports.searchOpenedAccounts(event);
  }
};

// 获取机
// 获取机构成员通过岗位
exports.getCorpMemberByJobs = async (context) => {
  const { corpId, jobs } = context;
  if (!corpId || !Array.isArray(jobs))
    return { success: false, message: "查询失败" };
  if (jobs.length === 0)
    return { success: true, message: "查询成功", list: [] };
  // 如果岗位是all,则查询所有
  try {
    const data = await db
      .collection("corp-member")
      .find({
        corpId,
        job: { $in: jobs },
      })
      .project({
        userid: 1,
        anotherName: 1,
        memberSource: 1, // 人员来源
        hlwDeptIds: 1, // 所属科室ID数组
        title: 1, // 职称
        recommended: 1, // 推荐状态（1/0）
        hlwSortOrder: 1, // 排序（按科室存储的对象）
        specialty: 1, // 擅长领域
        specialtyFields: 1, // 擅长领域
      })
      .limit(1000)
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.getCorpUsers = async (context) => {
  const { corpId, userIds } = context;
  if (!corpId || !Array.isArray(userIds))
    return { success: false, message: "查询失败" };
  if (userIds.length === 0)
    return { success: true, message: "查询成功", list: [] };
  try {
    const data = await db
      .collection("corp-member")
      .find(
        { corpId, userid: { $in: userIds } },
        {
          projection: {
            anotherName: 1,
            avatar: 1,
            job: 1,
            userid: 1,
            memberSource: 1, // 人员来源
            hlwDeptIds: 1, // 所属科室ID数组
            title: 1, // 职称
            recommended: 1, // 推荐状态（1/0）
            hlwSortOrder: 1, // 排序（按科室存储的对象）
            specialty: 1, // 擅长领域
            specialtyFields: 1, // 擅长领域
          },
        }
      )
      .toArray();
    return { success: true, message: "查询成功", list: data };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 根据获取机构成员
exports.getCorpMember = async (context) => {
  try {
    const { page, pageSize, params } = context;
    let { memberList, ...rest } = params;
    if (memberList) {
      rest["userid"] = { $in: memberList };
    }
    const total = await db.collection("corp-member").countDocuments(rest);
    const pages = Math.ceil(total / pageSize);
    const data = await db
      .collection("corp-member")
      .find(rest, {
        projection: { auth_corp_info: 0 },
      })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data: data,
      total: total,
      pages: pages,
      size: pageSize,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// superAdminName
exports.getSuperAdmin = async (context) => {
  const { corpId, env } = context;
  try {
    const data = await db
      .collection("corp-member")
      .find({
        corpId,
        roleType: "superAdmin",
      })
      .toArray();
    return {
      success: true,
      data,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 更新机构成员
exports.updateCorpMember = async (context) => {
  let { id, params, corpId } = context;
  try {
    params["updateTime"] = new Date().getTime();

    // 检查是否需要特殊处理 hlwDeptIds
    let needsHlwDeptProcessing = params.hasOwnProperty('hlwDeptIds');
    let originalMember = null;
    let removedDeptIds = [];

    if (needsHlwDeptProcessing) {
      // 获取原始成员数据
      let query = { _id: id, corpId };
      originalMember = await db.collection("corp-member").findOne(query);

      if (!originalMember) {
        query = { userid: id, corpId };
        originalMember = await db.collection("corp-member").findOne(query);
      }

      if (originalMember) {
        const originalHlwDeptIds = originalMember.hlwDeptIds || [];
        const newHlwDeptIds = params.hlwDeptIds || [];

        // 找出被移除的科室ID
        removedDeptIds = originalHlwDeptIds.filter(deptId => !newHlwDeptIds.includes(deptId));

        console.log(`[DEBUG] 成员 ${id} hlwDeptIds 更新: 原始=${JSON.stringify(originalHlwDeptIds)}, 新的=${JSON.stringify(newHlwDeptIds)}, 移除=${JSON.stringify(removedDeptIds)}`);

        // 如果有科室被移除，需要清理对应的排序信息
        if (removedDeptIds.length > 0) {
          const updatedHlwSortOrder = { ...originalMember.hlwSortOrder };

          // 移除被删除科室的排序信息
          removedDeptIds.forEach(deptId => {
            delete updatedHlwSortOrder[deptId];
          });

          params.hlwSortOrder = updatedHlwSortOrder;
          console.log(`[DEBUG] 清理排序信息，移除科室: ${JSON.stringify(removedDeptIds)}`);
        }

        // 如果成员被移出所有科室，清除hlw标签
        if (newHlwDeptIds.length === 0 && originalHlwDeptIds.length > 0) {
          params.memberSource = null;
          console.log(`[DEBUG] 成员 ${id} 已从所有科室移除，清除hlw来源标签`);
        }
      }
    }

    // 尝试通过 _id 和 corpId 查询
    let query = { _id: id, corpId };
    let res = await db
      .collection("corp-member")
      .updateOne(query, { $set: params });

    // 如果通过 _id 没有找到，尝试通过 userid 查询
    if (res.matchedCount === 0) {
      query = { userid: id, corpId };
      res = await db
        .collection("corp-member")
        .updateOne(query, { $set: params });
    }

    // 如果有科室被移除，需要重新调整其他成员的排序
    if (needsHlwDeptProcessing && removedDeptIds.length > 0 && originalMember) {
      await adjustSortOrderAfterRemoval(corpId, removedDeptIds, originalMember);
    }

    if (params.job) {
      await api.getMemberApi({ corpId, type: "updateSopTaskExecuteUser" });
    }

    return {
      success: true,
      message: res.matchedCount > 0 ? "更新成功" : "未找到匹配的成员记录",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || error,
    };
  }
};

// 删除机构成员
exports.removeCorpMember = async (context) => {
  let { id, corpId, env } = context;
  try {
    const res = await db.collection("corp-member").deleteOne({
      _id: id,
      corpId,
    });
    return {
      success: true,
      message: "删除成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

// 新增机构成员
exports.addCorpMember = async (context) => {
  let { params, env } = context;
  const { corpId, open_userid } = params;
  try {
    if (open_userid) {
      const flag = await corpLimitation(corpId, env);
      if (!flag) {
        return {
          success: false,
          message: "机构成员数已达上限!",
        };
      }
    }
    params["createTime"] = new Date().getTime();
    params._id = common.generateRandomString(24);
    const res = await db.collection("corp-member").insertOne(params);
    if (params.job) {
      await api.getMemberApi({ corpId, type: "updateSopTaskExecuteUser" });
    }
    return {
      success: true,
      message: "新增成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

async function corpLimitation(corpId, env) {
  const corpMemberCount = await db.collection("corp-member").countDocuments({
    corpId,
    open_userid: { $exists: true },
  });
  const corp = await db.collection("corp").findOne({ corpId });
  const { package } = corp;
  if (!package) return true;
  const { accountCount, giveAccountCount } = package;
  const allAccountCount = accountCount + giveAccountCount;
  return corpMemberCount <= allAccountCount;
}

// 更新机构成员通过OpenUserID
exports.updateCorpMemberByUserId = async (context) => {
  let { userid, params, env, corpId } = context;
  try {
    params["updateTime"] = new Date().getTime();
    const res = await db.collection("corp-member").updateOne(
      {
        userid,
        corpId,
      },
      { $set: params }
    );
    return {
      success: true,
      message: "更新成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

exports.getCorpMainMember = async (context) => {
  let { corpId, env } = context;
  try {
    const mainRole = await db.collection("sys-role").findOne({
      corpId,
      roleId: "main",
    });
    const member = await db.collection("corp-member").findOne({
      corpId,
      roleIds: mainRole._id,
    });
    return {
      success: true,
      message: "获取成功",
      data: member,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

exports.getRolesMemberList = async (context) => {
  let { corpId, env } = context;
  try {
    const res = await db
      .collection("sys-role")
      .aggregate([
        {
          $lookup: {
            from: "corp-member",
            localField: "_id",
            foreignField: "roleIds",
            as: "roleEmployees",
          },
        },
        {
          $match: {
            corpId: corpId,
          },
        },
      ])
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.getCorpMemberByUserId = async (context) => {
  let { corpId, userId, params = {} } = context;
  // 支持两种参数传递方式：直接传递或通过params对象传递
  const finalUserId = userId || params.userId;
  console.log("获取用户信息", context);
  console.log("最终使用的userId:", finalUserId);
  try {
    const data = await db
      .collection("corp-member")
      .find({
        corpId,
        userid: finalUserId,
      })
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.getCorpMemberAndCustomorCount = async (item) => {
  const { corpId, env } = item;
  try {
    const customtorCount = await db.collection("member").countDocuments({
      corpId,
    });
    const corpMemberCount = await db.collection("corp-member").countDocuments({
      corpId,
      open_userid: { $exists: true },
    });
    return {
      success: true,
      message: "获取成功",
      customtorCount,
      corpMemberCount,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 删除账号
exports.removeAccount = async (item) => {
  const { corpId, userId, env } = item;
  try {
    if (!userId)
      return {
        message: "账号不能为空",
        success: false,
      };
    if (!corpId)
      return {
        message: "机构ID不能为空",
        success: false,
      };
    // 删除账号
    await db.collection("corp-member").deleteMany({ corpId, userid: userId });
    // 删除团队
    await db.collection("team").updateMany(
      { corpId, memberList: userId },
      {
        $pull: {
          memberLeaderList: userId,
          friendlyMembers: userId,
          memberList: userId,
        },
      }
    );
    return {
      message: "删除成功",
      success: true,
    };
  } catch (error) {
    return {
      message: "删除失败",
      success: false,
    };
  }
};

// 获取未开通的账户
exports.getNotOpenedAccount = async (item) => {
  const { corpId } = item;
  try {
    const fetchData = async (page, pageSize, db) => {
      const data = await db
        .collection("corp-member")
        .find({
          corpId,
          open_userid: { $exists: false },
        })
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .toArray();
      return data;
    };
    const list = await utils.getAllData(fetchData, db);
    return {
      success: true,
      message: "获取成功!",
      data: list,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 获取机构所有员工
exports.getAllCorpMember = async (item) => {
  const { corpId } = item;
  try {
    const fetchData = async (page, pageSize, db) => {
      const data = await db
        .collection("corp-member")
        .find({ corpId })
        .skip((page - 1) * pageSize)
        .project({
          userid: 1,
          anotherName: 1,
          job: 1,
          deptIds: 1,
          memberSource: 1, // 人员来源
          hlwDeptIds: 1, // 所属科室ID数组
          title: 1, // 职称
          recommended: 1, // 推荐状态（1/0）
          hlwSortOrder: 1, // 排序（按科室存储的对象）
          specialty: 1, // 擅长领域
          specialtyFields: 1, // 擅长领域
        })
        .limit(pageSize)
        .toArray();
      return data;
    };
    const list = await utils.getAllData(fetchData, db);
    return {
      success: true,
      message: "获取成功!",
      data: list,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};
// 获取已开通的账户
exports.getOpenedAccount = async (item) => {
  const { corpId } = item;
  try {
    const fetchData = async (page, pageSize, db) => {
      const data = await db
        .collection("corp-member")
        .find({ corpId, open_userid: { $exists: true } })
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .toArray();
      return data;
    };
    const list = await utils.getAllData(fetchData, db);
    return {
      success: true,
      message: "获取成功!",
      data: list,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 获取机构成员的岗位信息
exports.getCorpMemberJob = async (item) => {
  const { env } = item;
  try {
    const data = await db.collection("sys-corp-member-job").find().toArray();
    return {
      success: true,
      message: "获取成功!",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

// 获取机构成员数据
exports.getCorpMemberData = async (event) => {
  const { userId, corpId } = event;
  console.log("获取用户信息", event);
  try {
    const member = await db
      .collection("corp-member")
      .findOne({ userid: userId, corpId });
    if (!member) {
      return {
        success: false,
        message: "用户不存在",
      };
    }
    // 获取角色信息
    member["teamId"] = await getTeamListByuserId(event);
    return {
      success: true,
      message: "获取成功",
      data: member,
    };
  } catch (error) {
    return {
      success: false,
      message: error,
    };
  }
};

// 获取用户所在的团队列表
async function getTeamListByuserId(event) {
  const { userId, corpId, env } = event;
  const data = await db
    .collection("team")
    .find({ corpId, memberList: userId })
    .toArray();
  return data.map((item) => item.teamId);
}

// 获取自定义成员信息
exports.getCustomMemberInfo = async (context) => {
  const { id: _id, userid, corpId, fields } = context;
  if (
    !_id ||
    !userid ||
    !corpId ||
    !Array.isArray(fields) ||
    fields.length === 0
  ) {
    return { success: false, message: "参数错误" };
  }
  try {
    const project = fields.reduce((acc, cur) => ({ ...acc, [cur]: 1 }), {});
    const query = db
      .collection("corp-member")
      .aggregate()
      .match({ _id, userid, corpId });
    if (fields.includes("job")) {
      query.lookup({
        from: "sys-corp-member-job",
        localField: "job",
        foreignField: "value",
        as: "memberJob",
      });
      project.memberJob = 1;
    }
    // 添加科室信息关联查询
    if (fields.includes("hlwDeptIds")) {
      query.lookup({
        from: "hlw-dept-list",
        localField: "hlwDeptIds",
        foreignField: "_id",
        as: "hlwDeptInfo",
      });
      project.hlwDeptInfo = 1;
    }
    const member = await query.project(project).toArray();
    return {
      success: true,
      message: "获取成功!",
      data: member[0],
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

// 根据团队和岗位获取到成员
exports.getCorpMemberByTeamsAndJobs = async (event) => {
  const { teamIds, corpId, jobs } = event;
  if (!Array.isArray(teamIds) || !corpId || !Array.isArray(jobs)) {
    return {
      success: false,
      message: "参数错误",
    };
  }
  const teams = await db
    .collection("team")
    .find({ teamId: { $in: teamIds }, corpId })
    .toArray();
  let userIds = teams.reduce((acc, cur) => acc.concat(cur.memberList), []);
  if (!jobs.includes("all")) {
    const res = await db
      .collection("corp-member")
      .find({
        corpId,
        job: { $in: jobs },
        accountState: { $ne: "disabled" },
        open_userid: { $exists: true },
        userid: { $in: userIds },
      })
      .project({ userid: 1, name: 1, job: 1, teamId: 1 })
      .toArray();
    userIds = res.map((item) => item.userid);
  }
  return {
    success: true,
    message: "获取成功",
    data: userIds,
  };
};

// 检查机构成员是否存在
exports.corpMemberExist = async (event) => {
  const { userid, corpId } = event;
  if (!userid || !corpId) {
    return { success: false, message: "参数错误" };
  }
  const total = await db
    .collection("corp-member")
    .countDocuments({ userid, corpId });
  return {
    success: true,
    exist: total > 0,
  };
};

// 检查用户是否有管理员角色
exports.checkAdminRole = async (ctx) => {
  const { userid, corpId } = ctx;
  if (!userid || !corpId) {
    return { success: false, message: "参数错误" };
  }
  try {
    const user = await db
      .collection("corp-member")
      .findOne({ userid, corpId }, { projection: { roleIds: 1 } });
    if (!user) {
      return { success: false, message: "用户不存在" };
    }
    const ids = Array.isArray(user.roleIds) ? user.roleIds : [];
    if (ids.length === 0) {
      return { success: false, message: "用户没有角色" };
    }
    const res = await corpRole.main(
      {
        type: "checkRoleExistByIds",
        corpId,
        ids,
        roleId: "admin",
      },
      db
    );
    const { success, message, exist = false } = res;
    return { success, message, exist };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

// 获取机构成员主页信息
exports.getCorpMemberHomepageInfo = async (ctx) => {
  const { userid, corpId } = ctx;
  if (!userid || !corpId) {
    return { success: false, message: "参数错误" };
  }
  try {
    // const user = await db
    //   .collection("corp-member")
    //   .aggregate()
    //   .match({ userid, corpId })
    //   .lookup({
    //     from: "corp",
    //     localField: "corpId",
    //     foreignField: "corpId",
    //     as: "corps",
    //   })
    //   .addFields({
    //     corpNames: {
    //       $map: { input: "$corps", as: "corp", in: "$$corp.corp_name" },
    //     },
    //   })
    //   .lookup({
    //     from: "dept-list",
    //     localField: "deptIds",
    //     foreignField: "_id",
    //     as: "depts",
    //   })
    //   .addFields({
    //     deptNames: {
    //       $map: { input: "$depts", as: "dept", in: "$$dept.deptName" },
    //     },
    //   })
    //   .project({
    //     anotherName: 1,
    //     avatar: 1,
    //     callNumber: 1,
    //     convenienceService: 1,
    //     outpatientTime: 1,
    //     corpNames: 1,
    //     deptNames: 1,
    //     memberTroduce: 1,
    //     job: 1,
    //   })
    //   .limit(1)
    //   .toArray();
    const user = await db
      .collection("corp-member")
      .aggregate([
        { $match: { userid, corpId } },
        {
          $lookup: {
            // 第二阶段：与 'corp' 集合进行关联
            from: "corp",
            localField: "corpId",
            foreignField: "corpId",
            as: "corps",
          },
        },
        {
          $addFields: {
            // 第三阶段：添加字段，提取 corp_names
            corpNames: {
              $map: {
                input: "$corps", // 遍历 'corps' 数组
                as: "corp", // 每个元素的别名
                in: "$$corp.corp_name", // 提取 'corp_name' 字段
              },
            },
          },
        },
        {
          $lookup: {
            // 第四阶段：与 'dept-list' 集合进行关联
            from: "dept-list",
            localField: "deptIds",
            foreignField: "_id",
            as: "depts",
          },
        },
        {
          $addFields: {
            // 第五阶段：添加字段，提取 dept_names
            deptNames: {
              $map: {
                input: "$depts", // 遍历 'depts' 数组
                as: "dept", // 每个元素的别名
                in: "$$dept.deptName", // 提取 'deptName' 字段
              },
            },
          },
        },
        {
          $project: {
            // 第六阶段：选择返回的字段
            anotherName: 1,
            avatar: 1,
            callNumber: 1,
            convenienceService: 1,
            outpatientTime: 1,
            corpNames: 1, // 包含 'corpNames' 字段
            deptNames: 1, // 包含 'deptNames' 字段
            memberTroduce: 1,
            job: 1,
          },
        },
        { $limit: 1 },
      ])
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data: user[0],
    };
  } catch (e) {
    return {
      success: false,
      message: e.message || "获取失败",
    };
  }
};
/**
 *
 * @param {*} param0
 */
async function getExternalUserIdByUserId({
  corpId,
  userId,
  externalUserIds,
  page = 1,
  pageSize = 500,
}) {
  if (!corpId || !userId || !Array.isArray(externalUserIds)) {
    return { success: false, message: "参数错误" };
  }
  try {
    const data = await db
      .collection("wechat-friends")
      .find({
        corpId,
        userid: userId,
        external_userid: { $in: externalUserIds },
      })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();

    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
}

// 获取微信好友
async function getWechatFriends(item) {
  const {
    corpId,
    userId,
    externalUserId,
    dates,
    page = 1,
    pageSize = 10,
    haveCustomer = false,
  } = item;
  let query = { corpId };
  if (userId) query.userid = userId;
  if (externalUserId) query.external_userid = externalUserId;
  if (Array.isArray(dates) && dates.length > 0) {
    query.createtime = {
      $gte: dayjs(dates[0]).startOf("day").unix(),
      $lte: dayjs(dates[1]).endOf("day").unix(),
    };
  }
  try {
    const total = await db.collection("wechat-friends").countDocuments(query);
    const pages = Math.ceil(total / pageSize);
    // 排序
    const aggregateList = [
      { $match: query },
      { $sort: { createtime: -1 } },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize },
    ];
    const data = await db
      .collection("wechat-friends")
      .aggregate(aggregateList)
      .toArray();
    const externalUserIds = data.map((item) => item.external_userid);
    if (haveCustomer && externalUserIds.length) {
      const res = await api.getMemberApi({
        corpId,
        type: "searchCorpCustomer",
        externalUserIds,
        page: 1,
        pageSize: 10 * externalUserIds.length,
      });
      const customers =
        res && Array.isArray(res.list)
          ? res.list.map((i) => ({
              name: i.name,
              customerStage: i.customerStage,
              _id: i._id,
              externalUserId: i.externalUserId,
            }))
          : [];
      return {
        success: true,
        message: "获取成功",
        list: data,
        customers,
        total,
        pages,
      };
    }

    return {
      success: true,
      message: "获取成功",
      list: data,
      total,
      pages,
    };
  } catch {
    return {
      success: false,
      message: "获取失败",
    };
  }
}

// 添加机构成员微信好友
exports.addWechatFriend = async (item) => {
  try {
    if (!item.corpId) {
      return {
        success: false,
        message: "机构ID不存在",
      };
    }
    const {
      corpId,
      external_userid,
      friendType,
      gender,
      name,
      corp_name,
      createtime,
      addWay,
      userid,
    } = item;

    const res = await db.collection("wechat-friends").insertOne({
      corpId,
      external_userid,
      friendType,
      gender,
      name,
      corp_name,
      createtime,
      addWay,
      userid,
      _id: common.generateRandomString(24),
    });
    return {
      success: true,
      message: "新增成功",
      data: res,
    };
  } catch {
    return {
      success: false,
      message: "新增失败",
    };
  }
};

// 批量更新 wechat-friends 表数据 使用批量插入
exports.batchUpdateWechatFriends = async (item) => {
  const { corpId, data } = item;
  try {
    const bulkOps = data.map((item) => ({
      updateOne: {
        filter: {
          corpId,
          external_userid: item.external_userid,
          userid: item.userid,
        },
        update: { $set: { ...item, _id: common.generateRandomString(24) } },
        upsert: true,
      },
    }));
    await db.collection("wechat-friends").bulkWrite(bulkOps);
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "更新失败",
      error: error.message,
    };
  }
};

// 删除机构成员微信好友
exports.removeWechatFriend = async (item) => {
  const { userid, external_userid, corpId } = item;
  try {
    const res = await db.collection("wechat-friends").deleteMany({
      userid,
      external_userid,
      corpId,
    });
    return {
      success: true,
      message: "删除成功",
    };
  } catch {
    return {
      success: false,
      message: "删除失败",
    };
  }
};

// 根据hlw科室获取成员
exports.getCorpMemberByHlwDept = async (context) => {
  const { corpId, hlwDeptIds, hlwDeptBusinessIds, page = 1, pageSize = 10 } = context;
  if (!corpId) {
    return { success: false, message: "机构ID不能为空" };
  }

  // 支持两种查询方式：通过_id或通过业务deptId
  let finalHlwDeptIds = [];

  if (hlwDeptIds && Array.isArray(hlwDeptIds) && hlwDeptIds.length > 0) {
    // 使用传统的_id查询
    finalHlwDeptIds = hlwDeptIds;
  } else if (hlwDeptBusinessIds && Array.isArray(hlwDeptBusinessIds) && hlwDeptBusinessIds.length > 0) {
    // 通过业务deptId查询，先获取对应的_id
    const deptQuery = await db.collection("hlw-dept-list").find({
      corpId,
      hlw_dept_id: { $in: hlwDeptBusinessIds }
    }).project({ _id: 1 }).toArray();

    finalHlwDeptIds = deptQuery.map(dept => dept._id);

    if (finalHlwDeptIds.length === 0) {
      return { success: true, message: "未找到匹配的科室", data: [], total: 0, pages: 0, size: pageSize };
    }
  } else {
    return { success: false, message: "请提供hlwDeptIds或hlwDeptBusinessIds参数" };
  }

  try {
    // 需要处理数据不匹配的情况：
    // 人员数据中可能存储的是业务科室ID，而查询使用的是MongoDB _id

    let businessDeptIds = [];

    // 对于每个MongoDB _id，尝试获取对应的业务ID
    for (const deptId of finalHlwDeptIds) {
      if (deptId.length === 24) { // MongoDB ObjectId 长度为24
        const dept = await db.collection("hlw-dept-list").findOne({
          _id: deptId,
          corpId
        }, { projection: { hlw_dept_id: 1 } });

        if (dept && dept.hlw_dept_id) {
          businessDeptIds.push(dept.hlw_dept_id);
        }
      }
    }

    // 构建查询条件，同时支持业务ID和MongoDB _id
    const allDeptIds = [...finalHlwDeptIds, ...businessDeptIds];

    const query = {
      corpId,
      hlwDeptIds: { $in: allDeptIds },
    };


    const total = await db.collection("corp-member").countDocuments(query);
    const pages = Math.ceil(total / pageSize);
    
    const data = await db
      .collection("corp-member")
      .find(query)
      .project({
        userid: 1,
        anotherName: 1,
        avatar: 1,
        job: 1,
        hlwDeptIds: 1,
        title: 1,
        recommended: 1,
        hlwSortOrder: 1,
        specialty: 1,
        specialtyFields: 1,
        memberSource: 1,
        gender: 1,           // 性别
        mobile: 1,           // 手机
        callNumber: 1,       // 对外手机
        workNo: 1,           // 工号
        memberTroduce: 1,    // 个人介绍
        outpatientTime: 1,   // 门诊时间
        convenienceService: 1, // 便民服务
        hlwDepartment: 1, // 部门信息
        deptIds: 1,
        doctorNo: 1,      // 医生编号
      })
      .sort({ "hlwSortOrder.sortValue": 1, createTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    
    return {
      success: true,
      message: "获取成功",
      data,
      total,
      pages,
      size: pageSize,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

// 互联网批量添加人员接口（改为批量更新）
exports.addHlwMembers = async (context) => {
  const { corpId, members } = context;
  if (!corpId || !Array.isArray(members) || members.length === 0) {
    return { success: false, message: "参数错误" };
  }

  // 检查每个成员都必须有 hlwDeptId 和 userid
  for (const member of members) {
    if (!member.hlwDeptId || !member.userid) {
      return { success: false, message: "每个成员必须包含 hlwDeptId 和 userid" };
    }
  }

  try {
    const currentTime = new Date().getTime();
    // 统计每个科室当前最大排序
    const deptIds = [...new Set(members.map(m => m.hlwDeptId))];
    const maxSortOrders = {};
    for (const deptId of deptIds) {
      // 检查科室中实际的成员数量
      const actualMemberCount = await db
        .collection("corp-member")
        .countDocuments({
          corpId,
          hlwDeptIds: deptId
        });

      const maxSortMember = await db
        .collection("corp-member")
        .findOne(
          {
            corpId,
            [`hlwSortOrder.${deptId}`]: { $exists: true }
          },
          {
            sort: { [`hlwSortOrder.${deptId}`]: -1 },
            projection: { hlwSortOrder: 1 }
          }
        );
      const maxSort = maxSortMember?.hlwSortOrder?.[deptId] || 0;
      maxSortOrders[deptId] = maxSort;

      console.log(`[DEBUG] 科室 ${deptId} 实际成员数量: ${actualMemberCount}, 当前最大排序: ${maxSort}`);
      console.log(`[DEBUG] 科室 ${deptId} maxSortMember:`, maxSortMember ? JSON.stringify(maxSortMember.hlwSortOrder) : 'null');

      // 如果科室实际没有成员但有排序数据，说明数据不一致
      if (actualMemberCount === 0 && maxSort > 0) {
        console.log(`[WARNING] 科室 ${deptId} 数据不一致：无成员但有排序数据，重置最大排序为0`);
        maxSortOrders[deptId] = 0;
      }
    }

    let updatedCount = 0;
    const updatedMembers = [];
    for (const member of members) {
      const { userid, hlwDeptId, hlwDeptBusinessId, sortOrder, recommended, title, specialty, doctorNo } = member;

      // 支持两种科室ID方式，统一使用业务ID
      let finalHlwDeptId = hlwDeptId;
      let businessDeptId = null;

      if (!hlwDeptId && hlwDeptBusinessId) {
        // 通过业务deptId查询，先获取对应的_id
        const dept = await db.collection("hlw-dept-list").findOne({
          corpId,
          hlw_dept_id: hlwDeptBusinessId
        }, { projection: { _id: 1, hlw_dept_id: 1 } });

        if (!dept) {
          console.warn(`未找到科室业务ID: ${hlwDeptBusinessId}`);
          continue; // 跳过无效的科室ID
        }

        finalHlwDeptId = dept._id;
        businessDeptId = dept.hlw_dept_id;
      } else if (!hlwDeptId) {
        console.warn(`成员 ${userid} 缺少科室ID参数`);
        continue; // 跳过缺少科室ID的成员
      }

      // 如果传入的是MongoDB _id，获取对应的业务ID
      if (finalHlwDeptId.length >= 24 && !businessDeptId) {
        const dept = await db.collection("hlw-dept-list").findOne({
          _id: finalHlwDeptId,
          corpId
        }, { projection: { hlw_dept_id: 1 } });

        if (dept && dept.hlw_dept_id) {
          businessDeptId = dept.hlw_dept_id;
        }
      }

      // 查询原有成员
      const old = await db.collection("corp-member").findOne({ corpId, userid });
      if (!old) continue; // 跳过不存在的成员
      // 处理 hlwDeptIds
      let hlwDeptIds = Array.isArray(old.hlwDeptIds) ? old.hlwDeptIds.slice() : [];
      const isAlreadyInDept = hlwDeptIds.includes(finalHlwDeptId);
      if (!isAlreadyInDept) hlwDeptIds.push(finalHlwDeptId);

      // 处理 hlwSortOrder
      let finalSortOrder = sortOrder;
      let hlwSortOrder = Object.assign({}, old.hlwSortOrder || {});

      if (isAlreadyInDept) {
        // 如果成员已经在科室中，跳过不处理
        console.log(`成员 ${userid} 已在科室 ${finalHlwDeptId} 中，跳过重复添加`);
        continue; // 跳过这个成员，不进行任何更新
      } else {
        // 新增成员到科室，分配新的排序号
        if (finalSortOrder === undefined || finalSortOrder === null || finalSortOrder === 1) {
          // 如果没有指定排序或者是默认值1，则自动分配下一个排序号
          console.log(`[DEBUG] 自动分配排序前: maxSortOrders[${finalHlwDeptId}] = ${maxSortOrders[finalHlwDeptId]}`);
          maxSortOrders[finalHlwDeptId] += 1;
          finalSortOrder = maxSortOrders[finalHlwDeptId];
          console.log(`[DEBUG] 自动分配排序后: finalSortOrder = ${finalSortOrder}`);
        } else {
          // 如果指定了具体的排序号，更新最大排序号以避免冲突
          console.log(`[DEBUG] 使用指定排序: ${finalSortOrder}`);
          if (finalSortOrder > maxSortOrders[finalHlwDeptId]) {
            maxSortOrders[finalHlwDeptId] = finalSortOrder;
          }
        }
        hlwSortOrder[finalHlwDeptId] = finalSortOrder;
        console.log(`成员 ${userid} 新增到科室 ${finalHlwDeptId}，分配排序: ${finalSortOrder}`);
      }
      // 构建更新对象，只更新明确传入的字段
      const updateFields = {
        hlwDeptIds,
        memberSource: "hlw", // 添加hlw来源标签
        updateTime: currentTime
      };

      // 新增成员，直接设置排序字段
      updateFields[`hlwSortOrder.${finalHlwDeptId}`] = finalSortOrder;

      // 只在明确传入值时才更新这些字段，避免覆盖现有数据
      if (recommended !== undefined && recommended !== null) {
        updateFields.recommended = recommended;
      }
      if (title !== undefined && title !== null) {
        updateFields.title = title;
      }
      if (specialty !== undefined && specialty !== null) {
        updateFields.specialty = specialty;
      }
      if (doctorNo !== undefined && doctorNo !== null) {
        updateFields.doctorNo = doctorNo;
      }

      // 更新
      const updateRes = await db.collection("corp-member").updateOne(
        { corpId, userid },
        { $set: updateFields }
      );
      if (updateRes.modifiedCount > 0) {
        updatedCount++;
        updatedMembers.push({
          userid,
          anotherName: old.anotherName,
          hlwDeptIds,
          hlwSortOrder,
          memberSource: "hlw"
        });
      }
    }
    return {
      success: true,
      message: `成功更新 ${updatedCount} 名成员`,
      data: {
        updatedCount,
        members: updatedMembers
      }
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "批量添加失败",
    };
  }
};

// 批量更新排序功能
exports.batchUpdateSortOrder = async (context) => {
  const { corpId, updates } = context;
  if (!corpId || !Array.isArray(updates) || updates.length === 0) {
    return { success: false, message: "参数错误" };
  }
  
  try {
    const bulkOps = [];
    const currentTime = new Date().getTime();
    
    for (const update of updates) {
      const { userid, hlwDeptId, hlwDeptBusinessId, sortOrder } = update;

      if (!userid || sortOrder === undefined) {
        continue; // 跳过无效的更新项
      }

      // 支持两种科室ID方式，处理数据不匹配问题
      let finalHlwDeptId = hlwDeptId;
      let businessDeptId = null;

      if (!hlwDeptId && hlwDeptBusinessId) {
        // 通过业务deptId查询，先获取对应的_id
        const dept = await db.collection("hlw-dept-list").findOne({
          corpId,
          hlw_dept_id: hlwDeptBusinessId
        }, { projection: { _id: 1 } });

        if (!dept) {
          console.warn(`未找到科室业务ID: ${hlwDeptBusinessId}`);
          continue; // 跳过无效的科室ID
        }

        finalHlwDeptId = dept._id;
      } else if (!hlwDeptId) {
        console.warn(`更新项缺少科室ID参数: ${JSON.stringify(update)}`);
        continue; // 跳过缺少科室ID的更新项
      }

      // 如果传入的是MongoDB _id，获取对应的业务ID
      if (finalHlwDeptId.length >= 24) {
        const dept = await db.collection("hlw-dept-list").findOne({
          _id: finalHlwDeptId,
          corpId
        }, { projection: { hlw_dept_id: 1 } });

        if (dept && dept.hlw_dept_id) {
          businessDeptId = dept.hlw_dept_id;
        }
      }

      // 确定要使用的排序键 - 优先使用业务ID
      const sortKey = businessDeptId || finalHlwDeptId;

      // 构建更新操作，同时清理可能存在的重复排序键
      const updateFields = {
        [`hlwSortOrder.${sortKey}`]: sortOrder,
        updateTime: currentTime
      };

      const unsetFields = {};

      // 如果使用业务ID作为排序键，清理可能存在的MongoDB _id排序键
      if (businessDeptId && businessDeptId !== finalHlwDeptId) {
        unsetFields[`hlwSortOrder.${finalHlwDeptId}`] = "";
      }

      const updateOperation = { $set: updateFields };
      if (Object.keys(unsetFields).length > 0) {
        updateOperation['$unset'] = unsetFields;
      }

      bulkOps.push({
        updateOne: {
          filter: { corpId, userid },
          update: updateOperation
        }
      });
    }
    
    if (bulkOps.length === 0) {
      return { success: false, message: "没有有效的更新数据" };
    }
    
    const result = await db.collection("corp-member").bulkWrite(bulkOps);
    
    return {
      success: true,
      message: `成功更新 ${result.modifiedCount} 名成员的排序`,
      data: {
        matchedCount: result.matchedCount,
        modifiedCount: result.modifiedCount,
        upsertedCount: result.upsertedCount,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "批量更新排序失败",
    };
  }
};

// 获取科室成员并按排序返回（用于验证排序是否正确）
exports.getHlwDeptMembersWithSort = async (context) => {
  const { corpId, hlwDeptId, hlwDeptBusinessId, page = 1, pageSize = 20 } = context;
  if (!corpId) {
    return { success: false, message: "机构ID不能为空" };
  }

  // 支持两种查询方式：通过_id或通过业务deptId
  let finalHlwDeptId = hlwDeptId;

  if (!hlwDeptId && hlwDeptBusinessId) {
    // 通过业务deptId查询，先获取对应的_id
    const dept = await db.collection("hlw-dept-list").findOne({
      corpId,
      hlw_dept_id: hlwDeptBusinessId
    }, { projection: { _id: 1 } });

    if (!dept) {
      return { success: false, message: "未找到匹配的科室" };
    }

    finalHlwDeptId = dept._id;
  } else if (!hlwDeptId) {
    return { success: false, message: "请提供hlwDeptId或hlwDeptBusinessId参数" };
  }

  try {
    // 需要处理两种情况：
    // 1. 人员数据中存储的是业务科室ID (hlw_dept_id)
    // 2. 人员数据中存储的是MongoDB _id

    let query;
    let businessDeptId = null;

    // 如果传入的是MongoDB _id，需要先获取对应的业务ID
    if (finalHlwDeptId.length >= 24) { // MongoDB ObjectId 长度为24或更长
      const dept = await db.collection("hlw-dept-list").findOne({
        _id: finalHlwDeptId,
        corpId
      }, { projection: { hlw_dept_id: 1 } });

      if (dept && dept.hlw_dept_id) {
        businessDeptId = dept.hlw_dept_id;
      }
    }

    // 构建查询条件，同时支持业务ID和MongoDB _id
    if (businessDeptId) {
      query = {
        corpId,
        $or: [
          { hlwDeptIds: { $in: [finalHlwDeptId] } },      // MongoDB _id
          { hlwDeptIds: { $in: [businessDeptId] } }       // 业务ID
        ]
      };
    } else {
      query = {
        corpId,
        hlwDeptIds: { $in: [finalHlwDeptId] }
      };
    }

    const total = await db.collection("corp-member").countDocuments(query);
    const pages = Math.ceil(total / pageSize);
    
    const data = await db
      .collection("corp-member")
      .find(query)
      .project({
        userid: 1,
        anotherName: 1,
        avatar: 1,
        job: 1,
        hlwDeptIds: 1,
        title: 1,
        recommended: 1,
        hlwSortOrder: 1,
        specialty: 1,
        specialtyFields: 1,
        memberSource: 1,
        createTime: 1,
        gender: 1,           // 性别
        mobile: 1,           // 手机
        callNumber: 1,       // 对外手机
        workNo: 1,           // 工号
        memberTroduce: 1,    // 个人介绍
        outpatientTime: 1,   // 门诊时间
        convenienceService: 1, // 便民服务
        hlwDepartment: 1, // 部门信息
        deptIds:1,
        doctorNo: 1,      // 医生编号
      })
      .sort({
        [`hlwSortOrder.${businessDeptId}`]: 1,
        [`hlwSortOrder.${finalHlwDeptId}`]: 1,
        createTime: -1
      })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();

    // 添加当前科室的排序值到每个成员对象中，方便前端显示
    const dataWithSort = data.map(member => ({
      ...member,
      currentSortOrder: member.hlwSortOrder?.[businessDeptId] ||member.hlwSortOrder?.[finalHlwDeptId] ||0
    }));
    
    return {
      success: true,
      message: "获取成功",
      data: dataWithSort,
      total,
      pages,
      size: pageSize,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "获取失败",
    };
  }
};

// 重新排序科室成员（将成员按顺序重新编号）
exports.reorderHlwDeptMembers = async (context) => {
  const { corpId, hlwDeptId, hlwDeptBusinessId, memberIds } = context;
  if (!corpId || !Array.isArray(memberIds) || memberIds.length === 0) {
    return { success: false, message: "参数错误" };
  }

  // 支持两种科室ID方式
  let finalHlwDeptId = hlwDeptId;
  if (!hlwDeptId && hlwDeptBusinessId) {
    // 通过业务deptId查询，先获取对应的_id
    const dept = await db.collection("hlw-dept-list").findOne({
      corpId,
      hlw_dept_id: hlwDeptBusinessId
    }, { projection: { _id: 1 } });

    if (!dept) {
      return {
        success: false,
        message: "未找到匹配的科室"
      };
    }

    finalHlwDeptId = dept._id;
  } else if (!hlwDeptId) {
    return {
      success: false,
      message: "请提供hlwDeptId或hlwDeptBusinessId参数"
    };
  }
  
  try {
    const bulkOps = [];
    const currentTime = new Date().getTime();
    
    memberIds.forEach((userid, index) => {
      bulkOps.push({
        updateOne: {
          filter: { corpId, userid },
          update: {
            $set: {
              [`hlwSortOrder.${finalHlwDeptId}`]: index + 1,
              updateTime: currentTime
            }
          }
        }
      });
    });
    
    const result = await db.collection("corp-member").bulkWrite(bulkOps);
    
    return {
      success: true,
      message: `成功重新排序 ${result.modifiedCount} 名成员`,
      data: {
        matchedCount: result.matchedCount,
        modifiedCount: result.modifiedCount,
        newOrder: memberIds.map((userid, index) => ({
          userid,
          sortOrder: index + 1
        }))
      },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "重新排序失败",
    };
  }
};

// 从指定科室移除成员（不删除成员记录）
exports.removeHlwMemberFromDept = async (context) => {
  const { corpId, userid, hlwDeptId, hlwDeptBusinessId } = context;

  if (!corpId || !userid) {
    return {
      success: false,
      message: "机构ID和用户ID不能为空"
    };
  }

  // 支持两种科室ID方式
  let finalHlwDeptId = hlwDeptId;
  if (!hlwDeptId && hlwDeptBusinessId) {
    // 通过业务deptId查询，先获取对应的_id
    const dept = await db.collection("hlw-dept-list").findOne({
      corpId,
      hlw_dept_id: hlwDeptBusinessId
    }, { projection: { _id: 1 } });

    if (!dept) {
      return {
        success: false,
        message: "未找到匹配的科室"
      };
    }

    finalHlwDeptId = dept._id;
  } else if (!hlwDeptId) {
    return {
      success: false,
      message: "请提供hlwDeptId或hlwDeptBusinessId参数"
    };
  }

  try {
    // 获取成员信息
    const member = await db
      .collection("corp-member")
      .findOne({ corpId, userid });

    if (!member) {
      return {
        success: false,
        message: "成员不存在",
      };
    }

    const currentHlwDeptIds = member.hlwDeptIds || [];

    // 需要处理数据不匹配的情况：
    // 1. 人员数据中存储的可能是业务科室ID
    // 2. 传入的可能是MongoDB _id

    let businessDeptId = null;
    let actualDeptIdToRemove = finalHlwDeptId;

    // 如果传入的是MongoDB _id，获取对应的业务ID
    if (finalHlwDeptId.length >= 24) {
      const dept = await db.collection("hlw-dept-list").findOne({
        _id: finalHlwDeptId,
        corpId
      }, { projection: { hlw_dept_id: 1 } });

      if (dept && dept.hlw_dept_id) {
        businessDeptId = dept.hlw_dept_id;
      }
    }

    // 检查成员是否属于指定科室（支持两种ID格式）
    let belongsToDept = currentHlwDeptIds.includes(finalHlwDeptId);

    if (!belongsToDept && businessDeptId) {
      belongsToDept = currentHlwDeptIds.includes(businessDeptId);
      if (belongsToDept) {
        actualDeptIdToRemove = businessDeptId;
      }
    }

    if (!belongsToDept) {
      return {
        success: false,
        message: "成员不属于指定科室",
      };
    }

    // 从科室列表中移除指定科室
    const updatedHlwDeptIds = currentHlwDeptIds.filter(deptId => deptId !== actualDeptIdToRemove);

    // 更新排序对象，移除对应科室的排序（需要同时处理两种ID格式）
    const updatedHlwSortOrder = { ...member.hlwSortOrder };
    if (updatedHlwSortOrder && typeof updatedHlwSortOrder === 'object') {
      delete updatedHlwSortOrder[finalHlwDeptId];      // 移除MongoDB _id的排序
      if (businessDeptId) {
        delete updatedHlwSortOrder[businessDeptId];    // 移除业务ID的排序
      }
      delete updatedHlwSortOrder[actualDeptIdToRemove]; // 移除实际使用的ID的排序
    }

    // 获取被移除成员的排序号（尝试多种ID格式）
    const removedSortOrder = member.hlwSortOrder?.[finalHlwDeptId] ||
                            member.hlwSortOrder?.[businessDeptId] ||
                            member.hlwSortOrder?.[actualDeptIdToRemove] || 0;

    // 构建更新字段
    const updateFields = {
      hlwDeptIds: updatedHlwDeptIds,
      hlwSortOrder: updatedHlwSortOrder,
      updateTime: new Date().getTime()
    };

    // 如果成员被移出所有科室，去除hlw标签
    if (updatedHlwDeptIds.length === 0) {
      updateFields.memberSource = null; // 清除hlw来源标签
      console.log(`成员 ${userid} 已从所有科室移除，清除hlw来源标签`);
    }

    // 更新成员信息，移除指定科室
    const result = await db
      .collection("corp-member")
      .updateOne(
        { corpId, userid },
        { $set: updateFields }
      );

    // 重新调整该科室中其他成员的排序（将大于被移除成员排序号的成员排序号减1）
    if (removedSortOrder > 0) {

      // 需要同时处理两种ID格式的排序调整
      const updatePromises = [];

      // 调整MongoDB _id格式的排序
      updatePromises.push(
        db.collection("corp-member").updateMany(
          {
            corpId,
            hlwDeptIds: finalHlwDeptId,
            [`hlwSortOrder.${finalHlwDeptId}`]: { $gt: removedSortOrder }
          },
          {
            $inc: { [`hlwSortOrder.${finalHlwDeptId}`]: -1 },
            $set: { updateTime: new Date().getTime() }
          }
        )
      );

      // 如果有业务ID，也调整业务ID格式的排序
      if (businessDeptId) {
        updatePromises.push(
          db.collection("corp-member").updateMany(
            {
              corpId,
              hlwDeptIds: businessDeptId,
              [`hlwSortOrder.${businessDeptId}`]: { $gt: removedSortOrder }
            },
            {
              $inc: { [`hlwSortOrder.${businessDeptId}`]: -1 },
              $set: { updateTime: new Date().getTime() }
            }
          )
        );
      }

      // 调整实际使用的ID格式的排序
      if (actualDeptIdToRemove !== finalHlwDeptId && actualDeptIdToRemove !== businessDeptId) {
        updatePromises.push(
          db.collection("corp-member").updateMany(
            {
              corpId,
              hlwDeptIds: actualDeptIdToRemove,
              [`hlwSortOrder.${actualDeptIdToRemove}`]: { $gt: removedSortOrder }
            },
            {
              $inc: { [`hlwSortOrder.${actualDeptIdToRemove}`]: -1 },
              $set: { updateTime: new Date().getTime() }
            }
          )
        );
      }

      await Promise.all(updatePromises);
    }

    // 构建返回消息
    let message;
    if (updatedHlwDeptIds.length === 0) {
      message = "已从最后一个科室移除，成员不再属于任何科室，hlw来源标签已清除";
    } else {
      message = "已从指定科室移除，其他成员排序已自动调整";
    }

    return {
      success: true,
      message: message,
      data: {
        modifiedCount: result.modifiedCount,
        remainingHlwDeptIds: updatedHlwDeptIds,
        isEmpty: updatedHlwDeptIds.length === 0,
        hlwSourceRemoved: updatedHlwDeptIds.length === 0, // 标识是否移除了hlw标签
        removedSortOrder: removedSortOrder
      },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "移除失败",
    };
  }
};

// 搜索HLW来源的人员（支持模糊搜索）
exports.searchHlwMembers = async (context) => {
  const { corpId, keyword = '', page = 1, pageSize = 20, hlwDeptId, searchAllRoles = false } = context;

  if (!corpId) {
    return { success: false, message: "机构ID不能为空" };
  }

  try {
    // 构建查询条件
    const query = {
      corpId
    };

    // 根据 searchAllRoles 参数决定是否只搜索 hlw 来源的人员
    if (!searchAllRoles) {
      query.memberSource = "hlw"; // 只搜索hlw来源的人员
    }

    // 如果指定了科室ID，添加科室过滤
    if (hlwDeptId) {
      query.hlwDeptIds = { $in: [hlwDeptId] };
    }

    // 如果有关键词，添加模糊搜索条件
    if (keyword && keyword.trim()) {
      const keywordRegex = new RegExp(keyword.trim(), 'i'); // 不区分大小写的正则表达式
      query.$or = [
        { anotherName: keywordRegex },        // 姓名模糊搜索
        { name: keywordRegex },               // 姓名模糊搜索
        // { doctorNo: keywordRegex },           // 工号模糊搜索
        // { title: keywordRegex },              // 职称模糊搜索
        // { specialty: keywordRegex },    // 擅长领域模糊搜索
        // { mobile: keywordRegex },             // 手机号模糊搜索
        // { callNumber: keywordRegex },         // 对外手机模糊搜索
        // { workNo: keywordRegex }              // 工号模糊搜索（另一个字段）
      ];
    }

    // 统计总数
    const total = await db.collection("corp-member").countDocuments(query);
    const pages = Math.ceil(total / pageSize);

    // 查询数据
    const data = await db
      .collection("corp-member")
      .find(query)
      .project({
        userid: 1,
        anotherName: 1,
        name: 1,
        avatar: 1,
        job: 1,
        title: 1,
        doctorNo: 1,
        specialty: 1,
        specialtyFields: 1,
        recommended: 1,
        memberSource: 1,
        hlwDeptIds: 1,
        hlwSortOrder: 1,
        mobile: 1,
        callNumber: 1,
        workNo: 1,
        memberTroduce: 1,
        outpatientTime: 1,
        convenienceService: 1,
        hlwDepartment: 1,
        deptIds: 1,
        gender: 1,
        createTime: 1,
        updateTime: 1
      })
      .sort({
        recommended: -1,  // 推荐的排在前面
        createTime: -1    // 创建时间倒序
      })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();

    // 如果有科室ID，为每个成员添加当前科室的排序信息
    const dataWithSort = data.map(member => {
      const result = { ...member };
      if (hlwDeptId && member.hlwSortOrder) {
        result.currentSortOrder = member.hlwSortOrder[hlwDeptId] || 0;
      }
      return result;
    });

    return {
      success: true,
      message: "搜索成功",
      data: dataWithSort,
      total,
      pages,
      page,
      pageSize,
      keyword: keyword.trim(),
      searchAllRoles: searchAllRoles,
      hlwDeptId: hlwDeptId || null
    };

  } catch (error) {
    return {
      success: false,
      message: error.message || "搜索失败"
    };
  }
};

// 人员管理查询 - 根据姓名、联系方式、所属部门、岗位进行查询
exports.searchCorpMembers = async (event) => {
  try {
    const {
      corpId,
      anotherName,
      mobile,
      deptIds,
      job,
      page = 1,
      pageSize = 20
    } = event;

    if (!corpId) {
      return {
        success: false,
        message: "机构ID不能为空"
      };
    }

    // 构建查询条件
    const query = { corpId };

    // 姓名模糊查询
    if (anotherName && anotherName.trim()) {
      query.anotherName = { $regex: anotherName.trim(), $options: 'i' };
    }

    // 联系方式查询
    if (mobile && mobile.trim()) {
      query.mobile = { $regex: mobile.trim(), $options: 'i' };
    }

    // 所属部门查询 - 支持数组或单个部门ID
    if (deptIds) {
      if (Array.isArray(deptIds) && deptIds.length > 0) {
        query.deptIds = { $in: deptIds };
      } else if (typeof deptIds === 'string' && deptIds.trim()) {
        query.deptIds = deptIds.trim();
      }
    }

    // 岗位查询
    if (job && job.trim()) {
      query.job = job.trim();
    }

    console.log('人员管理查询条件:', query);

    // 统计总数
    const total = await db.collection("corp-member").countDocuments(query);
    const pages = Math.ceil(total / pageSize);

    // 查询数据
    const data = await db
      .collection("corp-member")
      .find(query)
      .project({
        _id: 1,
        userid: 1,
        anotherName: 1,
        name: 1,
        avatar: 1,
        mobile: 1,
        job: 1,
        deptIds: 1,
        title: 1,
        memberSource: 1,
        hlwDeptIds: 1,
        gender: 1,
        callNumber: 1,
        workNo: 1,
        memberTroduce: 1,
        outpatientTime: 1,
        convenienceService: 1,
        doctorNo: 1,
        createTime: 1,
        updateTime: 1
      })
      .sort({ createTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();

    return {
      success: true,
      message: "查询成功",
      data,
      total,
      pages,
      page,
      pageSize
    };

  } catch (error) {
    console.error('人员管理查询失败:', error);
    return {
      success: false,
      message: error.message || "查询失败"
    };
  }
};

// 员工账号查询 - 针对已开通账号的查询
exports.searchOpenedAccounts = async (event) => {
  try {
    const {
      corpId,
      anotherName,
      name,
      accountState,
      page = 1,
      pageSize = 20
    } = event;

    if (!corpId) {
      return {
        success: false,
        message: "机构ID不能为空"
      };
    }

    // 构建查询条件 - 只查询已开通账号的用户
    const query = {
      corpId,
      open_userid: { $exists: true, $ne: null }
    };

    // 姓名查询 - 支持anotherName或name字段
    if (anotherName && anotherName.trim()) {
      query.$or = [
        { anotherName: { $regex: anotherName.trim(), $options: 'i' } },
        { name: { $regex: anotherName.trim(), $options: 'i' } }
      ];
    } else if (name && name.trim()) {
      query.$or = [
        { anotherName: { $regex: name.trim(), $options: 'i' } },
        { name: { $regex: name.trim(), $options: 'i' } }
      ];
    }

    // 账户状态查询
    if (accountState && accountState.trim()) {
      query.accountState = accountState.trim();
    }

    console.log('员工账号查询条件:', query);

    // 统计总数
    const total = await db.collection("corp-member").countDocuments(query);
    const pages = Math.ceil(total / pageSize);

    // 查询数据
    const data = await db
      .collection("corp-member")
      .find(query)
      .project({
        _id: 1,
        userid: 1,
        open_userid: 1,
        anotherName: 1,
        name: 1,
        avatar: 1,
        mobile: 1,
        job: 1,
        deptIds: 1,
        title: 1,
        accountState: 1,
        memberSource: 1,
        hlwDeptIds: 1,
        gender: 1,
        callNumber: 1,
        workNo: 1,
        memberTroduce: 1,
        outpatientTime: 1,
        convenienceService: 1,
        doctorNo: 1,
        createTime: 1,
        updateTime: 1,
        password: 1  // 可能需要显示是否设置了密码
      })
      .sort({ createTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();

    return {
      success: true,
      message: "查询成功",
      data,
      total,
      pages,
      page,
      pageSize
    };

  } catch (error) {
    console.error('员工账号查询失败:', error);
    return {
      success: false,
      message: error.message || "查询失败"
    };
  }
};

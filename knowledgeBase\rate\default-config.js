const DFFAULT_CONFIG = {
  fiveStar: ['热情亲切', '经验丰富', '迅速响应', '沟通清晰', '服务态度'],
  fourStar: ['态度友好', '专业解答', '较快回复'],
  threeStar: ['服务平平', '基本专业', '态度尚可'],
  twoStar: ['态度冷淡', '沟通障碍', '回复缓慢', '解答模糊'],
  oneStar: ['长时间无回复', '极不专业', '态度恶劣']
}
/**
 * @description 获取机构默认的评分配置
 * @param corpId 机构id
 * @returns 默认的评分配置
 */
exports.getDefaultConfig = (corpId) => {
  return Object.keys(DFFAULT_CONFIG).reduce((acc, rateStar) => {
    const texts = DFFAULT_CONFIG[rateStar];
    return [...acc, ...texts.map(text => ({ corpId, rateStar, text }))]
  }, [])
}

/**
 * @description 获取默认的评分配置
 * @param corpId 机构id
 * @returns 默认的评分配置
 */
exports.rateStars = Object.keys(DFFAULT_CONFIG);
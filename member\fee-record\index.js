const dayjs = require("dayjs");
const utils = require("../utils.js");
const customerHisSyncAppFunction = require("../app-function/customerHisSync.js");
const common = require("../../common.js");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getFeeRecord":
      return getFeeRecord(content);
    case "addFeeRecord":
      return addFeeRecord(content);
    case "merageHisFeeRecord":
      return merageHisFeeRecord(content);
    case "feeRecordStatistics":
      return feeRecordStatistics(content);
    case "updateFeeRecord":
      return updateFeeRecord(content);
    case "batchAddFeeRecord":
      return batchAddFeeRecord(content);
  }
};

/**
 * 获取客户费用记录
 * @param {object} content 请求内容
 * @returns {object} 返回结果
 */
async function getFeeRecord(content) {
  const {
    corpId,
    page,
    pageSize,
    orderName,
    orderStatus,
    billTime,
    billStatus,
    consultId,
    consultBillId,
    memberId,
  } = content;

  // 构建查询条件
  const query = { corpId };

  if (orderName && typeof orderName === "string" && orderName.trim()) {
    query.orderName = new RegExp(orderName.trim(), "i"); // 正则匹配
  }

  if (orderStatus) query.orderStatus = orderStatus;
  if (consultId) query.consultId = consultId;
  if (consultBillId) query.consultBillId = consultBillId;
  if (memberId) query.memberId = memberId;

  // 处理账单状态和账单时间
  if (billStatus === "unbilled") query.billStatus = { $ne: "billed" };
  if (billTime && billTime.length > 0) {
    query.billTime = {
      $gte: dayjs(billTime[0]).valueOf(),
      $lte: dayjs(billTime[1]).valueOf(),
    };
  }

  try {
    // 获取总数
    const total = await db.collection("fee-record").countDocuments(query);
    const pages = Math.ceil(total / pageSize); // 总页数
    // 获取分页数据
    const list = await db
      .collection("fee-record")
      .aggregate([
        { $match: query },
        { $sort: { billTime: -1 } },
        { $skip: pageSize * (page - 1) },
        { $limit: pageSize },
      ])
      .toArray();
    return {
      success: true,
      message: "获取成功",
      list,
      total,
      pages,
      size: pageSize,
    };
  } catch (error) {
    logger.error("获取客户费用记录失败:", error);
    return {
      success: false,
      message: "获取失败，请稍后再试",
    };
  }
}

/**
 * 费用记录统计
 * @param {object} content 请求内容
 * @returns {object} 返回结果
 */
async function feeRecordStatistics(content) {
  const { corpId, memberId, billTime, orderName, orderStatus, payTime } =
    content;

  let query = { corpId, memberId };

  if (orderName && typeof orderName === "string" && orderName.trim()) {
    const str = orderName.trim().replace(/[.*+?^=!:${}()|\[\]\/\\]/g, "\\$&");
    query.orderName = { $regex: ".*" + str, $options: "i" };
  }
  if (orderStatus) query.orderStatus = orderStatus;
  if (billTime && billTime.length > 0) {
    query.billTime = {
      $gte: dayjs(billTime[0]).valueOf(),
      $lte: dayjs(billTime[1]).valueOf(),
    };
  }
  if (payTime && payTime.length > 0) {
    query.payTime = {
      $gte: dayjs(payTime[0]).valueOf(),
      $lte: dayjs(payTime[1]).valueOf(),
    };
  }

  const res = await db
    .collection("fee-record")
    .aggregate([
      { $match: query },
      { $group: { _id: "$billId", uniqueRecord: { $first: "$$ROOT" } } },
      {
        $group: {
          _id: "$uniqueRecord.orderStatus",
          totalPayment: { $sum: "$uniqueRecord.payment" },
        },
      },
    ])
    .toArray();

  let refundTotalPayment = 0,
    noPayTotalPayment = 0,
    paidTotalPayment = 0;

  if (res && Array.isArray(res)) {
    res.forEach((i) => {
      if (i._id === "未结算") noPayTotalPayment = i.totalPayment;
      else if (i._id === "已退费") refundTotalPayment = i.totalPayment;
      else if (i._id === "已结算") paidTotalPayment = i.totalPayment;
    });
  }

  return {
    success: true,
    message: "获取成功",
    refundTotalPayment,
    noPayTotalPayment,
    paidTotalPayment,
  };
}

/**
 * 同步his数据
 * @param {object} content 请求内容
 * @returns {object} 返回结果
 */
async function merageHisFeeRecord({
  corpId,
  memberId,
  customerNumber,
  billdDates,
}) {
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!memberId) return { success: false, message: "客户id不能为空" };
  if (!customerNumber) return { success: false, message: "患者编号不能为空" };
  if (!billdDates || billdDates.length !== 2)
    return { success: false, message: "请选择时间范围" };

  let startTime = billdDates[0];
  let endTime = billdDates[1];
  let list = await customerHisSyncAppFunction.getHisFeeRecord({
    corpId,
    customerNumber,
    startTime,
    endTime,
  });

  if (list && Array.isArray(list) && list.length !== 0) {
    for (let i of list) {
      i.feeId = utils.generateRandomString();
      i.memberId = memberId;
      i.corpId = corpId;
      i.billTime && (i.billTime = dayjs(i.billTime).valueOf());
      i.payTime && (i.payTime = dayjs(i.payTime).valueOf());
      i.payment && (i.payment = Number(i.payment));
      i.totalFee && (i.totalFee = Number(i.totalFee));

      let feeItem = await db
        .collection("fee-record")
        .findOne({ memberId, corpId, billId: i.billId });

      if (!feeItem) {
        await addFeeRecord(i);
      } else if (feeItem.orderStatus !== i.orderStatus) {
        await db
          .collection("fee-record")
          .updateOne({ _id: feeItem._id }, { $set: i });
      }
    }
  }

  return {
    success: true,
    message: "同步成功",
  };
}

/**
 * 添加费用记录
 * @param {object} content 请求内容
 * @returns {object} 返回结果
 */
async function addFeeRecord(content) {
  const { corpId, memberId, ...rest } = content;

  if (!memberId) return { success: false, message: "客户id不能为空" };
  if (!corpId) return { success: false, message: "机构id不能为空" };

  try {
    let result = await db.collection("fee-record").insertOne({
      _id: common.generateRandomString(24),
      ...rest,
      memberId,
      corpId,
    });

    return {
      success: true,
      message: "新增成功",
      data: result.insertedId,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
}

/**
 * 批量添加费用记录
 * @param {object} content 请求内容
 * @returns {object} 返回结果
 */
async function batchAddFeeRecord(content) {
  const { list } = content;

  if (!Array.isArray(list) || list.length === 0) {
    return {
      success: false,
      message: "列表不能为空或无效",
    };
  }

  try {
    const bulkOps = list.map((item) => ({
      insertOne: {
        document: { _id: common.generateRandomString(24), ...item },
      },
    }));
    await db.collection("fee-record").bulkWrite(bulkOps);

    return {
      success: true,
      message: "批量新增成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "批量新增失败",
    };
  }
}

/**
 * 更新费用记录
 * @param {object} content 请求内容
 * @returns {object} 返回结果
 */
async function updateFeeRecord(content) {
  const { corpId, ids, params } = content;

  if (!corpId) return { success: false, message: "机构id不能为空" };

  try {
    await db
      .collection("fee-record")
      .updateMany({ corpId, _id: { $in: ids } }, { $set: params });

    return { success: true, message: "更新成功" };
  } catch (error) {
    return { success: false, message: "更新失败" };
  }
}

const dayjs = require("dayjs");
const request = require("../../request");
// const baseUrl = "http://**************:8089"; //his说 8089可能会关闭，用8083
const baseUrl = "http://**************:8083";

module.exports = async (item) => {
  switch (item.type) {
    case "getHisCustomer":
      return await getHisCustomer(item);
    case "addHisCustomer":
      return await addHisCustomer(item);
    case "registration":
      return await registration(item);
    case "getPayStatus":
      return await getPayStatus(item);
    case "hlwRefund":
      return await hlwRefund(item);
    case "hlwuploadprescription":
      return await hlwuploadprescription(item);
    case "hlwPrescriptionOrderStatus":
      return await hlwPrescriptionOrderStatus(item);
  }
};
async function getHisCustomer(item) {
  const { idCard } = item;
  try {
    const url = `${baseUrl}/his/hlw/patientinfo`;
    const { data, status_code, message } = await request.main(
      url,
      { type: "01", idnumber: idCard },
      "POST"
    );
    if (status_code == 200) {
      return {
        success: true,
        list: data?.patients,
        message: "获取成功",
      };
    } else {
      return {
        success: false,
        message: message || "获取失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: e.message || "获取失败",
    };
  }
}
async function addHisCustomer(item) {
  let { idCard, name, mobile, address, psnToken } = item;
  try {
    const url = `${baseUrl}/his/hlw/patientbulid`;
    const payload = {
      type: "1",
      name,
      socialno: idCard,
      tel: mobile,
    };
    if (psnToken) {
      payload.psnToken = psnToken;
      payload.type = "2";
    }
    if (typeof address === "string") {
      payload.address = address;
    }
    const { data, status_code, message } = await request.main(
      url,
      payload,
      "POST"
    );
    if (status_code == 200) {
      return {
        success: true,
        list: data?.patients,
        message: "获取成功",
      };
    } else {
      return {
        success: false,
        message: message || "获取失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: "获取失败",
    };
  }
}

// 预约挂号
async function registration(item) {
  const { patientId, doctorCode, unitCode, registerId } = item;
  try {
    const url = `${baseUrl}/his/hlw/registration`;
    // 根据dayjs 判断是上午 还是下午
    const ampm = dayjs().hour() < 12 ? "a" : "p";
    const requestday = dayjs().format("YYYY-MM-DD HH:mm:ss");
    // registerId 为 H开头 15位随机数
    const payload = {
      patientId,
      doctorCode,
      unitCode,
      requestday,
      ampm,
      registerId,
      chargetype: "普通",
    };
    const { data, status_code, message } = await request.main(
      url,
      payload,
      "POST"
    );
    if (data && data.register && data.register.medorg_order_no) {
      return {
        success: true,
        data,
        message: message || "挂号成功",
      };
    } else {
      return {
        success: false,
        message: message || "挂号失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: "获取失败",
    };
  }
}

//订单支付状态查询

async function getPayStatus(item) {
  const { patientId, registerId, medorgOrderNo } = item;
  try {
    const url = `${baseUrl}/his/hlw/settlement`;
    const { status, status_code, message, ...rest } = await request.main(
      url,
      {
        patientid: patientId,
        registerid: registerId,
        medorg_order_no: medorgOrderNo,
      },
      "POST"
    );
    const settlement =
      rest.data && rest.data.settlement ? rest.data.settlement : null;
    //settlement.status  0 已收费 1 已退费 5 未收费 x 已作废
    if (status_code == 200) {
      return {
        success: true,
        status: settlement ? settlement.status : '',
        settlement,
        message: "订单查询成功",
      };
    } else {
      return {
        success: false,
        message: message || "获取失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: "获取失败",
    };
  }
}

//医保患者退费
async function hlwRefund(item) {
  const { patientId, registerId, medorgOrderNo } = item;
  try {
    const url = `${baseUrl}/his/hlw/refund`;
    const { data, status_code, message, ...rest } = await request.main(
      url,
      {
        patientid: patientId,
        registerid: registerId,
        medorg_order_no: medorgOrderNo,
      },
      "POST"
    );
    console.log(rest);
    console.log(data, status_code, message);

    if (status_code == 200) {
      return {
        success: true,
        message: "退费成功",
      };
    } else {
      return {
        success: false,
        message: message || "获取失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: "获取失败",
    };
  }
}

// 6.外配处方回写
/**
 *
 * @param {*} param0
 * @returns
 */
async function hlwuploadprescription(item) {
  try {
    const url = `${baseUrl}/his/hlw/ordersave`;
    const { code, status_code, message } = await request.main(
      url,
      item.payload,
      "POST"
    );

    if (status_code == 200 && code == '0' && !message) {
      return {
        success: true,
        _id: item._id,
        message: "回写成功",
      };
    } else {
      return {
        success: false,
        message: message || "回写失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: err.message || "回写失败",
    };
  }
}

// 外配处方状态查询

async function hlwPrescriptionOrderStatus(item) {
  const { patientId, orderno } = item;
  try {
    const url = `${baseUrl}/his/hlw/orderstatus`;
    const { status_code, message, data } = await request.main(
      url,
      {
        patientid: patientId,
        orderno,
      },
      "POST"
    );
    if (status_code == 200 && data && data.orderstatus) {
      return {
        success: true,
        uploaded: data.orderstatus.status == '1',
        message: "查询成功",
      };
    } else {
      return {
        success: false,
        message: message || "查询失败",
      };
    }
  } catch (err) {
    return {
      success: false,
      message: "查询失败",
    };
  }
}

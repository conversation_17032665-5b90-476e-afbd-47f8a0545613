// 简单验证新添加的搜索函数
const corpMember = require('./index');

// 如果直接运行此文件，执行测试
if (require.main === module) {
  console.log('运行搜索函数测试...');
  
  // 简单的手动测试
  const testSearchCorpMembers = async () => {
    console.log('\n测试 searchCorpMembers:');
    
    // 测试1: 缺少corpId
    const result1 = await corpMember.searchCorpMembers({});
    console.log('测试1 - 缺少corpId:', result1);
    
    // 测试2: 正常参数（需要真实数据库连接）
    // const result2 = await corpMember.searchCorpMembers({
    //   corpId: 'test-corp-id',
    //   anotherName: '测试',
    //   page: 1,
    //   pageSize: 10
    // });
    // console.log('测试2 - 正常查询:', result2);
  };
  
  const testSearchOpenedAccounts = async () => {
    console.log('\n测试 searchOpenedAccounts:');
    
    // 测试1: 缺少corpId
    const result1 = await corpMember.searchOpenedAccounts({});
    console.log('测试1 - 缺少corpId:', result1);
  };
  
  testSearchCorpMembers();
  testSearchOpenedAccounts();
}

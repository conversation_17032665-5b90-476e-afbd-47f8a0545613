// 测试新添加的搜索函数
const corpMember = require('./index');

// 模拟数据库
const mockDb = {
  collection: (name) => ({
    countDocuments: jest.fn(),
    find: jest.fn().mockReturnThis(),
    project: jest.fn().mockReturnThis(),
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    toArray: jest.fn()
  })
};

describe('Corp Member Search Functions', () => {
  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks();
  });

  describe('searchCorpMembers', () => {
    test('应该正确构建基础查询条件', async () => {
      const mockCollection = mockDb.collection('corp-member');
      mockCollection.countDocuments.mockResolvedValue(10);
      mockCollection.toArray.mockResolvedValue([]);

      const event = {
        corpId: 'test-corp-id',
        anotherName: '张三',
        mobile: '138',
        deptIds: ['dept1', 'dept2'],
        job: '医生',
        page: 1,
        pageSize: 20
      };

      // 模拟数据库连接
      const originalDb = global.db;
      global.db = mockDb;

      try {
        await corpMember.searchCorpMembers(event);

        // 验证查询条件
        const expectedQuery = {
          corpId: 'test-corp-id',
          anotherName: expect.any(RegExp),
          mobile: expect.any(RegExp),
          deptIds: { $in: ['dept1', 'dept2'] },
          job: '医生'
        };

        expect(mockCollection.countDocuments).toHaveBeenCalledWith(expectedQuery);
      } finally {
        global.db = originalDb;
      }
    });

    test('应该处理空参数', async () => {
      const mockCollection = mockDb.collection('corp-member');
      mockCollection.countDocuments.mockResolvedValue(0);
      mockCollection.toArray.mockResolvedValue([]);

      const event = {
        corpId: 'test-corp-id'
      };

      const originalDb = global.db;
      global.db = mockDb;

      try {
        const result = await corpMember.searchCorpMembers(event);

        expect(result.success).toBe(true);
        expect(mockCollection.countDocuments).toHaveBeenCalledWith({
          corpId: 'test-corp-id'
        });
      } finally {
        global.db = originalDb;
      }
    });

    test('应该返回错误当corpId为空', async () => {
      const event = {};
      
      const result = await corpMember.searchCorpMembers(event);
      
      expect(result.success).toBe(false);
      expect(result.message).toBe('机构ID不能为空');
    });
  });

  describe('searchOpenedAccounts', () => {
    test('应该正确构建已开通账号的查询条件', async () => {
      const mockCollection = mockDb.collection('corp-member');
      mockCollection.countDocuments.mockResolvedValue(5);
      mockCollection.toArray.mockResolvedValue([]);

      const event = {
        corpId: 'test-corp-id',
        anotherName: '李四',
        accountState: 'active',
        page: 1,
        pageSize: 20
      };

      const originalDb = global.db;
      global.db = mockDb;

      try {
        await corpMember.searchOpenedAccounts(event);

        const expectedQuery = {
          corpId: 'test-corp-id',
          open_userid: { $exists: true, $ne: null },
          $or: [
            { anotherName: expect.any(RegExp) },
            { name: expect.any(RegExp) }
          ],
          accountState: 'active'
        };

        expect(mockCollection.countDocuments).toHaveBeenCalledWith(expectedQuery);
      } finally {
        global.db = originalDb;
      }
    });

    test('应该只查询已开通账号的用户', async () => {
      const mockCollection = mockDb.collection('corp-member');
      mockCollection.countDocuments.mockResolvedValue(0);
      mockCollection.toArray.mockResolvedValue([]);

      const event = {
        corpId: 'test-corp-id'
      };

      const originalDb = global.db;
      global.db = mockDb;

      try {
        await corpMember.searchOpenedAccounts(event);

        const expectedQuery = {
          corpId: 'test-corp-id',
          open_userid: { $exists: true, $ne: null }
        };

        expect(mockCollection.countDocuments).toHaveBeenCalledWith(expectedQuery);
      } finally {
        global.db = originalDb;
      }
    });

    test('应该返回错误当corpId为空', async () => {
      const event = {};
      
      const result = await corpMember.searchOpenedAccounts(event);
      
      expect(result.success).toBe(false);
      expect(result.message).toBe('机构ID不能为空');
    });
  });
});

// 如果直接运行此文件，执行测试
if (require.main === module) {
  console.log('运行搜索函数测试...');
  
  // 简单的手动测试
  const testSearchCorpMembers = async () => {
    console.log('\n测试 searchCorpMembers:');
    
    // 测试1: 缺少corpId
    const result1 = await corpMember.searchCorpMembers({});
    console.log('测试1 - 缺少corpId:', result1);
    
    // 测试2: 正常参数（需要真实数据库连接）
    // const result2 = await corpMember.searchCorpMembers({
    //   corpId: 'test-corp-id',
    //   anotherName: '测试',
    //   page: 1,
    //   pageSize: 10
    // });
    // console.log('测试2 - 正常查询:', result2);
  };
  
  const testSearchOpenedAccounts = async () => {
    console.log('\n测试 searchOpenedAccounts:');
    
    // 测试1: 缺少corpId
    const result1 = await corpMember.searchOpenedAccounts({});
    console.log('测试1 - 缺少corpId:', result1);
  };
  
  testSearchCorpMembers();
  testSearchOpenedAccounts();
}

const axios = require("axios");
const logger = require("./utils/logger");
exports.main = async (url, data, type) => {
  if (type === "POST") {
    return await post(url, data);
  } else {
    return await get(url);
  }
};

function get(url) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axios.get(url);
      logger.info(`请求URL: ${url}`);
      logger.info(`请求成功: ${JSON.stringify(response.data)}`);
      resolve(response.data);
    } catch (error) {
      logger.error(error);
      reject(error);
      // throw error;
    }
  });
}

function post(url, data) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axios.post(url, data);
      logger.info(`请求URL: ${url}`);
      logger.info(`请求DATA: ${JSON.stringify(data)}`);
      logger.info(`请求成功: ${JSON.stringify(response.data)}`);
      resolve(response.data);
    } catch (error) {
      logger.info(`请求失败: ${error}`);
      logger.error(error);
      reject(error);
      // throw error;
    }
  });
}

const dayjs = require("dayjs");
let db = null;
exports.main = async (event, mongodb) => {
  db = mongodb;
  switch (event.type) {
    case "upDatetransferTime":
      return await exports.upDatetransferTime(event);
    case "judgeTransferTime":
      return await exports.judgeTransferTime(event);
  }
};
// 更新transferTime字段
exports.upDatetransferTime = async (event) => {
  const { corpId, userId } = event;
  try {
    const transferTime = dayjs().valueOf();
    await db.collection("corp-member").updateOne(
      { corpId, userid: userId }, // 查询条件
      { $set: { transferTime } } // 更新内容
    );
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 判断transferTime是否过期 期限为30天
exports.judgeTransferTime = async (event) => {
  const { corpId, userId, env } = event;
  try {
    const user = await db.collection("corp-member").findOne(
      { corpId, userid: userId } // 查询条件
    );
    // 如果没有transferTime字段，返回false
    const { transferTime = "" } = user || {};
    if (!transferTime) {
      return {
        success: true,
        message: "获取成功",
        data: {
          isAfterToday: true,
          remainingTime: 0,
        },
      };
    }
    const isAfterToday = dayjs().isAfter(dayjs(transferTime).add(30, "day"));
    // 获取剩余天数
    const remainingTime = dayjs(transferTime)
      .add(30, "day")
      .diff(dayjs(), "day");
    return {
      success: true,
      message: "获取成功",
      data: {
        isAfterToday,
        remainingTime,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

const signatur = require("./signatur");
const information = require("./externalcontact-info");
const addExternalContact = require("./add-external-contact");
const externalContact = require("./externalContact");
const accessToken = require("./token");
const weChatApi = require("./wechat-api");
const staffAccountLicese = require("./account-license");
const groupTag = require("./group-tag");
const pushAppMessage = require("./push-app-message");
const staffQrcode = require("./staff-qrcode");
const suiteToken = require("./suite-token");
const authCorp = require("./auth-corp");
const accessUser = require("./access-user");
const tool = require("./tool");
const groupmsg = require("./groupmsg");
const sessionArchiveAuth = require("./session-archive-auth");
const gfyWechat = require("../gfy-wechat");
exports.main = async (context) => {
  switch (context.type) {
    case "getSignstrOfjsapi":
      return await signatur.getSignstrOfjsapi(context);
    case "getMyExternalcontact":
    case "getNameByexternalUserId":
    case "getCorpMemberInfoByUserId":
    case "getUnionidToExternalUserid":
    case "getCorpMemberExternalContact":
    case "unionidToExternalUserid":
    case "getMyExternalcontactByBatch":
    case "batchGetWeChatFriend":
      return await information.main(context);
    case "getAccessUser":
      return await accessUser.getAccessUser(context);
    case "addExternalContact":
      return await addExternalContact.main(context);
    case "getAccessToken":
      const token = await accessToken.getToken(context);
      return {
        success: true,
        accessToken: token,
        message: "获取成功",
      };
    case "getSuiteToken":
      return await accessToken.getSuiteToken(context.corpid);
    case "getWeChatUserInfo":
      return await weChatApi.getWeChatUserInfo(context);
    case "removeExternalContact":
    case "editExternalContact":
    case "transferExternalcontact":
    case "asyncExternalContactRemark":
    case "batchGetBehaviorData":
    case "getCorpAllStaffUseBehaviorDataToday":
    case "getAllCorpYesterdayBehaviorData":
      return await externalContact.main(context);
    case "transferLicense":
      return await staffAccountLicese.transferLicense(context);
    case "getCorpTagList":
      return await groupTag.getCorpTagList(context);
    case "syncCorpTag":
      return await groupTag.syncCorpTag(context);
    case "pushAppMessage":
      return await pushAppMessage.main(context);
    case "addContactWay":
    case "delContactWay":
    case "updateContactWay":
      return await staffQrcode.main(context);
    case "getSuiteAccessToken":
      return await suiteToken.getSuiteAccessToken(context);
    case "getCorpAuthCode":
      return await authCorp.main(context);
    case "uploadTempImage":
    case "uploadTempMedia":
    case "getPageTitleAndContent":
      return await tool.main(context);
    case "addWecomMsgTemplate":
    case "getWecomGroupmsgtask":
    case "getWecomGroupmsgSendResult":
    case "stopWecomGroupmsgTask":
    case "remindGroupmsgSend":
      return await groupmsg.main(context);
    case "setSessoinPublicKey":
    case "getSessionAuthUserList":
    case "getSessionSearchMsg":
    case "syncCallProgram":
    case "asyncProgramTask":
      return await sessionArchiveAuth.main(context);
    case "generateMiniProgramLink":
      return await gfyWechat.main(context);
    default:
      return { success: false, message: "未找到对应方法" };
  }
};

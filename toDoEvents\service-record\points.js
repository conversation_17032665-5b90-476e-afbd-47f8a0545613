const dayjs = require("dayjs");
const common = require("../../common.js");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getCorpServicePointsList":
      return await getCorpServicePointsList(content);
    case "getServicePointsStatistics":
      return await getServicePointsStatistics(content);
  }
};

// 获取企业服务积分列表
async function getCorpServicePointsList({
  corpId,
  serviceTypes,
  executorUserIds,
  dates,
  executeTeamIds,
  page = 1,
  pageSize = 10,
  serviceRates,
}) {
  let query = {
    corpId,
    points: { $exists: true, $ne: null, $ne: "" },
  };

  if (Array.isArray(executorUserIds) && executorUserIds.length > 0) {
    query.executorUserId = { $in: executorUserIds };
  }

  if (Array.isArray(executeTeamIds) && executeTeamIds.length > 0) {
    query.executeTeamId = { $in: executeTeamIds };
  }

  if (Array.isArray(serviceTypes) && serviceTypes.length > 0) {
    query.eventType = { $in: serviceTypes };
  }

  if (Array.isArray(serviceRates) && serviceRates.length > 0) {
    query.serviceRate = { $in: serviceRates };
  }

  if (Array.isArray(dates) && dates.length > 0) {
    query.executionTime = {
      $gte: dayjs(dates[0]).startOf("day").valueOf(),
      $lte: dayjs(dates[1]).endOf("day").valueOf(),
    };
  }

  const total = await db.collection("service-record").countDocuments(query);
  const pages = Math.ceil(total / pageSize);
  const data = await db
    .collection("service-record")
    .find(query)
    .sort({ createTime: -1 })
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .toArray();

  return {
    message: "获取成功",
    list: data,
    total,
    pages,
    success: true,
  };
}

// 积分统计
async function getServicePointsStatistics({
  corpId,
  executorUserIds,
  serviceTypes,
  dates,
  executeTeamIds,
  serviceRates,
}) {
  if (!corpId) return false;

  let query = {
    corpId,
    points: { $exists: true },
  };

  if (Array.isArray(dates) && dates.length > 0) {
    query.executionTime = {
      $gte: dayjs(dates[0]).startOf("day").valueOf(),
      $lte: dayjs(dates[1]).endOf("day").valueOf(),
    };
  }

  if (Array.isArray(executorUserIds) && executorUserIds.length > 0) {
    query.executorUserId = { $in: executorUserIds };
  }

  if (Array.isArray(executeTeamIds) && executeTeamIds.length > 0) {
    query.executeTeamId = { $in: executeTeamIds };
  }

  if (Array.isArray(serviceTypes) && serviceTypes.length > 0) {
    query.eventType = { $in: serviceTypes };
  }

  if (Array.isArray(serviceRates) && serviceRates.length > 0) {
    query.serviceRate = { $in: serviceRates };
  }

  const totalData = await db
    .collection("service-record")
    .aggregate([
      { $match: query },
      { $group: { _id: null, total: { $sum: "$points" } } },
    ])
    .toArray();

  return {
    allPoints:
      totalData && Array.isArray(totalData) && totalData.length
        ? totalData[0]?.total
        : 0,
    totalData,
    success: true,
    message: "获取成功",
  };
}

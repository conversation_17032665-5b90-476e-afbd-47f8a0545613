const common = require("../../common.js");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getMenus":
      return await exports.getMenus(content);
    case "getMenusByMenuIds":
      return await exports.getMenusByMenuIds(content);
    case "setCorpMenuConfig":
      return await exports.setCorpMenuConfig(content);
  }
};

// 获取菜单
exports.getMenus = async (ctx) => {
  const { success, message, data } = await getCorpMenus({}, ctx.corpId);
  if (success) return { success, message, data };
  return { success, message };
};

// 根据菜单ID列表获取菜单
exports.getMenusByMenuIds = async (context) => {
  const menuIdList = Array.isArray(context.menuIdList)
    ? context.menuIdList
    : [];
  const {
    success,
    message,
    data: menuList,
  } = await getCorpMenus({ menuId: { $in: menuIdList } }, context.corpId);
  if (!success) return { success, message };
  const parentIds = menuList
    .map((item) => item.parentId)
    .filter((item) => Boolean(item) && !menuIdList.includes(item));
  if (parentIds.length == 0) return { success, message, data: menuList };
  const {
    success: success2,
    message: message2,
    data: parentMenuList,
  } = await getCorpMenus({ menuId: { $in: parentIds } }, context.corpId);
  if (success2) {
    return {
      success: success2,
      message: message2,
      data: [...menuList, ...parentMenuList],
    };
  }
  return { success: success2, message: message2 };
};

// 设置企业菜单配置
exports.setCorpMenuConfig = async (ctx) => {
  const { corpId, menuId, menuName: customMenuName } = ctx;
  if (!corpId || !menuId || typeof customMenuName !== "string") {
    return {
      success: false,
      message: "参数错误",
    };
  }
  try {
    const res = await db
      .collection("corp-menu-config")
      .updateOne({ menuId, corpId }, { $set: { customMenuName } });
    if (res.modifiedCount > 0) return { success: true, message: "操作成功" };

    const result = await db.collection("corp-menu-config").insertOne({
      _id: common.generateRandomString(24),
      corpId,
      menuId,
      customMenuName,
    });
    return { success: true, message: "操作成功", data: result };
  } catch (e) {
    return {
      success: false,
      message: e.message,
    };
  }
};

// 获取企业菜单
async function getCorpMenus(query = {}, corpId) {
  try {
    const params = {
      $or: [
        { disabledCorps: { $exists: false } },
        { disabledCorps: { $nin: [corpId] } },
      ],
      ...query,
    };
    const res = await db
      .collection("sys-menu")
      .aggregate([
        { $match: params },
        {
          $lookup: {
            from: "corp-menu-config",
            let: { menuId: "$menuId" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$menuId", "$$menuId"] },
                      { $eq: ["$corpId", corpId] },
                    ],
                  },
                },
              },
              { $project: { customMenuName: 1 } },
            ],
            as: "customSet",
          },
        },
        { $limit: 10000 },
      ])
      .toArray();

    const list = res.map((item) => {
      const { customSet, ...menu } = item;
      return {
        ...menu,
        customMenuName: customSet[0]?.customMenuName || "",
      };
    });
    return { success: true, message: "获取成功", data: list };
  } catch (e) {
    return {
      success: false,
      message: e.message,
    };
  }
}

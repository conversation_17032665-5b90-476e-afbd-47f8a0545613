const zytHis = require("./zyt-his");
const drugInfo = require("./drug-info");
const patientDescription = require("./patient-description");
const tencentIM = require("./tencent-im");
const hlwDoctor = require("./hlw-doctor");
const hlwDiagnosis = require("./hlw-diagnosis");
const consultOrder = require("./consult-order");
const diagnosticRecord = require("./diagnostic-record");
const beijingCa = require("./beijing-ca");
const chatRecord = require("./chat-record");
const stats = require("./stats");


module.exports = async (item, db) => {
  switch (item.type) {
    case "getHisCustomer":
    case "addHisCustomer":
    case "registration":
    case "getPayStatus":
    case "hlwRefund":
    case "hlwuploadprescription":
    case "hlwPrescriptionOrderStatus":
      return await zytHis(item, db);
    case "getDrugInfo":
    case "deleteDrugInfo":
    case "updateDrugInfo":
    case "addDrugInfo":
      return await drugInfo(item, db);
    case "addPatientDescription":
    case "deletePatientDescription":
    case "updatePatientDescription":
    case "getPatientDescription":
      return await patientDescription(item, db);
    case "sendSystemNotification":
    case "generateUserSig":
    case "setUserProfileOrderStatys":
    case "setUserProfile":
      return await tencentIM(item, db);
    case "getHlwDoctorList":
    case "deleteHlwDoctor":
    case "updateHlwDoctor":
    case "addHlwDoctor":
    case "getRandomOnlineDoctor":
    case "isDoctorOnline":
    case "getAllDoctorNos":
      return await hlwDoctor(item, db);
    case "getDiagnosisList":
    case "deleteDiagnosis":
    case "updateDiagnosis":
    case "addDiagnosis":
      return await hlwDiagnosis(item, db);
    case "getConsultOrder":
    case "updateConsultOrder":
    case "deleteConsultOrder":
    case "addConsultOrder":
    case "updateConsultOrderStatus":
    case "getPatientOrder":
    case "refundConsultOrder":
    case "acceptConsultOrder":
    case "completeConsultOrder":
    case "finishConsultOrder":
    case "startConsultOrder":
    case "refreshOrderPayStatus":
    case "cancelConsultOrder":
    case "doctorHasPendingOrder":
    case "serviceFinishConsultOrder":
      return await consultOrder(item, db);
    case "addConsultDiagnosis":
    case "getOrderDiagnosis":
    case "getAuditDiagnosisList":
    case "getAuditedDiagnosisList":
    case "auditDiagnosis":
    case "uploadDiagnosis":
    case "reUploadDiagnosticRecord":
    case "getPatientDiagnosisRecord":
    case "getLatestSubmitTime":
    case "orderHasDiagnosis":
      return diagnosticRecord(item, db);
    case "caAuth":
    case "getSealImage":
    case "getCaUserInfo":
    case "startAutoSign":
    case "autoSign":
    case "closeAutoSign":
    case "autoSignVerifyCA":
      return await beijingCa(item, db);
    case "addChatMsg":
    case "getChatRecord":
      return await chatRecord(item, db);
    case "hlwStats":
      return await stats(item, db);
    default:
      return {
        success: false,
        message: "未找到接口",
      };
  }
};

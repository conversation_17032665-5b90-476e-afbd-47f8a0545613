const dayjs = require("dayjs");
const customer = require("../customer");
const consultRecordQuery = require("./query");
const eConsultRecord = require("./e-consult-record");
const common = require("../../common");
const api = require("../../api");
const statistics = require("./statistics");
const { ProjectCache, QueryCache } = require("./cache-helper");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "addConsultBillRecord":
      return await addConsultBillRecord(content);
    case "getConsultBillRecord":
      return await getConsultBillRecord(content);
    case "addConsultRecord":
      return await addConsultRecord(content);
    case "updateConsultRecord":
      return await updateConsultRecord(content);
    case "deleteConsultRecord":
      return await deleteConsultRecord(content);
    case "getConsultRecord":
      return await getConsultRecord(content);
    case "todayOrderExists":
      return await todayOrderExists(content);
    case "getPreConsultRecord":
      return await getPreConsultRecord(content);
    case "getFirstTriagePersonUserId":
      return await getFirstTriagePersonUserId(content);
    case "voidIssuedBill":
      return await voidIssuedBill(content);
    case "getConsultRecordCount":
      return await consultRecordQuery.main(content, db);
    case "getConsultStageCount":
      return await consultRecordQuery.main(content, db);
    case "addEConsuleRecord":
    case "updateEConsuleRecord":
    case "getEConsuleRecord":
    case "getFirstEConsuleRecord":
      return await eConsultRecord.main(content, db);
    case "getConsultantStatistics":
    case "getConsultantSalesRanking":
    case "getDoctorPerformance":
    case "getBillRecordsWithMemberInfo":
    case "getBillRecordsWithMemberDetails":
    case "getEConsultProjectStatistics":
    case "getProjectStatistics":
    case "getConsultantSourceStatistics":
    case "getProjectConsultStatistics":
    case "getConsultRecords":
    case "getDoctorDeductRecords":
      return await statistics.main(content, db);
    default:
      return {
        success: false,
        message: "未找到对应的操作类型",
      };
  }
};

/**
 * 新增咨询记录
 * @param {Object} content 咨询记录内容
 * @returns {Object} 结果
 */
async function addConsultRecord(content) {
  const {
    corpId,
    customerId,
    triagePersonUserId,
    receptionPersonUserId,
    visitType,
    projectIds,
    teamId,
    triageRemark = "",
    source,
    counselorUserId,
    introducerUserId,
  } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!customerId) return { success: false, message: "客户id不能为空" };

  const noTradeAmountCount = await db
    .collection("consult-record")
    .countDocuments({
      corpId,
      customerId,
      $or: [{ tradeAmount: 0 }, { tradeAmount: { $exists: false } }],
      visitStatus: { $ne: "canceled" },
    });

  const tradeAmountCount = await db.collection("bill-record").countDocuments({
    corpId,
    customerId,
  });

  let consultStage = "firstVisit";
  if (noTradeAmountCount && !tradeAmountCount) {
    consultStage = "returnVisit";
  } else if (tradeAmountCount) {
    consultStage = "moreConsumed";
  }
  const query = {
    _id: common.generateRandomString(24),
    corpId,
    customerId,
    visitStatus: "pending",
    triageTime: dayjs().valueOf(),
    triagePersonUserId,
    receptionPersonUserId,
    createTime: dayjs().valueOf(),
    consultStage,
    visitType,
    teamId,
    triageRemark,
    consumeCount: tradeAmountCount,
    introducerUserId,
    tradeAmount: 0,
    counselorUserId,
    projectIds,
    source: Array.isArray(source) ? source : [],
  };
  await db.collection("consult-record").insertOne(query);
  await customer.main(
    {
      corpId,
      customerId,
      inHospitalTime: dayjs().valueOf(),
      counselor: receptionPersonUserId,
      creator: triagePersonUserId,
      teamId,
      type: "updateCustomerInHospitalTime",
    },
    db
  );

  await customer.main(
    {
      corpId,
      customerId,
      counselorUserId,
      introducerUserId,
      type: "updateCustomerCounselor",
    },
    db
  );

  return { success: true, message: "新增成功" };
}

/**
 * 更新咨询记录
 * @param {Object} content 更新内容
 * @returns {Object} 结果
 */
async function updateConsultRecord(content) {
  let { corpId, id, params } = content;
  const { tradeAmount, ...rest } = params;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!id || !params) return { success: false, message: "缺少必要的字段" };
  try {
    let updateParams = { $set: rest };
    if (tradeAmount) {
      updateParams.$inc = {
        tradeAmount,
        consumeCount: 1,
      };
    }
    await db.collection("consult-record").updateOne({ _id: id }, updateParams);
    return { success: true, message: "更新成功" };
  } catch (error) {
    return { success: false, message: error.message || "更新失败" };
  }
}

/**
 * 删除咨询记录
 * @param {Object} content 删除内容
 * @returns {Object} 结果
 */
async function deleteConsultRecord(content) {
  const { corpId, id } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!id) return { success: false, message: "咨询记录id不能为空" };
  try {
    await db.collection("consult-record").deleteOne({ corpId, _id: id });
    return { success: true, message: "删除成功" };
  } catch (error) {
    return { success: false, message: "删除失败" };
  }
}

/**
 * 获取咨询记录列表（优化版本）
 * @param {Object} content 查询内容
 * @returns {Object} 结果
 */
async function getConsultRecord(content) {
  const {
    corpId,
    name,
    mobile,
    page = 1,
    pageSize = 10,
    triageTimeDates,
    receptionPersonUserIds,
    reportPeoples,
    triagePersonUserIds,
    visitStatus,
    consumeStatus,
    consultStages,
    createTeamId,
    customerId,
    projectIds,
    tradeStatus,
    receptionDates,
    teamId,
    source,
    customerSource,
    counselors,
    introducers,
    interviewDoctors,
  } = content;
  if (!corpId) return { success: false, message: "机构id不能为空" };

  let matchConditions = { corpId };

  // 构建查询条件
  if (Array.isArray(projectIds) && projectIds.length) {
    matchConditions["projectIds"] = { $in: projectIds };
  }
  if (Array.isArray(interviewDoctors)) {
    matchConditions["interviewDoctor"] = { $in: interviewDoctors };
  }

  if (
    triageTimeDates &&
    Array.isArray(triageTimeDates) &&
    triageTimeDates.length === 2
  ) {
    matchConditions.triageTime = {
      $gte: dayjs(triageTimeDates[0]).startOf("day").valueOf(),
      $lte: dayjs(triageTimeDates[1]).endOf("day").valueOf(),
    };
  }

  if (source && Array.isArray(source) && source.length > 0) {
    matchConditions.source = { $in: source };
  }

  if (
    receptionDates &&
    Array.isArray(receptionDates) &&
    receptionDates.length === 2
  ) {
    matchConditions.receptionTime = {
      $gte: dayjs(receptionDates[0]).startOf("day").valueOf(),
      $lte: dayjs(receptionDates[1]).endOf("day").valueOf(),
    };
  }

  if (introducers && Array.isArray(introducers) && introducers.length > 0) {
    matchConditions.introducerUserId = { $in: introducers };
  }

  if (counselors && Array.isArray(counselors) && counselors.length > 0) {
    matchConditions.counselorUserId = { $in: counselors };
  }

  if (
    receptionPersonUserIds &&
    Array.isArray(receptionPersonUserIds) &&
    receptionPersonUserIds.length > 0
  ) {
    matchConditions.receptionPersonUserId = { $in: receptionPersonUserIds };
  }

  if (
    triagePersonUserIds &&
    Array.isArray(triagePersonUserIds) &&
    triagePersonUserIds.length > 0
  ) {
    matchConditions.triagePersonUserId = { $in: triagePersonUserIds };
  }

  if (visitStatus && Array.isArray(visitStatus) && visitStatus.length > 0) {
    matchConditions.visitStatus = { $in: visitStatus };
  }

  if (
    consumeStatus &&
    Array.isArray(consumeStatus) &&
    consumeStatus.length > 0
  ) {
    let consumeCountQuery = [];
    if (consumeStatus.includes("notConsumed")) consumeCountQuery.push(0);
    if (consumeStatus.includes("consumed")) consumeCountQuery.push(1);
    if (consumeStatus.includes("moreConsumed"))
      consumeCountQuery.push({ $gt: 1 });
    matchConditions.consumeCount = { $in: consumeCountQuery };
  }

  let memberMatchConditions = {};
  if (name) memberMatchConditions["customerInfo.name"] = new RegExp(name, "i");
  if (mobile) {
    memberMatchConditions["$or"] = [
      { "customerInfo.mobile": mobile },
      { "customerInfo.phone1": mobile },
      { "customerInfo.phone2": mobile },
      { "customerInfo.phone3": mobile },
    ];
  }

  if (Array.isArray(customerSource) && customerSource.length) {
    memberMatchConditions["customerInfo.customerSource"] = {
      $in: customerSource,
    };
  }

  if (
    reportPeoples &&
    Array.isArray(reportPeoples) &&
    reportPeoples.length > 0
  ) {
    memberMatchConditions["customerInfo.addMethod"] = "eStoreReport";
    memberMatchConditions["customerInfo.creator"] = { $in: reportPeoples };
  }

  if (
    consultStages &&
    Array.isArray(consultStages) &&
    consultStages.length > 0
  ) {
    matchConditions.consultStage = { $in: consultStages };
  }
  if (customerId) matchConditions.customerId = customerId;
  if (teamId) matchConditions.teamId = teamId;
  if (tradeStatus === "traded") {
    matchConditions.$or = [{ tradeAmount: { $gt: 0 } }, { dealStatus: "成交" }];
  }
  if (tradeStatus === "untraded") {
    matchConditions.$or = [
      { tradeAmount: { $eq: 0 } },
      { dealStatus: "未成交" },
    ];
  }
  try {
    // 优化：使用单个聚合管道同时获取总数和分页数据
    const aggregateResult = await db
      .collection("consult-record")
      .aggregate(
        [
          // 第一步：匹配咨询记录条件（使用索引）
          { $match: matchConditions },

          // 第二步：排序（在lookup之前排序，减少处理的数据量）
          { $sort: { createTime: -1 } },

          // 第三步：与member集合关联（只获取必要字段）
          {
            $lookup: {
              from: "member",
              localField: "customerId",
              foreignField: "_id",
              as: "customerInfo",
            },
          },
          { $unwind: "$customerInfo" },

          // 第四步：匹配member相关条件
          { $match: memberMatchConditions },
          // 第五步：使用facet同时计算总数和分页数据
          {
            $facet: {
              totalCount: [{ $count: "count" }],
              data: [{ $skip: (page - 1) * pageSize }, { $limit: pageSize }],
            },
          },
        ],
        {
          // 添加聚合选项来优化性能
          allowDiskUse: true,
          maxTimeMS: 30000,
        }
      )
      .toArray();

    const result = aggregateResult[0];
    const list = result.data || [];
    const total = result.totalCount[0]?.count || 0;

    // 批量获取项目信息（使用缓存优化）
    const projectIds = list.reduce((ids, item) => {
      if (Array.isArray(item?.projectIds)) {
        ids.push(...item.projectIds);
      }
      return ids;
    }, []);

    if (projectIds.length) {
      // 使用缓存获取项目信息
      const projectMaps = await ProjectCache.getProjectInfo(
        corpId,
        projectIds,
        api
      );

      list.forEach((item) => {
        if (Array.isArray(item.projectIds)) {
          item.projectNames = item.projectIds
            .map((id) => projectMaps.nameMap.get(id))
            .filter(Boolean);
          item.projectDeptIds = item.projectIds
            .map((id) => projectMaps.deptMap.get(id))
            .filter(Boolean);
        } else {
          item.projectNames = [];
          item.projectDeptIds = [];
        }
      });
    }

    const pages = Math.ceil(total / pageSize);
    return { success: true, list, total, pages, message: "查询成功" };
  } catch (error) {
    console.error("getConsultRecord error:", error);
    return { success: false, message: "查询失败", error: error.message };
  }
}

async function todayOrderExists(ctx) {
  const { customerId, corpId } = ctx;
  if (!customerId || !corpId) return { success: false, message: "参数错误" };
  try {
    const total = await db.collection("consult-record").countDocuments({
      customerId,
      corpId,
      createTime: { $gte: dayjs().startOf("day").valueOf() },
      visitStatus: { $in: ["pending", "visited"] },
    });
    return { success: true, exist: total > 0 };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

/**
 * 获取首次分诊人用户ID
 * @param {Object} ctx 查询内容
 * @returns {Object} 结果
 */
async function getFirstTriagePersonUserId(ctx) {
  const { customerId, corpId } = ctx;
  if (!customerId || !corpId) return { success: false, message: "参数错误" };
  try {
    const record = await db
      .collection("consult-record")
      .find({ customerId, corpId })
      .sort({ triageTime: 1 })
      .limit(1)
      .project({
        triagePersonUserId: 1,
        triageTime: 1,
        receptionPersonUserId: 1,
      })
      .toArray();

    if (record.length > 0) {
      return { success: true, data: record[0] };
    } else {
      return { success: false, message: "未找到对应的数据" };
    }
  } catch (error) {
    return { success: false, message: error.message };
  }
}

/**
 * 作废开单
 * @param {Object} params - 参数对象
 * @param {string} params.corpId - 机构id
 * @param {string} params.consultId - 咨询记录id
 * @param {number} params.consumeAmount - 消费金额
 * @param {string} params.consultBillId - 咨询账单id
 * @returns {Object} 结果
 */
async function voidIssuedBill({
  corpId,
  consultId,
  consumeAmount,
  consultBillId,
}) {
  if (!corpId) return { success: false, message: "机构id不能为空" };
  const deductRecordCount = await db
    .collection("deduct-record")
    .countDocuments({
      corpId,
      consultBillId,
    });
  if (deductRecordCount > 0)
    return { success: false, message: "已有治疗记录,不可作废" };
  await Promise.all([
    db.collection("bill-record").deleteMany({ corpId, consultBillId }),
    db.collection("fee-record").deleteMany({ corpId, consultBillId }),
    db
      .collection("consult-record")
      .updateOne(
        { corpId, _id: consultId },
        { $inc: { tradeAmount: -consumeAmount, consumeCount: -1 } }
      ),
    db.collection("consult-bill-record").deleteOne({ corpId, consultBillId }),
    db.collection("consume-record").deleteMany({ corpId, consultBillId }),
    db.collection("treatment-record").deleteMany({
      corpId,
      consultId,
    }),
  ]);
  return { success: true, message: "删除成功" };
}

async function addConsultBillRecord({
  corpId,
  consultId,
  billType,
  consultBillId,
  customerId,
  consumeAmount,
}) {
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    await db.collection("consult-bill-record").insertOne({
      corpId,
      consultId,
      billType,
      consultBillId,
      customerId,
      consumeAmount,
      createTime: dayjs().valueOf(),
    });
    return { success: true, message: "新增成功", consumeAmount };
  } catch (error) {
    return { success: false, message: "新增失败" };
  }
}

async function getConsultBillRecord({ corpId, consultId }) {
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const result = await db
      .collection("consult-bill-record")
      .find({ corpId, consultId })
      .toArray();
    return { success: true, data: result, message: "查询成功" };
  } catch (error) {
    return { success: false, message: "查询失败" };
  }
}

async function getPreConsultRecord(params) {
  const { customerId, corpId } = params;
  try {
    const consult = await db.collection("consult-record").findOne(
      {
        customerId,
        corpId,
      },
      { sort: { createTime: -1 } },
      { projection: { _id: 0, projectIds: 1, source: 1, createTime: 1 } }
    );
    const EConsult = await db.collection("e-consult-record").findOne(
      {
        customerId,
        corpId,
      },
      { sort: { timestamp: -1 } },
      { projection: { _id: 0, projectIds: 1, source: 1, timestamp: 1 } }
    );
    const createTime = consult ? consult.createTime : 0;
    const eConsultTime = EConsult ? EConsult.timestamp : 0;
    const record = eConsultTime > createTime ? EConsult : consult;
    return { success: true, data: record };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

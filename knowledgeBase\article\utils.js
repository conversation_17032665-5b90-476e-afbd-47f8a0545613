exports.formatArticle = function (article) {
  const { cateId = '', corpId = '', userId, cover = '', disease = [], tagIds = [], keyword = '', summary = '', title = '', link = '', content = '' } = article;
  let message = '';
  if (typeof corpId !== 'string' || !corpId) {
    message = '机构id不能为空'
  } else if (typeof userId !== 'string' || !userId) {
    message = '操作人id不能为空'
  } else if (typeof cateId !== 'string' || !cateId) {
    message = '文章分类id不能为空'
  } else if (typeof title !== 'string' || title.trim() === '') {
    message = '文章标题不能为空'
  } else if (title.trim().length > 30) {
    message = '文章标题不能超过30个字'
  } else if (typeof summary === 'string' && summary.length > 50) {
    message = '文章摘要不能超过50个字'
  } else if (typeof keyword === 'string' && keyword.length > 50) {
    message = '文章摘要不能超过50个字'
  } else if (Array.isArray(disease) && disease.length > 10) {
    message = '文章关联的疾病不能超过十个'
  } else if ((typeof link !== 'string' || link.trim() === '') && (typeof content !== 'string' || content.trim() === '')) {
    message = '文章内容不能为空'
  }
  if (message) {
    return { success: false, message }
  }
  return {
    title,
    link: typeof link === 'string' ? link : '',
    content: typeof content === 'string' ? content : '',
    tagIds: Array.isArray(tagIds) ? tagIds : [],
    cateId: cateId.slice(0, 100),
    corpId: corpId.slice(0, 100),
    cover: typeof cover === 'string' ? cover.slice(0, 400) : '',
    disease: getDisease(disease),
    keyword: typeof keyword === 'string' ? keyword : '',
    success: true,
    summary: typeof summary === 'string' ? summary : ''
  }
}

function getDisease(disease) {
  if (Array.isArray(disease)) {
    return disease.filter(i => (typeof i === 'string' && i.length <= 50))
  }
  return []
}
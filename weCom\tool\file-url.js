const axios = require("axios");
// const { JSD<PERSON> } = require("jsdom");

exports.getPageTitleAndContent = async (context) => {
  // const { url } = context;
  // try {
  //   const response = await axios.get(url);
  //   const dom = new JSDOM(response.data);
  //   const document = dom.window.document;
  //   console.log("document", document);
  //   const weixinTitle =
  //     document.querySelector("#activity-name")?.textContent.trim() || "";
  //   const title = document.querySelector("head > title")?.textContent || "";
  //   let bodyContent = document.querySelector("body")?.textContent || "";
  //   bodyContent = bodyContent.replace(/\s+/g, " ").trim();
  //   const match = bodyContent.match(/[\u4e00-\u9fa5，。！？、]*[。！？]/);
  //   const subtitle = (match && match[0]) || "";
  //   const images = Array.from(document.querySelectorAll("img")).map(
  //     (img) => img.src
  //   );

  //   const page = {
  //     title,
  //     weixinTitle,
  //     subtitle,
  //     images: images[0],
  //   };

  //   return {
  //     success: true,
  //     message: "获取成功",
  //     data: page,
  //   };
  // } catch (error) {
  //   // 如果发送请求时发生错误，那么 URL 是无效的
  //   return {
  //     success: false,
  //     message: "链接无效",
  //   };
  // }
};

// 互联网科室管理工具函数

/**
 * 验证科室名称
 * @param {string} hlw_dept_name 科室名称
 * @returns {object} 验证结果
 */
function validateDeptName(hlw_dept_name) {
  if (!hlw_dept_name || typeof hlw_dept_name !== "string") {
    return { valid: false, message: "科室名称不能为空" };
  }
  
  const trimmedName = hlw_dept_name.trim();
  if (trimmedName === "") {
    return { valid: false, message: "科室名称不能为空" };
  }
  
  if (trimmedName.length > 50) {
    return { valid: false, message: "科室名称不能超过50个字符" };
  }
  
  return { valid: true, value: trimmedName };
}

/**
 * 验证科室ID
 * @param {string} hlw_dept_id 科室ID
 * @returns {object} 验证结果
 */
function validateDeptId(hlw_dept_id) {
  if (!hlw_dept_id || typeof hlw_dept_id !== "string") {
    return { valid: false, message: "科室ID不能为空" };
  }
  
  const trimmedId = hlw_dept_id.trim();
  if (trimmedId === "") {
    return { valid: false, message: "科室ID不能为空" };
  }
  
  if (trimmedId.length > 20) {
    return { valid: false, message: "科室ID不能超过20个字符" };
  }
  
  // 检查是否包含特殊字符（只允许字母、数字、下划线、中划线）
  const validPattern = /^[a-zA-Z0-9_-]+$/;
  if (!validPattern.test(trimmedId)) {
    return { valid: false, message: "科室ID只能包含字母、数字、下划线和中划线" };
  }
  
  return { valid: true, value: trimmedId };
}

/**
 * 验证科室介绍
 * @param {string} hlw_dept_description 科室介绍
 * @returns {object} 验证结果
 */
function validateDeptDescription(hlw_dept_description) {
  if (hlw_dept_description === null || hlw_dept_description === undefined) {
    return { valid: true, value: "" };
  }
  
  if (typeof hlw_dept_description !== "string") {
    return { valid: false, message: "科室介绍格式不正确" };
  }
  
  const trimmedDesc = hlw_dept_description.trim();
  
  if (trimmedDesc.length > 500) {
    return { valid: false, message: "科室介绍不能超过500个字符" };
  }
  
  return { valid: true, value: trimmedDesc };
}

/**
 * 验证状态值
 * @param {number} hlw_status 状态值
 * @returns {object} 验证结果
 */
function validateStatus(hlw_status) {
  if (hlw_status === null || hlw_status === undefined) {
    return { valid: true, value: 1 }; // 默认正常状态
  }
  
  if (![0, 1].includes(hlw_status)) {
    return { valid: false, message: "状态值无效，只能是0或1" };
  }
  
  return { valid: true, value: hlw_status };
}

/**
 * 验证排序值
 * @param {number} hlw_sort 排序值
 * @returns {object} 验证结果
 */
function validateSort(hlw_sort) {
  if (hlw_sort === null || hlw_sort === undefined) {
    return { valid: true, value: 0 }; // 默认排序值
  }
  
  if (typeof hlw_sort !== "number" || hlw_sort < 0 || hlw_sort % 1 !== 0) {
    return { valid: false, message: "排序值必须是非负整数" };
  }
  
  return { valid: true, value: hlw_sort };
}

/**
 * 验证层级值
 * @param {number} hlw_level 层级值
 * @returns {object} 验证结果
 */
function validateLevel(hlw_level) {
  if (![1, 2, 3].includes(hlw_level)) {
    return { valid: false, message: "科室层级只能是1、2或3" };
  }
  
  return { valid: true, value: hlw_level };
}

/**
 * 构建查询条件
 * @param {object} params 查询参数
 * @param {string} corpId 机构ID
 * @returns {object} MongoDB查询条件
 */
function buildQueryConditions(params, corpId) {
  const { hlw_status, keyword, hlw_level, hlw_parent_id } = params;
  const query = { corpId };
  
  // 状态筛选
  if (hlw_status !== undefined && hlw_status !== null && hlw_status !== "") {
    query.hlw_status = hlw_status;
  }
  
  // 层级筛选
  if (hlw_level !== undefined && hlw_level !== null && hlw_level !== "") {
    query.hlw_level = hlw_level;
  }
  
  // 上级科室筛选
  if (hlw_parent_id !== undefined && hlw_parent_id !== null && hlw_parent_id !== "") {
    query.hlw_parent_id = hlw_parent_id;
  }
  
  // 关键词搜索
  if (keyword && keyword.trim()) {
    const keywordRegex = new RegExp(keyword.trim(), "i");
    query.$or = [
      { hlw_dept_name: keywordRegex },
      { hlw_dept_id: keywordRegex },
      { hlw_dept_description: keywordRegex }
    ];
  }
  
  return query;
}

/**
 * 构建排序条件
 * @param {string} sortBy 排序字段
 * @param {number} sortOrder 排序方向
 * @returns {object} MongoDB排序条件
 */
function buildSortConditions(sortBy = "hlw_create_time", sortOrder = -1) {
  const allowedSortFields = [
    "hlw_create_time", 
    "hlw_update_time", 
    "hlw_dept_name", 
    "hlw_dept_id", 
    "hlw_level", 
    "hlw_sort"
  ];
  const finalSortBy = allowedSortFields.includes(sortBy) ? sortBy : "hlw_create_time";
  const finalSortOrder = [1, -1].includes(sortOrder) ? sortOrder : -1;
  
  const sort = {};
  sort[finalSortBy] = finalSortOrder;
  
  return sort;
}

/**
 * 格式化科室数据（用于返回给前端）
 * @param {object} dept 科室数据
 * @returns {object} 格式化后的科室数据
 */
function formatDeptData(dept) {
  if (!dept) return null;
  
  return {
    _id: dept._id,
    corpId: dept.corpId,
    hlw_dept_name: dept.hlw_dept_name,
    hlw_dept_id: dept.hlw_dept_id,
    hlw_dept_description: dept.hlw_dept_description || "",
    hlw_parent_id: dept.hlw_parent_id,
    hlw_area_id: dept.hlw_area_id,
    hlw_level: dept.hlw_level,
    hlw_sort: dept.hlw_sort,
    hlw_status: dept.hlw_status,
    hlw_status_text: dept.hlw_status === 1 ? "正常" : "禁用",
    hlw_level_text: getLevelText(dept.hlw_level),
    hlw_create_time: dept.hlw_create_time,
    hlw_update_time: dept.hlw_update_time,
    // 关联数据
    areaInfo: dept.areaInfo || [],
    parentInfo: dept.parentInfo || [],
    children: dept.children || []
  };
}

/**
 * 获取层级文本
 * @param {number} level 层级数字
 * @returns {string} 层级文本
 */
function getLevelText(level) {
  const levelMap = {
    1: "一级科室",
    2: "二级科室", 
    3: "三级科室"
  };
  return levelMap[level] || "未知层级";
}

/**
 * 格式化科室列表（用于返回给前端）
 * @param {Array} depts 科室列表
 * @returns {Array} 格式化后的科室列表
 */
function formatDeptList(depts) {
  if (!Array.isArray(depts)) return [];
  
  return depts.map(dept => formatDeptData(dept));
}

/**
 * 生成科室ID（如果不提供的话）
 * @param {string} deptName 科室名称
 * @param {string} corpId 机构ID
 * @param {number} level 科室层级
 * @returns {string} 生成的科室ID
 */
function generateDeptId(deptName, corpId, level = 1) {
  // 提取科室名称的拼音首字母或简化版本
  const timestamp = Date.now().toString().slice(-6);
  const corpIdSuffix = corpId.slice(-3);
  const levelPrefix = level === 1 ? "L1" : level === 2 ? "L2" : "L3";
  
  // 简单的ID生成策略，实际项目中可能需要更复杂的逻辑
  return `${levelPrefix}_${corpIdSuffix}_${timestamp}`;
}

/**
 * 检查科室层级是否合法
 * @param {number} currentLevel 当前层级
 * @param {number} parentLevel 父级层级
 * @returns {object} 检查结果
 */
function validateLevelHierarchy(currentLevel, parentLevel) {
  if (parentLevel && currentLevel !== parentLevel + 1) {
    return { 
      valid: false, 
      message: `${getLevelText(currentLevel)}的上级必须是${getLevelText(currentLevel - 1)}` 
    };
  }
  
  return { valid: true };
}

/**
 * 构建聚合管道（用于关联查询）
 * @param {object} matchCondition 匹配条件
 * @param {object} sortCondition 排序条件
 * @param {number} skip 跳过数量
 * @param {number} limit 限制数量
 * @returns {Array} 聚合管道
 */
function buildAggregationPipeline(matchCondition, sortCondition, skip = 0, limit = 20) {
  return [
    { $match: matchCondition },
    // 关联院区信息
    {
      $lookup: {
        from: "hlw-hospital-area",
        let: { areaId: "$hlw_area_id" },
        pipeline: [
          { $match: { $expr: { $eq: ["$_id", { $toObjectId: "$$areaId" }] } } }
        ],
        as: "areaInfo"
      }
    },
    // 关联父级科室信息
    {
      $lookup: {
        from: "hlw-dept-list",
        let: { parentId: "$hlw_parent_id" },
        pipeline: [
          { $match: { $expr: { $eq: ["$_id", { $toObjectId: "$$parentId" }] } } }
        ],
        as: "parentInfo"
      }
    },
    { $sort: sortCondition },
    { $skip: skip },
    { $limit: limit }
  ];
}

module.exports = {
  validateDeptName,
  validateDeptId,
  validateDeptDescription,
  validateStatus,
  validateSort,
  validateLevel,
  buildQueryConditions,
  buildSortConditions,
  formatDeptData,
  formatDeptList,
  generateDeptId,
  validateLevelHierarchy,
  getLevelText,
  buildAggregationPipeline
};

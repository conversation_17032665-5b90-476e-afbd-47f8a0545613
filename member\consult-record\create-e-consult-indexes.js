/**
 * E-Consult-Record 查询性能优化索引脚本
 * 解决 getEConsuleRecord 查询超时问题
 */
const { connectToMongoDB, getDatabase } = require('../../mongodb');

async function createEConsultOptimizedIndexes() {
  try {
    console.log('开始连接MongoDB...');
    await connectToMongoDB();
    const db = await getDatabase(process.env.CONFIG_DB_NAME || 'ytk-customer-service');
    
    console.log('开始创建 e-consult-record 优化索引...');

    // === e-consult-record 集合索引 ===
    const eConsultCollection = db.collection('e-consult-record');

    // 1. 主要查询索引 - 支持按机构、时间范围查询和排序
    await eConsultCollection.createIndex(
      { corpId: 1, timestamp: -1, createTime: -1 },
      { name: 'corpId_timestamp_createTime', background: true }
    );
    console.log('✓ 创建 corpId + timestamp + createTime 索引');

    // 2. 客户查询索引 - 支持按客户ID查询
    await eConsultCollection.createIndex(
      { corpId: 1, customerId: 1, timestamp: -1 },
      { name: 'corpId_customerId_timestamp', background: true }
    );
    console.log('✓ 创建 corpId + customerId + timestamp 索引');

    // 3. 项目筛选索引
    await eConsultCollection.createIndex(
      { corpId: 1, projectIds: 1, timestamp: -1 },
      { name: 'corpId_projectIds_timestamp', background: true }
    );
    console.log('✓ 创建 corpId + projectIds + timestamp 索引');

    // 4. 来源筛选索引
    await eConsultCollection.createIndex(
      { corpId: 1, source: 1, timestamp: -1 },
      { name: 'corpId_source_timestamp', background: true }
    );
    console.log('✓ 创建 corpId + source + timestamp 索引');

    // 5. 登记人筛选索引
    await eConsultCollection.createIndex(
      { corpId: 1, registrantUserId: 1, timestamp: -1 },
      { name: 'corpId_registrantUserId_timestamp', background: true }
    );
    console.log('✓ 创建 corpId + registrantUserId + timestamp 索引');

    // 6. 开发者筛选索引
    await eConsultCollection.createIndex(
      { corpId: 1, userId: 1, timestamp: -1 },
      { name: 'corpId_userId_timestamp', background: true }
    );
    console.log('✓ 创建 corpId + userId + timestamp 索引');

    // 7. 复合查询索引 - 支持多条件组合查询
    await eConsultCollection.createIndex(
      { corpId: 1, timestamp: -1, source: 1, projectIds: 1 },
      { name: 'corpId_timestamp_source_projectIds', background: true }
    );
    console.log('✓ 创建复合查询索引');

    // === member 集合索引 ===
    console.log('\n开始创建 member 优化索引...');
    const memberCollection = db.collection('member');

    // 1. 基础查询索引
    await memberCollection.createIndex(
      { corpId: 1, _id: 1 },
      { name: 'corpId_id', background: true }
    );
    console.log('✓ 创建 corpId + _id 索引');

    // 2. 手机号查询索引
    await memberCollection.createIndex(
      { corpId: 1, mobile: 1 },
      { name: 'corpId_mobile', background: true }
    );
    console.log('✓ 创建 corpId + mobile 索引');

    // 3. 姓名查询索引
    await memberCollection.createIndex(
      { corpId: 1, name: 1 },
      { name: 'corpId_name', background: true }
    );
    console.log('✓ 创建 corpId + name 索引');

    // 4. 到院状态查询索引
    await memberCollection.createIndex(
      { corpId: 1, inHospitalTimes: 1 },
      { name: 'corpId_inHospitalTimes', background: true, sparse: true }
    );
    console.log('✓ 创建 corpId + inHospitalTimes 索引');

    // 5. 文本搜索索引（可选，用于全文搜索优化）
    try {
      await memberCollection.createIndex(
        { name: "text", mobile: "text" },
        { 
          name: 'name_mobile_text',
          background: true,
          partialFilterExpression: { corpId: { $exists: true } }
        }
      );
      console.log('✓ 创建 name + mobile 文本索引');
    } catch (error) {
      console.log('⚠ 文本索引创建失败（可能已存在）:', error.message);
    }

    console.log('\n=== 索引创建完成！===');

    // 显示当前索引状态
    console.log('\ne-consult-record 集合当前索引：');
    const eConsultIndexes = await eConsultCollection.indexes();
    eConsultIndexes.forEach(index => {
      console.log(`- ${index.name}: ${JSON.stringify(index.key)}`);
    });

    console.log('\nmember 集合当前索引：');
    const memberIndexes = await memberCollection.indexes();
    memberIndexes.forEach(index => {
      console.log(`- ${index.name}: ${JSON.stringify(index.key)}`);
    });

    process.exit(0);
  } catch (error) {
    console.error('创建索引失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createEConsultOptimizedIndexes();
}

module.exports = { createEConsultOptimizedIndexes }; 
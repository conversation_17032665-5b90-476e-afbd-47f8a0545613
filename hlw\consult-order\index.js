const common = require("../../common");
const zytHis = require("../zyt-his");
const dayjs = require("dayjs");
const { getConfig } = require("../utils/config");
const tencentIM = require("../tencent-im");
const trigger = require("./trigger");
let db = "";
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "addConsultOrder":
      return await addConsultOrder(item);
    case "getPatientOrder":
      return await getPatientOrder(item);
    case "deleteConsultOrder":
      return await deleteConsultOrder(item);
    case "updateConsultOrder":
      return await updateConsultOrder(item);
    case "getConsultOrder":
      return await getConsultOrder(item);
    case "updateConsultOrderStatus":
      return await updateConsultOrderStatus(item);
    case "refundConsultOrder":
      return await refundConsultOrder(item);
    case "acceptConsultOrder":
      return await acceptConsultOrder(item);
    case "completeConsultOrder":
      return await completeConsultOrder(item);
    case "finishConsultOrder":
      return await finishConsultOrder(item);
    case "startConsultOrder":
      return await startConsultOrder(item);
    case "refreshOrderPayStatus":
      return await refreshOrderPayStatus(item);
    case "cancelConsultOrder":
      return await cancelConsultOrder(item);
    case "doctorHasPendingOrder":
      return await doctorHasPendingOrder(item);
    case "refundFeeAndOrder":
      return await refundFeeAndOrder(item);
    case "serviceFinishConsultOrder":
      return await serviceFinishConsultOrder(item);
    default:
      return {
        success: false,
        message: "请求失败!",
      };
  }
};
// 咨询订单库 数据库是 consult-order
async function addConsultOrder(item) {
  let { params, corpId } = item;
  const orderId = `H${common.generateRandomString(10)}`;
  const registerId = `H${common.generateRandomString(15)}`;
  params.orderId = orderId;
  params.registerId = registerId;
  params.createTime = Date.now();
  params.orderStatus = "pending";
  params.payStatus = "pending";
  params.corpId = corpId;
  const { payDuration } = await getConfig(corpId);
  params.payExpireTime = dayjs().add(payDuration, "minute").valueOf();
  try {
    // const has = await hasPendingOrder(params.patientId);
    // if (has) {
    //   return {
    //     success: false,
    //     message: "很抱歉！您有处理中的问诊订单，暂不支持发起新的问诊",
    //   };
    // }
    const { insertedId } = await db
      .collection("consult-order")
      .insertOne(params);
    if (insertedId) {
      const { patientId, doctorCode, unitCode, registerId } = params;
      const { success, data, message } = await zytHis({
        type: "registration",
        patientId,
        doctorCode,
        unitCode,
        registerId,
      });
      if (success && data && data.register && data.register.medorg_order_no) {
        db.collection("consult-order").updateOne(
          { _id: insertedId },
          { $set: { medorg_order_no: data.register.medorg_order_no } }
        );
        return {
          success: true,
          message: "新增成功",
          data: {
            orderId,
            registerId,
            medorg_order_no: data.register.medorg_order_no,
          },
        };
      }
      // 如果his接口调用失败 删除订单
      await db.collection("consult-order").deleteOne({ _id: insertedId });
      return { success: false, message: message || "新增失败" };
    }
    return {
      success: false,
      message: "新增失败",
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "新增失败",
    };
  }
}

async function cancelConsultOrder(item) {
  const { orderId, corpId } = item;
  try {
    // 获取订单
    const res = await db.collection("consult-order").updateOne(
      { orderId, corpId },
      {
        $set: {
          orderStatus: "cancelled",
          payStatus: "cancelled",
          payResult: "用户取消订单",
          updateTime: Date.now(),
        },
      }
    );
    return {
      success: true,
      message: "取消成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: "取消失败",
    };
  }
}

async function getPatientOrder(item) {
  const { orderId } = item;
  if (!orderId) {
    return {
      success: false,
      message: "参数错误",
    };
  }
  try {
    const [data] = await db
      .collection("consult-order")
      .aggregate([
        { $match: { orderId: orderId } },
        {
          $lookup: {
            from: "diagnostic-record", // 连接到 diagnostic-record 集合
            localField: "orderId", // consult-order 集合中的 orderId 字段
            foreignField: "orderId", // diagnostic-record 集合中的 orderId 字段
            as: "diagnostic", // 返回的联接结果存储在 diagnostic 字段中
          },
        },
        {
          $addFields: {
            diagnostic: {
              $filter: {
                input: "$diagnostic",
                as: "diagnosticRecord",
                cond: { $eq: ["$$diagnosticRecord.status", "PASS"] },
              },
            },
          },
        },
        {
          $addFields: {
            hasPassDiagnostic: { $gt: [{ $size: "$diagnostic" }, 0] },
          },
        },
        {
          $project: {
            diagnostic: 0, // 排除 diagnostic 字段
          },
        },
        {
          $limit: 1,
        },
      ])
      .toArray();
    return {
      success: true,
      data,
      message: "查询成功",
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "查询失败",
    };
  }
}

async function acceptConsultOrder(item) {
  const { orderId, corpId, doctorCode } = item;
  const { consultationDuration } = await getConfig(corpId);
  const expireTime = dayjs().add(consultationDuration, "minute").valueOf();
  try {
    await updateConsultOrderStatus({
      orderId,
      orderStatus: "processing",
      expireTime,
      doctorCode,
      corpId,
    });

    // 接受问诊 发送系统消息
    await tencentIM({
      type: "sendSystemNotification",
      formAccount: doctorCode,
      toAccount: orderId,
      SyncOtherMachine: 2,
      corpId,
      msgBody: [
        {
          MsgType: "TIMCustomElem",
          MsgContent: {
            Data: "ACCEPTCONSULT",
            Ext: "",
          },
        },
      ],
    });

    // 删除定时任务
    await trigger({
      type: "deleteDelayedTask",
      triggerTaskId: orderId,
      corpId,
    });
    // 从新建一个定时任务
    await trigger({
      type: "createDelayedTask",
      triggerTaskId: orderId,
      endTime: expireTime,
      corpId,
    });
    return {
      success: true,
      message: "接诊成功",
      expireTime,
    };
  } catch (err) {
    return {
      success: false,
      message: "接诊失败",
    };
  }
}
// 结束问诊
async function finishConsultOrder(item) {
  // 判断是否有正在审核的单子
  const count = await db.collection("diagnostic-record").countDocuments({
    orderId: item.orderId,
    status: "INIT",
  });
  if (count > 0) {
    return {
      success: false,
      message: "存在正在审核的诊断单，不能结束咨询",
    };
  }
  return await serviceFinishConsultOrder(item);
}
async function serviceFinishConsultOrder(item) {
  let { orderId, doctorCode, corpId } = item;
  try {
    // 获取订单
    if (!doctorCode || !corpId) {
      const order = await db.collection("consult-order").findOne({ orderId });
      doctorCode = order.doctorCode;
      corpId = order.corpId;
      if (
        order.orderStatus === "finished" ||
        order.orderStatus === "cancelled"
      ) {
        return {
          success: false,
          message: "订单已结束",
        };
      }
    }
    // 更新订单状态
    await closeConsultOrder({ orderId, doctorCode, corpId, doctorCode });
    await setDiagnosticExpired({ orderId });
    return {
      success: true,
      message: "完成成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "完成失败",
    };
  }
}

async function closeConsultOrder({ orderId, doctorCode, corpId }) {
  // 删除定时任务
  await trigger({
    type: "deleteDelayedTask",
    triggerTaskId: orderId,
  });
  await tencentIM({
    type: "sendSystemNotification",
    formAccount: doctorCode,
    toAccount: orderId,
    SyncOtherMachine: 1,
    corpId,
    msgBody: [
      {
        MsgType: "TIMCustomElem",
        MsgContent: {
          Data: "FINISHED",
          Ext: "本次问诊已结束，不能继续发消息。",
        },
      },
    ],
  });
  await updateConsultOrderStatus({
    orderId,
    orderStatus: "finished",
    doctorCode,
    corpId,
  });
}

// 完成订单
async function completeConsultOrder(item) {
  let { orderId, doctorCode, sendText, corpId } = item;
  try {
    // 获取订单
    if (doctorCode) {
      const order = await db.collection("consult-order").findOne({ orderId });
      doctorCode = order.doctorCode;
    }
    // 更新订单状态
    await updateConsultOrderStatus({
      orderId,
      orderStatus: "completed",
    });
    // 删除定时任务
    await trigger({
      type: "deleteDelayedTask",
      triggerTaskId: orderId,
    });
    // 发送系统消息
    tencentIM({
      type: "sendSystemNotification",
      formAccount: doctorCode,
      toAccount: orderId,
      corpId,
      SyncOtherMachine: 1,
      msgBody: [
        {
          MsgType: "TIMCustomElem",
          MsgContent: {
            Data: "COMPLETED",
            Desc: sendText,
          },
        },
      ],
    });
    return {
      success: true,
      message: "完成成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "完成失败",
    };
  }
}
// 删除咨询订单
async function deleteConsultOrder(item) {
  const { orderId } = item;
  try {
    // 调用mongo 数据库删除
    const res = await db.collection("consult-order").deleteOne({
      orderId,
    });
    return {
      success: true,
      message: "删除成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: "删除失败",
    };
  }
}
// 更新咨询订单
async function updateConsultOrder(item) {
  const { orderId, params } = item;
  try {
    // 调用mongo 数据库更新
    const res = await db.collection("consult-order").updateOne(
      { orderId },
      {
        $set: {
          ...params,
          updateTime: Date.now(),
        },
      }
    );
    return {
      success: true,
      message: "更新成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: "更新失败",
    };
  }
}

// 获取咨询订单 分页查询 page 页码 pageSize 每页数量 数据库分页
async function getConsultOrder(item) {
  const {
    page,
    pageSize,
    orderId,
    hospitalId,
    patientId,
    idCard,
    orderStatus,
    mergeStatus,
    doctorCode,
    corpId,
    showPassdiagnostic = false,
    hasPassDiagnostic,
  } = item;
  const query = {};
  if (orderId) query.orderId = orderId;
  if (hospitalId) query.hospitalId = hospitalId;
  if (patientId) query.patientId = patientId;
  if (idCard) query.idCard = idCard;
  if (corpId) query.corpId = corpId;
  if (doctorCode) query.doctorCode = doctorCode;
  if (orderStatus) query.orderStatus = orderStatus;
  if (typeof hasPassDiagnostic == "boolean")
    query.hasPassDiagnostic = hasPassDiagnostic;
  if (mergeStatus === "需处理") {
    query.orderStatus = {
      $in: ["pending", "processing", "completed"],
    };
  }
  // 已完成 包括已完成和已取消 两种状态 或者 过期时间小于当前时间 注意 是或的关系 使用 or
  if (mergeStatus === "已处理") {
    query.orderStatus = {
      $in: ["cancelled", "finished"],
    };
  }
  try {
    const pipeList = [];
    if (showPassdiagnostic) {
      pipeList.push(
        {
          $lookup: {
            from: "diagnostic-record", // 连接到 diagnostic-record 集合
            localField: "orderId", // consult-order 集合中的 orderId 字段
            foreignField: "orderId", // diagnostic-record 集合中的 orderId 字段
            as: "diagnostic", // 返回的联接结果存储在 diagnostic 字段中
          },
        },
        {
          $addFields: {
            diagnostic: {
              $filter: {
                input: "$diagnostic",
                as: "diagnosticRecord",
                cond: { $eq: ["$$diagnosticRecord.status", "PASS"] },
              },
            },
          },
        },
        {
          $addFields: {
            hasPassDiagnostic: { $gt: [{ $size: "$diagnostic" }, 0] },
          },
        },
        {
          $project: {
            diagnostic: 0,
          },
        },
        {
          $sort: { createTime: -1 },
        }
      );
    }
    pipeList.push(
      { $match: query },
      { $sort: { createTime: -1 } },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize }
    );
    // 调用mongo 数据库查询
    const res = await db
      .collection("consult-order")
      .aggregate(pipeList)
      .toArray();
    const total = await db.collection("consult-order").countDocuments(query);
    const array = res.map((item) => {
      if (item.payExpireTime < Date.now() && item.payStatus === "pending") {
        item.payStatus = "expired";
        item.payResult = "超时未支付";
      }
      if (
        ["processing", "pending", "completed"].includes(item.orderStatus) &&
        item.expireTime < Date.now()
      ) {
        item.orderStatus = "cancelled";
      }
      return item;
    });
    return {
      success: true,
      message: "查询成功",
      data: array,
      total,
      pages: Math.ceil(total / pageSize),
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "查询失败",
    };
  }
}

// 修改问诊状态
async function updateConsultOrderStatus(item) {
  const {
    orderId,
    orderStatus,
    payStatus,
    expireTime,
    doctorCode,
    corpId,
    reason,
  } = item;
  try {
    let query = {};
    if (orderStatus) query.orderStatus = orderStatus;
    if (payStatus) query.payStatus = payStatus;
    if (expireTime) query.expireTime = expireTime;
    if (typeof reason === "string") query.reason = reason;
    // 当医生开始处理, 开始加过期时间, 过期时间为30分钟
    const res = await db.collection("consult-order").updateOne(
      { orderId },
      {
        $set: {
          ...query,
          updateTime: Date.now(),
        },
      }
    );
    // 删除会话
    if (orderStatus === "cancelled" || orderStatus === "finished") {
      const res = await tencentIM({
        type: "deleteSession",
        fromAccount: doctorCode,
        toAccount: orderId,
        corpId: corpId,
      });
    }
    return {
      success: true,
      message: "更新成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: "更新失败",
    };
  }
}
// 退费并退款接口
async function refundConsultOrder(item) {
  // 判断是否有正在审核的单子
  const count = await db.collection("diagnostic-record").countDocuments({
    orderId: item.orderId,
    status: "INIT",
  });
  if (count > 0) {
    return {
      success: false,
      message: "存在正在审核的诊断单，不能退费",
    };
  }
  // 退费并退诊
  return await refundFeeAndOrder(item);
}
// 退费并退诊 换一个方法名
async function refundFeeAndOrder(item) {
  let { orderId, reason, order } = item;
  try {
    // 1. 更新订单状态
    if (!order) {
      order = await db.collection("consult-order").findOne({ orderId });
      if (!order) {
        return {
          success: false,
          message: "订单不存在",
        };
      }
    }
    if (["cancelled", "finished"].includes(order.orderStatus)) {
      return {
        success: false,
        message: "订单已取消或已完成，不能退费",
      };
    }
    const { doctorCode, corpId } = order;
    // 如果订单已完成，且 审核通过
    if (order.orderStatus === "completed") {
      const count = await db.collection("diagnostic-record").countDocuments({
        orderId,
        status: "PASS",
      });
      if (count > 0) {
        return await serviceFinishConsultOrder({ orderId, doctorCode, corpId });
      }
    }
    // 调用退费接口
    // 2. 退费
    const { success, message } = await zytHis({
      type: "hlwRefund",
      patientId: order.patientId,
      registerId: order.registerId,
      medorgOrderNo: order.medorgOrderNo,
    });
    const payStatus = success ? "refunded" : "refundFailed";
    const payResult = success ? "退费成功" : message;
    // 更新诊断单状态
    await setDiagnosticExpired({ orderId });
    await tencentIM({
      type: "sendSystemNotification",
      formAccount: doctorCode,
      toAccount: orderId,
      SyncOtherMachine: 1,
      corpId,
      msgBody: [
        {
          MsgType: "TIMCustomElem",
          MsgContent: {
            Data: "CANCELLED",
            Desc: "notification",
            Ext: reason,
          },
        },
      ],
    });
    // 3. 更新订单状态
    await updateConsultOrderStatus({
      orderId,
      orderStatus: "cancelled",
      payStatus,
      payResult,
      doctorCode,
      corpId,
      reason: typeof reason == "string" ? reason : "",
    });
    return {
      success: true,
      message: "退费成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "退费失败",
    };
  }
}

// // 问诊开始
async function startConsultOrder(item) {
  const { orderId, corpId, doctorCode } = item;
  const { consultationDuration, acceptDuration } = await getConfig(corpId);
  const expireTime = dayjs().add(acceptDuration, "minute").valueOf();
  try {
    // 更新订单过期时间
    await db
      .collection("consult-order")
      .updateOne({ orderId }, { $set: { expireTime } });
    // 从新建一个定时任务
    await trigger({
      type: "createDelayedTask",
      triggerTaskId: orderId,
      endTime: expireTime,
      corpId,
    });
    await tencentIM({
      type: "sendSystemNotification",
      formAccount: orderId,
      toAccount: doctorCode,
      SyncOtherMachine: 2,
      corpId,
      msgBody: [
        {
          MsgType: "TIMCustomElem",
          MsgContent: {
            Data: "STARTCONSULT",
            Ext: `问诊已开始，本次问诊可持续${consultationDuration}分钟`,
          },
        },
      ],
    });
    // 患者向医生发送一个系统消息，告诉医生问诊已经开始
    setTimeout(() => {
      tencentIM({
        type: "sendSystemNotification",
        formAccount: orderId,
        toAccount: doctorCode,
        SyncOtherMachine: 1,
        corpId,
        msgBody: [
          {
            MsgType: "TIMTextElem",
            MsgContent: {
              Text: "向你发起图文问诊服务，请及时回复处理。感谢！",
            },
          },
        ],
      });
    }, 1000);
    return {
      success: true,
      message: "开始成功",
      expireTime,
    };
  } catch (err) {
    return {
      success: false,
      message: "开始失败",
    };
  }
}

async function refreshOrderPayStatus(params) {
  const { orderId, patientId, registerId, medorgOrderNo } = params;
  if (!orderId || !patientId || !registerId || !medorgOrderNo) {
    return {
      success: false,
      message: "参数错误",
    };
  }
  try {
    const res = await zytHis({
      type: "getPayStatus",
      patientId,
      registerId,
      medorgOrderNo,
    });
    let updateRes = "";

    if (res && res.settlement) {
      updateRes = await db.collection("consult-order").updateOne(
        {
          orderId,
          patientId,
          registerId,
          medorg_order_no: medorgOrderNo,
          settlement: { $exists: false },
        },
        {
          $set: {
            settlement: res.settlement,
          },
        }
      );
    }
    return { ...res, updateRes };
  } catch (e) {
    return { success: false, message: e.message || "刷新失败" };
  }
}

/**
 * 是否有进行中的订单 有则不能创建新的订单
 * @param {patientId}; 患者id
 * @returns {boolean}
 */
async function hasPendingOrder(patientId) {
  try {
    const count = await db.collection("consult-order").countDocuments({
      patientId,
      orderStatus: { $in: ["pending", "processing", "completed"] },
      expireTime: { $gt: Date.now() },
    });
    return count > 0;
  } catch (e) {
    return false;
  }
}

/**
 * 查询医生是否存在进行中的订单
 * @param {*} params
 * @returns
 */
async function doctorHasPendingOrder(params) {
  try {
    const count = await db.collection("consult-order").countDocuments({
      doctorCode: params.doctorCode,
      orderStatus: { $in: ["pending", "processing", "completed"] },
    });
    return { success: true, data: count > 0, message: "查询成功" };
  } catch (e) {
    return { success: false, message: e.message || "查询失败" };
  }
}
// 设置处方诊断过期
async function setDiagnosticExpired(item) {
  const { orderId } = item;
  try {
    const res = await db
      .collection("diagnostic-record")
      .updateMany({ orderId, status: "INIT" }, { $set: { status: "EXPIRED" } });
    return { success: true, message: "操作成功" };
  } catch (e) {
    return { success: false, message: e.message || "操作失败" };
  }
}

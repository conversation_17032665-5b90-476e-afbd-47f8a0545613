let db = null;
exports.addCorpDefaultInfo = async (context, mongodb) => {
  let { corpid } = context;
  db = mongodb;
  // 获取添加默认角色
  await addDefaultRole(corpid);
  // 获取添加默认模板
  await addDefaultTemplate(corpid);
  return "存储成功";
};

async function addDefaultRole(corpId) {
  let res = await db.collection("default-administrator-role").find().toArray();
  const roleList = res;
  const promiseArr = roleList.map((item) => {
    return defaultRoles(item.roleId, corpId);
  });
  await Promise.all(promiseArr);
}

async function defaultRoles(roleId, corpId) {
  const count = await db
    .collection("sys-role")
    .countDocuments({ corpId, roleId });
  if (count === 0) {
    const res = await db
      .collection("default-administrator-role")
      .findOne({ roleId });
    const { _id, ...rest } = res;
    rest.corpId = corpId;
    await db.collection("sys-role").insertOne(rest);
  } else {
    return {};
  }
}

async function addDefaultTemplate(corpId) {
  // 获取平台档案模版
  const currentEnvApp = envConfig.getCurrentEnvApp();
  const client = new MongoClient(currentEnvApp.databaseUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });
  await client.connect();
  const currentEnvDb = client.db(currentEnvApp.databaseName);

  let res = await currentEnvDb
    .collection("platform-template")
    .find({ useType: "public" })
    .toArray();
  const templateList = res;
  const promiseArr = templateList.map((item) => {
    return defaultTemplate(item, corpId);
  });
  await Promise.all(promiseArr);
}

async function defaultTemplate(item, corpId) {
  const count = await db
    .collection("corp-template")
    .countDocuments({ corpId, templateType: item.templateType });
  if (count === 0) {
    const { _id, ...rest } = item;
    rest.corpId = corpId;
    await db.collection("corp-template").insertOne(rest);
  }
}

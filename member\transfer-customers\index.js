const dayjs = require("dayjs");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "transferCustomers":
      return await exports.transferCustomers(content);
    case "batchCreateServiceRecord":
      return await batchCreateServiceRecord(content);
    case "transferServiceRecords":
      return await transferServiceRecords(content);
  }
};

/**
 * 转移客户
 * @param {*} context
 *
 */
exports.transferCustomers = async (context) => {
  const { corpId, customerIds } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!Array.isArray(customerIds) || customerIds.length === 0)
    return { success: false, message: "客户id不能为空" };

  const res = await db.collection("member").find({ corpId, _id: { $in: customerIds } }).limit(100).toArray();
  const memberList = res;

  const bulkOps = memberList.map((item) => {
    let { teamId, personResponsibles, _id } = item;
    personResponsibles = Array.isArray(personResponsibles) ? personResponsibles : [];
    const newTeamIds = getChangeTeamIds(teamId, context);
    const newPersonResponsibles = getChangedPersonResponsibles(personResponsibles, context);
    let query = {
      teamId: newTeamIds,
      personResponsibles: newPersonResponsibles,
    };
    return {
      updateOne: {
        filter: { _id },
        update: { $set: query },
      },
    };
  });

  if (bulkOps.length > 0) {
    await db.collection("member").bulkWrite(bulkOps);
  }

  await transferServiceRecords(context);
  return { success: true, message: "转移成功" };
};

function getChangeTeamIds(teamId, context) {
  let newTeamIds = [];
  if (typeof teamId === "string") {
    newTeamIds = [teamId];
  } else if (Array.isArray(teamId)) {
    newTeamIds = teamId;
  }
  const { currentTeamId, targetTeamId, operationType } = context;
  if (operationType === "share" || operationType === "transferToSameTeam") {
    newTeamIds = Array.from(new Set([...newTeamIds, targetTeamId]));
  } else if (operationType === "transferToOtherTeam") {
    newTeamIds = newTeamIds.filter((i) => i !== currentTeamId);
    newTeamIds = Array.from(new Set([...newTeamIds, targetTeamId]));
  } else if (operationType === "transferToCustomerPool") {
    newTeamIds = newTeamIds.filter((i) => i !== currentTeamId);
  }
  return newTeamIds;
}

function getChangedPersonResponsibles(personResponsibles, context) {
  let newPersonResponsibles = Array.isArray(personResponsibles) ? personResponsibles : [];
  const { currentTeamId, targetUserId, targetTeamId, operationType } = context;
  if (operationType === "share") {
    if (!personResponsibles.some((i) => i.teamId == targetTeamId)) {
      newPersonResponsibles = [...personResponsibles, { corpUserId: targetUserId, teamId: targetTeamId }];
    }
  } else if (operationType === "transferToOtherTeam" || operationType === "transferToSameTeam") {
    newPersonResponsibles = personResponsibles.filter((i) => i.teamId !== currentTeamId);
    if (!newPersonResponsibles.some((i) => i.teamId == targetTeamId)) {
      newPersonResponsibles = [...newPersonResponsibles, { corpUserId: targetUserId, teamId: targetTeamId }];
    }
  } else if (operationType === "transferToCustomerPool") {
    newPersonResponsibles = personResponsibles.filter((i) => i.teamId !== currentTeamId);
  }
  return newPersonResponsibles;
}

async function transferServiceRecords(context) {
  const { corpId, customerIds, currentTeamId, targetTeamId, operationType, creatorUserId, targetUserId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!Array.isArray(customerIds) || customerIds.length === 0)
    return { success: false, message: "客户id不能为空" };

  const res = await db.collection("team").find({ corpId, teamId: { $in: [currentTeamId, targetTeamId] } }).toArray();
  const teamList = res || [];
  const currentTeam = teamList.find((i) => i.teamId === currentTeamId);
  const targetTeam = teamList.find((i) => i.teamId === targetTeamId);

  let taskContent = "";
  if (operationType === "transferToCustomerPool") {
    taskContent = currentTeam ? `&&&${creatorUserId}&&&把客户从"${currentTeam.name}"转移至公共客户池` : `&&&${creatorUserId}&&&把客户转移至公共客户池`;
  } else if (operationType === "share") {
    taskContent = currentTeam ? `&&&${creatorUserId}&&&把客户从"${currentTeam.name}"共享至"${targetTeam.name}"的&&&${targetUserId}&&&` : `&&&${creatorUserId}&&&把客户共享至"${targetTeam.name}"的&&&${targetUserId}&&&`;
  } else {
    taskContent = currentTeam ? `&&&${creatorUserId}&&&把客户从"${currentTeam.name}"转移至"${targetTeam.name}"的&&&${targetUserId}&&&` : `&&&${creatorUserId}&&&把客户转移至"${targetTeam.name}"的&&&${targetUserId}&&&`;
  }

  await batchCreateServiceRecord({
    corpId,
    customerIds,
    currentTeamId,
    operationType,
    creatorUserId,
    currentTeamName: currentTeam ? currentTeam.name : "",
    taskContent,
    transferToTeamIds: [targetTeamId],
  });

  return { success: true, message: "服务记录添加成功" };
}

async function batchCreateServiceRecord(context) {
  const { corpId, customerIds, currentTeamId, operationType, creatorUserId, currentTeamName, taskContent, transferToTeamIds } = context;
  for (const id of customerIds) {
    const res = await db.collection("member").findOne({ _id: id });
    const member = res;

    let params = {
      _id: common.generateRandomString(24),
      taskContent,
      customerId: id,
      executeTeamName: currentTeamName,
      executeTeamId: currentTeamId || "",
      customerUserId: "",
      creatorUserId: creatorUserId,
      executorUserId: creatorUserId,
      corpId,
      executionTime: dayjs().valueOf(),
      eventType: operationType,
      customerName: member.name,
      createTime: dayjs().valueOf(),
      transferToTeamIds,
    };

    const { points, pointTaskId } = await getServicePoint({
      corpId,
      executorUserId: creatorUserId,
      eventType: operationType,
    });

    if (points) params["points"] = points;
    if (pointTaskId) params["pointTaskId"] = pointTaskId;

    await db.collection("service-record").insertOne(params);
  }
}

async function getServicePoint({ corpId, executorUserId, eventType, serviceRate }) {
  if (!executorUserId || !eventType || !corpId) return false;

  let query = { corpId, serviceType: eventType, taskStatus: "enable" };
  if (serviceRate) query["serviceRate"] = serviceRate;

  const res = await db.collection("points-task").find(query).toArray();
  const list = res;

  if (!list || list.length === 0) return false;

  const { taskTriggerTotal = 0, userTriggerlimitTotal = 0, userTriggerTodayLimitCount = 0, _id: pointTaskId, points } = list[0];
  if (typeof points !== "number" || points === 0) return false;

  if (userTriggerlimitTotal > 0) {
    const count = await db.collection("service-record").find({ corpId, executorUserId, eventType, points: { $exists: true }, pointTaskId }).count();
    if (count >= userTriggerlimitTotal) return false;
  }

  if (userTriggerTodayLimitCount > 0) {
    const count = await db.collection("service-record").find({
      corpId,
      eventType,
      points: { $exists: true },
      createTime: { $gte: dayjs().startOf("day").valueOf(), $lte: dayjs().endOf("day").valueOf() },
      pointTaskId,
    }).count();
    if (count >= userTriggerTodayLimitCount) return false;
  }

  if (taskTriggerTotal > 0) {
    const count = await db.collection("service-record").find({ corpId, eventType, points: { $exists: true }, pointTaskId }).count();
    if (count >= taskTriggerTotal) return false;
  }

  return {
    points,
    pointTaskId,
  };
}

enum RateStar {
  'oneStar' = 'oneStar',
  'twoStar' = 'twoStar',
  'threeStar' = 'threeStar',
  'fourStar' = 'fourStar',
  'fiveStar' = 'fiveStar'
}

// 评价标签
interface RateTag {
  readonly _id: string;
  corpId: string; // 机构id
  rateStar: RateStar; // 评分
  text: string; // 评价文本
}


type OneToFive = 1 | 2 | 3 | 4 | 5;
// 评价记录
interface RateRecord {
  readonly _id: string;
  corpId: string; // 机构id
  rate?: OneToFive; // 评价星级
  tags?: string[]; // 评价标签
  words: string, // 评价内容
  createTime: number; // 创建时间
  updateTime?: number; // 更新时间
  expireTime: number; // 过期时间
  userId: string; // 用户id
  avatar: string; // 用户头像
  job: string,// 用户职位
  userName: string; // 用户名
  teamId: string; // 团队id
  customerId: string; // 客户id
  customerName: string; // 客户名称
  externalUserId: string //客户的外部联系人id
}
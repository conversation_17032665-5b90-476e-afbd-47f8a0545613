const aliApi = require("./minialiplay");

exports.main = async (event, db) => {
  switch (event.type) {
    case "getUserInfo":
      return await getUserInfo(event);
    default:
      return {
        success: false,
        message: "参数错误",
      };
  }
};

async function getUserInfo({ code }) {
  try {
    let accessToken = await aliApi.accToken({ code });
    let data = await aliApi.userInfo(accessToken);
    return { success: true, data }
  } catch (e) {
    return { success: false, message: e.message };
  }

}
const data = require("./data");
const util = require("./util");
const cate = require("./survery-cate");
const common = require("../common");
const api = require("../api");
let db = null;
let surveryDB = null;
let answerDB = null;

exports.main = async (context, DB) => {
  db = DB;
  surveryDB = db.collection("survery");
  answerDB = db.collection("survery-answer");
  switch (context.type) {
    case "getList":
      return await getList(context);
    case "getDetail":
      return await getDetail(context);
    case "getAnswer":
      return await getAnswer(context);
    case "answerSurvery":
      return await answerSurvery(context);
    case "getAnswerList":
      return await getAnswerList(context);
    case "getAnswerCount":
      return await getAnswerCount(context);
    case "createRecord":
      return await createSendRecord(context);
    case "setSurvery":
      return await setSurvery(context);
    case "setSurveryStatus":
      return await setSurveryStatus(context);
    case "removeSurvery":
      return await removeSurvery(context);
    case "getSurveryCount":
      return await getSurveryCount(context);
    case "setSurveryCate":
      return await exports.setSurveryCate(context);
    case "getAnswerRecord":
      return await getAnswerRecord(context);
    case "initCorpSurveyCate":
    case "addSurveryCate":
    case "updateSurveryCate":
    case "deleteSurveryCate":
    case "getSurveryCateList":
    case "sortSurveryCate":
      return await cate.main(context, db);
  }
};

// 答题记录
async function answerSurvery(context) {
  try {
    const { corpId, surveryId, memberId, answerId: _id, list, score } = context;
    if (!_id) return { success: false, message: "新增填写记录失败" };
    if (!Array.isArray(list))
      return { success: false, message: "新增填写记录失败" };
    const query = { corpId, surveryId, memberId, _id };
    const record = await answerDB.findOne(query);
    if (record && record.submitTime) {
      return {
        success: false,
        code: "HAS_ANSWERED",
        message: "已经填写过问卷",
      };
    } else if (record) {
      const submitTime = Date.now();
      const data = { submitTime, list };
      if (record.enableScore && typeof score == "number" && score >= 0) {
        data.score = score;
      }
      await answerDB.updateOne(query, {
        $set: { corpId, memberId, submitTime, ...data },
      });
      return { success: true, message: "填写成功" };
    } else {
      return { success: false, message: "未查询到问卷是否填写" };
    }
  } catch (e) {
    return { success: false, message: e.message || "新增填写记录失败" };
  }
}

// 生成发送记录
async function createSendRecord(context) {
  try {
    const {
      memberId,
      corpId,
      surveryId,
      userId: sender,
      customer = "",
    } = context;
    if (!surveryId)
      return { success: false, message: "生成发送失败(surveryId不能为空)" };
    if (!memberId)
      return { success: false, message: "生成发送失败(memberId不能为空)" };
    const survery = await surveryDB.findOne({ corpId, _id: surveryId });
    if (!survery)
      return { success: false, message: "生成发送记录失败(问卷不存在)" };
    const createTime = new Date().getTime();
    const res = await answerDB.insertOne({
      _id: common.generateRandomString(24),
      memberId,
      corpId,
      surveryId,
      sender,
      createTime,
      customer,
      description: survery.description,
      name: survery.name,
      enableScore: Boolean(survery.enableScore),
    });
    return { success: true, message: "生成发送记录成功", id: res.insertedId };
  } catch (e) {
    return { success: false, message: "生成发送记录失败" };
  }
}

// 获取问卷列表
async function getList(context) {
  try {
    const {
      corpId,
      page = 1,
      pageSize = 10,
      name,
      status,
      showCount = false,
      cateIds,
    } = context;
    const query = { corpId };
    if (status) query.status = status;
    if (Array.isArray(cateIds)) query.cateId = { $in: cateIds };
    if (typeof name === "string" && name.trim()) {
      query.name = { $regex: ".*" + name.trim() + ".*", $options: "i" };
    }
    const total = await surveryDB.countDocuments(query);
    const list = await surveryDB
      .find(query)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    const ids = list.map((i) => i._id);
    if (ids.length && showCount) {
      const countList = await db
        .collection("survery-answer")
        .aggregate([
          { $match: { surveryId: { $in: ids } } },
          {
            $group: {
              _id: "$surveryId",
              sendCount: { $sum: 1 },
              answerCount: {
                $sum: { $cond: { if: "$submitTime", then: 1, else: 0 } },
              },
            },
          },
        ])
        .toArray();
      list.forEach((i) => {
        const { sendCount = 0, answerCount = 0 } =
          countList.find((j) => j._id === i._id) || {};
        i.sendCount = sendCount;
        i.answerCount = answerCount;
      });
    }
    return { success: true, message: "获取问卷列表成功", list, total };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 获取问卷详情
async function getDetail(context) {
  try {
    const { id: _id } = context;
    if (!_id) return { success: false, message: "问卷Id不能为空" };
    const survery = await surveryDB.findOne({ _id });
    if (survery) {
      return { success: true, message: "获取问卷详情成功", data: survery };
    }
    return { success: false, message: "未查询到问卷" };
  } catch (e) {
    return { success: false, message: "获取问卷详情失败" };
  }
}

// 查询答题记录
async function getAnswer(context) {
  try {
    const { corpId, surveryId, memberId, answerId: _id } = context;
    const query = { corpId, surveryId, memberId };
    if (_id) query._id = _id;
    const record = await answerDB.findOne(query);
    return { success: true, message: "查询答题记录成功", record };
  } catch (e) {
    return { success: false, message: "查询答题记录失败" };
  }
}

// 查询问卷统计数据
async function getAnswerCount(context) {
  try {
    const { corpId, surveryId } = context;
    const count = await answerDB.countDocuments({ corpId, surveryId });
    const answerCount = await answerDB.countDocuments({
      corpId,
      surveryId,
      submitTime: { $exists: true },
    });
    const unAnswerCount = count - answerCount;
    return {
      success: true,
      message: "查询问卷统计数据成功",
      answerCount,
      unAnswerCount,
    };
  } catch (e) {
    return { success: false, message: "查询问卷统计数据失败" };
  }
}

// 查询答题记录列表
async function getAnswerList(context) {
  try {
    const {
      corpId,
      surveryId,
      page,
      pageSize,
      customer = "",
      answered = true,
      minScore,
      maxScore,
    } = context;
    const query = { corpId, surveryId };
    if (typeof customer === "string" && customer.trim()) {
      query.customer = { $regex: ".*" + customer.trim() + ".*", $options: "i" };
    }
    query.submitTime = { $exists: Boolean(answered) };
    const score = [];
    if (typeof minScore === "number") score.push({ $gte: minScore });
    if (typeof maxScore === "number") score.push({ $lte: maxScore });
    if (score.length) query.score = { $and: score };
    const list = await answerDB
      .find(query)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    const total = await answerDB.countDocuments(query);
    return {
      success: true,
      message: "查询答题记录成功",
      list: Array.isArray(list) ? list : [],
      total,
    };
  } catch (e) {
    return { success: false, message: "查询答题记录失败" };
  }
}

// 设置问卷
async function setSurvery(context) {
  const { _id, corpId, userId, ...payload } = context;
  if (!corpId || !userId)
    return { success: false, message: "机构id或者成员id不能为空" };
  try {
    const { valid, message, survery } = util.getSurveryData(payload);
    if (!valid) return { success: false, message };
    if (_id) {
      await surveryDB.updateOne(
        { _id },
        {
          $set: {
            ...survery,
            updateBy: userId,
            updateTime: new Date().getTime(),
          },
        }
      );
      return { success: true, message: "修改问卷成功" };
    } else {
      await surveryDB.insertOne({
        _id: common.generateRandomString(24),
        ...survery,
        corpId,
        createBy: userId,
        createTime: new Date().getTime(),
      });
      return { success: true, message: "新增问卷成功" };
    }
  } catch (e) {
    console.log(e);
    return { success: true, message: `${_id ? "修改" : "新增"}问卷成功` };
  }
}

// 设置问卷状态
async function setSurveryStatus(context) {
  const { _id, status } = context;
  if (!data.SurveryStatus[status])
    return { success: false, message: "未知问卷状态" };
  try {
    await surveryDB.updateOne({ _id }, { $set: { status } });
    return { success: true, message: "设置问卷状态成功" };
  } catch (e) {
    return { success: false, message: "设置问卷状态失败" };
  }
}

// 删除问卷
async function removeSurvery(context) {
  const { _id, corpId } = context;
  if (!_id) return { success: false, message: "问卷id不能为空" };
  try {
    const record = await surveryDB
      .aggregate([
        { $match: { _id, corpId } },
        {
          $lookup: {
            from: "survery-answer",
            localField: "_id",
            foreignField: "surveryId",
            as: "answers",
          },
        },
      ])
      .toArray();
    if (record.length && record[0].answers.length === 0) {
      await surveryDB.deleteOne({ _id });
      return { success: true, message: "问卷删除成功" };
    } else if (record.length && record[0].answers.length > 0) {
      return { success: false, message: "该问卷已有答题记录,不支持删除" };
    } else {
      return { success: false, message: "问卷未找到" };
    }
  } catch (e) {
    return { success: false, message: "问卷删除失败" };
  }
}

// 获取问卷数量
async function getSurveryCount(context) {
  const { corpId, cateIds } = context;
  if (!corpId) return { success: false, msg: "缺少corpId参数" };
  if (!Array.isArray(cateIds))
    return { success: false, msg: "缺少cateIds参数" };
  try {
    const total = await surveryDB.countDocuments({
      corpId,
      cateId: { $in: cateIds },
    });
    return { success: true, data: total };
  } catch (e) {
    return { success: false, msg: e.message };
  }
}

// 设置问卷分类
exports.setSurveryCate = async (context) => {
  const { _id, corpId, cateId } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const time = new Date().getTime();
    const article = await surveryDB.findOne({ _id, corpId });
    if (!article) return { success: false, message: "问卷不存在" };
    const { success, message } = await cate.main(
      {
        type: "ifCateExist",
        corpId,
        cateId,
      },
      db
    );
    if (!success) return { success, message };
    await surveryDB.updateOne({ _id }, { $set: { cateId, updateTime: time } });
    return { success: true, message: "更新问卷分类成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
};

// 获取答题记录
async function getAnswerRecord(context) {
  const { corpId, surveryId, id, source, unionid, realUnionid, userId } =
    context;
  try {
    if (
      !corpId ||
      !surveryId ||
      !id ||
      !source ||
      !unionid ||
      !realUnionid ||
      !userId
    )
      return { success: false, message: "获取问卷信息失败" };
    const record = await answerDB.findOne({
      corpId,
      thirdId: id,
      source,
      sender: userId,
      surveryId,
    });
    if (record)
      return { success: true, message: "获取答题记录成功", data: record };
    const survery = await surveryDB.findOne({ corpId, _id: surveryId });
    if (!survery) return { success: false, message: "未查询到问卷" };
    const exist = await corpMemberExist(corpId, userId);
    if (!exist) return { success: false, message: "未查询到员工信息" };
    const customer = await getCustomerByUnionId(corpId, unionid, realUnionid);
    if (!customer) return { success: false, message: "未查询到客户信息" };
    const payload = {
      _id: common.generateRandomString(24),
      memberId: customer._id,
      corpId,
      source,
      thirdId: id,
      surveryId,
      sender: userId,
      createTime: Date.now(),
      customer: customer.name,
      description: survery.description,
      name: survery.name,
      enableScore: Boolean(survery.enableScore),
    };
    const res = await answerDB.insertOne(payload);
    return {
      success: true,
      message: "生成发送记录成功",
      data: { ...payload, _id: res.insertedId },
    };
  } catch (e) {
    return { success: false, message: e.message || "生成发送记录失败" };
  }
}

// 根据unionid获取客户信息
async function getCustomerByUnionId(corpId, unionid, realUnionid) {
  let res = await api.getMemberApi({
    type: "getCustomerByUnionId",
    corpId,
    unionid,
    realUnionid,
  });
  if (res) {
    const { data } = res;
    return data;
  }
  return null;
}

// 判断企业成员是否存在
async function corpMemberExist(corpId, userid) {
  let res = await api.getCorpApi({
    type: "corpMemberExist",
    corpId,
    userid,
  });
  if (res) {
    const { exist } = res;
    return exist;
  }
  return false;
}

const { generateRandomString } = require('../../common.js');
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getRoles":
      return await this.getRoles(content);
    case "getRolesByRoleId":
      return await this.getRolesByRoleId(content);
    case "addRole":
      return await this.addRole(content);
    case "updateRole":
      return await this.updateRole(content);
    case "deleteRole":
      return await this.deleteRole(content);
  }
};

// 获取角色列表
exports.getRoles = async (context) => {
  let { corpId } = context;
  let res = await db.collection("sys-role").find({ corpId: corpId }).toArray();
  return {
    success: true,
    message: "获取成功",
    data: res,
  };
};

// 根据角色ID获取角色
exports.getRolesByRoleId = async (context) => {
  let { corpId, roleIds } = context;
  let res = await db.collection("sys-role").find({ corpId: corpId, _id: { $in: roleIds } }).toArray();
  return {
    success: true,
    message: "获取成功",
    data: res,
  };
};

// 新增角色
exports.addRole = async (context) => {
  try {
    let { params } = context;
    params._id = generateRandomString(24);
    let res = await db.collection("sys-role").insertOne(params);
    return {
      success: true,
      message: "新增成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 更新角色
exports.updateRole = async (context) => {
  try {
    let { id, params } = context;
    let res = await db.collection("sys-role").updateOne({ _id: id }, { $set: params });
    return {
      success: true,
      message: "更新成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 删除角色
exports.deleteRole = async (context) => {
  try {
    let { id } = context;
    let res = await db.collection("sys-role").deleteOne({ _id: id });
    return {
      success: true,
      message: "删除成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

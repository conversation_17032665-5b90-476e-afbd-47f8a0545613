const dayjs = require("dayjs");
const common = require("../../common");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "createPointsTask":
      return await createPointsTask(content);
    case "getPointsTaskList":
      return await getPointsTaskList(content);
    case "updatePointsTask":
      return await updatePointsTask(content);
    case "getPointsTask":
      return await getPointsTask(content);
    case "deletePointsTask":
      return await deletePointsTask(content);
  }
};

/**
 * 创建积分任务
 * @param {Object} context
 * @returns {Object}
 */
async function createPointsTask(context) {
  const {
    corpId,
    creator,
    points,
    triggerCondition,
    taskTriggerTotal,
    userTriggerlimitTotal,
    userTriggerTodayLimitCount,
    taskStatus = "enable",
    serviceRate,
    serviceType,
  } = context;

  const query = {
    _id: common.generateRandomString(24),
    corpId,
    creator,
    points,
    serviceType,
    taskTriggerTotal,
    userTriggerlimitTotal,
    userTriggerTodayLimitCount,
    taskStatus,
    serviceRate,
    triggerCondition,
    createTime: dayjs().valueOf(),
  };

  const total = await db.collection("points-task").countDocuments({
    corpId,
    serviceType,
    taskStatus: "enable",
    serviceRate,
  });

  if (total > 0) {
    return {
      success: false,
      message: "该服务类型已存在",
    };
  }

  try {
    await db.collection("points-task").insertOne(query);
    return {
      success: true,
      message: "积分记录成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "积分记录失败",
    };
  }
}

/**
 * 获取积分任务列表
 * @param {Object} params
 * @returns {Object}
 */
async function getPointsTaskList(params) {
  const { corpId, page, pageSize, taskStatus } = params;

  const query = { corpId };
  if (taskStatus) query.taskStatus = taskStatus;

  const total = await db.collection("points-task").countDocuments(query);
  const pages = Math.ceil(total / pageSize);

  const data = await db
    .collection("points-task")
    .aggregate([
      { $match: query },
      { $sort: { createTime: -1 } },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize },
      {
        $lookup: {
          from: "service-record",
          localField: "_id",
          foreignField: "pointTaskId",
          as: "serviceRecords",
        },
      },
      {
        $addFields: {
          serviceRecordCount: { $size: "$serviceRecords" },
        },
      },
      {
        $project: {
          serviceRecords: 0,
        },
      },
    ])
    .toArray();

  return {
    success: true,
    list: data,
    total,
    pages,
    message: "获取积分任务列表成功",
  };
}

/**
 * 更新积分任务
 * @param {Object} params
 * @returns {Object}
 */
async function updatePointsTask(params) {
  const { corpId, id, taskStatus, serviceRate, serviceType } = params;

  if (!corpId || !id || !taskStatus) {
    return { success: false, message: "缺少必要的字段" };
  }

  const query = {
    taskStatus,
    updateTime: dayjs().valueOf(),
  };

  if (taskStatus === "enable") {
    const total = await db.collection("points-task").countDocuments({
      corpId,
      taskStatus: "enable",
      serviceRate,
      serviceType,
    });

    if (total > 0) {
      return {
        success: false,
        message: "存在相同服务类型的积分规则启用中",
      };
    }
  }

  try {
    await db.collection("points-task").updateOne({ _id: id }, { $set: query });
    return {
      success: true,
      message: "更新积分任务成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "更新积分任务失败",
    };
  }
}

/**
 * 获取积分任务
 * @param {Object} params
 * @returns {Object}
 */
async function getPointsTask(params) {
  const { corpId, id } = params;

  if (!corpId || !id) {
    return { success: false, message: "缺少必要的字段" };
  }

  const data = await db.collection("points-task").findOne({ corpId, _id: id });

  return {
    success: true,
    data,
    message: "获取积分任务成功",
  };
}

/**
 * 删除积分任务
 * @param {Object} params
 * @returns {Object}
 */
async function deletePointsTask(params) {
  const { corpId, id } = params;

  if (!corpId || !id) {
    return { success: false, message: "缺少必要的字段" };
  }

  try {
    await db.collection("points-task").deleteOne({ _id: id });
    return {
      success: true,
      message: "删除积分任务成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "删除积分任务失败",
    };
  }
}

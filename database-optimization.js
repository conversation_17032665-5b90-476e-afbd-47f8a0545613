/**
 * 数据库索引优化脚本
 * 用于优化consult-record集合的查询性能
 */
const { connectToMongoDB, getDatabase } = require('./mongodb');

async function createOptimizedIndexes() {
  try {
    await connectToMongoDB();
    const db = await getDatabase(process.env.CONFIG_DB_NAME || 'ytk-customer-service');
    const collection = db.collection('consult-record');

    console.log('开始创建索引...');

    // 1. 核心查询索引 - corpId + createTime（用于基础查询和排序）
    await collection.createIndex(
      { corpId: 1, createTime: -1 },
      { 
        name: 'corpId_createTime_-1',
        background: true 
      }
    );
    console.log('✓ 创建 corpId + createTime 索引');

    // 2. 客户查询索引 - corpId + customerId
    await collection.createIndex(
      { corpId: 1, customerId: 1 },
      { 
        name: 'corpId_customerId',
        background: true 
      }
    );
    console.log('✓ 创建 corpId + customerId 索引');

    // 3. 时间范围查询索引 - corpId + triageTime
    await collection.createIndex(
      { corpId: 1, triageTime: 1 },
      { 
        name: 'corpId_triageTime',
        background: true 
      }
    );
    console.log('✓ 创建 corpId + triageTime 索引');

    // 4. 接诊时间查询索引 - corpId + receptionTime
    await collection.createIndex(
      { corpId: 1, receptionTime: 1 },
      { 
        name: 'corpId_receptionTime',
        background: true 
      }
    );
    console.log('✓ 创建 corpId + receptionTime 索引');

    // 5. 复合索引 - 常用筛选条件组合
    await collection.createIndex(
      { 
        corpId: 1, 
        visitStatus: 1, 
        createTime: -1 
      },
      { 
        name: 'corpId_visitStatus_createTime',
        background: true 
      }
    );
    console.log('✓ 创建 corpId + visitStatus + createTime 索引');

    // 6. 项目查询索引 - corpId + projectIds
    await collection.createIndex(
      { corpId: 1, projectIds: 1 },
      { 
        name: 'corpId_projectIds',
        background: true 
      }
    );
    console.log('✓ 创建 corpId + projectIds 索引');

    // 7. 团队查询索引 - corpId + teamId
    await collection.createIndex(
      { corpId: 1, teamId: 1 },
      { 
        name: 'corpId_teamId',
        background: true 
      }
    );
    console.log('✓ 创建 corpId + teamId 索引');

    // 8. 人员相关索引
    await collection.createIndex(
      { corpId: 1, receptionPersonUserId: 1 },
      { 
        name: 'corpId_receptionPersonUserId',
        background: true 
      }
    );
    console.log('✓ 创建 corpId + receptionPersonUserId 索引');

    await collection.createIndex(
      { corpId: 1, triagePersonUserId: 1 },
      { 
        name: 'corpId_triagePersonUserId',
        background: true 
      }
    );
    console.log('✓ 创建 corpId + triagePersonUserId 索引');

    // 9. 消费状态索引
    await collection.createIndex(
      { corpId: 1, consumeCount: 1 },
      { 
        name: 'corpId_consumeCount',
        background: true 
      }
    );
    console.log('✓ 创建 corpId + consumeCount 索引');

    // 10. 交易状态索引
    await collection.createIndex(
      { corpId: 1, tradeAmount: 1 },
      { 
        name: 'corpId_tradeAmount',
        background: true 
      }
    );
    console.log('✓ 创建 corpId + tradeAmount 索引');

    // 为member集合也创建必要的索引
    const memberCollection = db.collection('member');
    
    // member表的客户查询索引
    await memberCollection.createIndex(
      { _id: 1, name: "text" },
      { 
        name: 'id_name_text',
        background: true 
      }
    );
    console.log('✓ 创建 member name 文本索引');

    await memberCollection.createIndex(
      { mobile: 1, phone1: 1, phone2: 1, phone3: 1 },
      { 
        name: 'phone_numbers',
        background: true,
        sparse: true
      }
    );
    console.log('✓ 创建 member 电话号码索引');

    // 为to-do-events集合创建索引
    const todoCollection = db.collection('to-do-events');
    await todoCollection.createIndex(
      { customerId: 1, eventStatus: 1, executeTeamId: 1 },
      { 
        name: 'customerId_eventStatus_executeTeamId',
        background: true 
      }
    );
    console.log('✓ 创建 to-do-events 复合索引');

    console.log('\n所有索引创建完成！');

    // 显示当前索引状态
    const indexes = await collection.indexes();
    console.log('\nconsult-record 集合当前索引：');
    indexes.forEach(index => {
      console.log(`- ${index.name}: ${JSON.stringify(index.key)}`);
    });

  } catch (error) {
    console.error('创建索引失败:', error);
  }
}

// 分析查询性能的函数
async function analyzeQueryPerformance() {
  try {
    const db = await getDatabase(process.env.CONFIG_DB_NAME || 'ytk-customer-service');
    const collection = db.collection('consult-record');

    console.log('\n开始分析查询性能...');

    // 分析常用查询的执行计划
    const sampleQuery = {
      corpId: "sample_corp_id",
      createTime: { $gte: Date.now() - 30 * 24 * 60 * 60 * 1000 } // 最近30天
    };

    const explain = await collection.find(sampleQuery).explain('executionStats');
    
    console.log('查询执行统计：');
    console.log(`- 执行时间: ${explain.executionStats.executionTimeMillis}ms`);
    console.log(`- 检查的文档数: ${explain.executionStats.totalDocsExamined}`);
    console.log(`- 返回的文档数: ${explain.executionStats.totalDocsReturned}`);
    console.log(`- 使用的索引: ${explain.executionStats.winningPlan.inputStage?.indexName || '无索引'}`);

  } catch (error) {
    console.error('性能分析失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  (async () => {
    await createOptimizedIndexes();
    await analyzeQueryPerformance();
    process.exit(0);
  })();
}

module.exports = {
  createOptimizedIndexes,
  analyzeQueryPerformance
}; 
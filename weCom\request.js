const axios = require("axios");
exports.main = async (url, data, type) => {
  if (type === "POST") {
    return await post(url, data);
  } else {
    return await get(url);
  }
};

function get(url) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axios.get(url);
      console.log(`请求URL: ${url}`);
      console.log(`请求成功: ${JSON.stringify(response.data)}`);
      resolve(response.data);
    } catch (error) {
      console.error(error);
      reject(error);
      // throw error;
    }
  });
}

function post(url, data) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axios.post(url, data);
      console.log(`请求URL: ${url}`);
      console.log(`请求成功: ${JSON.stringify(response.data)}`);
      resolve(response.data);
    } catch (error) {
      console.log(`请求失败: ${error}`);
      console.error(error);
      reject(error);
      // throw error;
    }
  });
}

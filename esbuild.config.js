const { build } = require("esbuild");
const envFile = `.env.${process.env.NODE_ENV || 'development'}`;
require('dotenv').config({ path: envFile });

// 获取当前环境并构建输出文件名
const currentEnv = process.env.NODE_ENV || 'development';
const outputFileName = `./dist/bundle.${currentEnv}.js`;

const difineOption = Object.keys(process.env).reduce((prev, key) => {
  if (key.startsWith('CONFIG_')) {
    prev[`process.env.${key}`] = JSON.stringify(process.env[key]);
  }
  return prev;
}, { 'process.env.CONFIG_IS_BUNDLED': '"YES"' })

build({
  entryPoints: ["./index.js"], // 入口文件
  outfile: outputFileName, // 动态设置输出文件名
  platform: "node", // Node.js 平台
  bundle: true, // 打包依赖
  minify: true, // 压缩代码
  sourcemap: false, // 不生成 sourcemap 文件
  target: ["node16", "node19"], // 目标运行时版本
  external: ["fs", "path"], // 排除 Node.js 内置模块
  define: difineOption
}).catch(() => process.exit(1));

let db = null;
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "addChatMsg":
      return await addChatMsg(item);
    case "getChatRecord":
      return await getChatRecord(item);
    default:
      return {
        success: false,
        message: "未找到请求类型",
      };
  }
};
// 把聊天消息存储在数据库中 im-chat-msg
async function addChatMsg(item) {
  try {
    const { params } = item;
    const res = await db.collection("im-chat-msg").insertOne(params);
    return {
      success: true,
      message: "添加成功",
      res,
    };
  } catch (err) {
    return {
      success: false,
      message: "添加失败",
    };
  }
}

// 根据 From_Account 和  To_Account 查询聊天记录 传入的  doctorNo  和 orderId 有可能是  From_Account 或者是 To_Account 获取到的聊天记录是双方的聊天记录
async function getChatRecord(item) {
  const { doctorNo, orderId } = item;
  try {
    const res = await db
      .collection("im-chat-msg")
      .find({
        $or: [
          { From_Account: doctorNo, To_Account: orderId },
          { From_Account: orderId, To_Account: doctorNo },
        ],
      })
      .toArray();
    return {
      success: true,
      message: "查询成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "查询失败",
    };
  }
}

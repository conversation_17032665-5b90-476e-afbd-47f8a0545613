const request = require("../request.js");
const accessToken = require("../token/index.js");
exports.main = async (context) => {
  let { agentid, touser, content, access_token = "" } = context;
  if (!access_token) access_token = await accessToken.getToken(context);
  let url = `https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=${access_token}`;
  let params = {
    touser: touser,
    msgtype: "text",
    agentid: agentid,
    text: {
      content: content,
    },
    safe: 0,
    enable_id_trans: 0,
    enable_duplicate_check: 0,
    duplicate_check_interval: 1800,
  };
  let res = await request.main(url, params, "POST");
  if (res.errcode === 0) {
    return {
      success: true,
      message: "发送成功",
    };
  } else {
    return {
      success: false,
      errcode: res.errcode,
      message: res.errmsg,
    };
  }
};

const dayjs = require("dayjs");
const request = require("../request");
const corpToken = require("../corp-token");
const { generateRandomString } = require("../utils");
const { ObjectId } = require("mongodb");

exports.getCorpInfoById = async (item, db) => {
  const { env, id, corpId } = item;
  if (!id) {
    return {
      success: false,
      message: "机构id未传",
    };
  }
  try {
    const corpInfo = await db.collection("corp").findOne({ _id: id });
    if (!corpInfo) {
      return {
        success: false,
        message: "机构不存在",
      };
    }
    const counts = await getCustomtorAndMemberCount(corpId, db);
    Object.assign(corpInfo, counts);
    return {
      success: true,
      message: "获取成功",
      data: corpInfo,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.getCorpMemberList = async (item, db) => {
  const { env, corpId, page, pageSize } = item;
  if (!corpId) {
    return {
      success: false,
      message: "机构id未传",
    };
  }
  const total = await db.collection("corp-member").countDocuments({
    corpId,
  }); //总数
  const pages = Math.ceil(total / pageSize);
  const data = await db
    .collection("corp-member")
    .find({
      corpId,
    })
    .sort({ createTime: -1 })
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .toArray();
  const provider_access_token = await corpToken.getProviderAccessToken();
  const promiseArr = data.map((item) => {
    return getAccountInfo(item, provider_access_token);
  });
  const accountInfoList = await Promise.all(promiseArr);
  const list = data.map((item) => {
    let account = accountInfoList.find((i) => i.userid === item.userid);
    if (account) {
      const { errcode, errmsg, ...rest } = account;
      Object.assign(item, rest);
    }
    return item;
  });
  return {
    success: true,
    message: "获取成功",
    total: total,
    pages: pages,
    size: pageSize,
    data: list,
  };
};

// 获取账号详情
async function getAccountInfo(item, provider_access_token) {
  const { corpId, userid } = item;
  let url = `https://qyapi.weixin.qq.com/cgi-bin/license/get_active_info_by_user?provider_access_token=${provider_access_token}`;
  const params = {
    corpid: corpId,
    userid,
  };
  let res = await request.main(url, params, "POST");
  if (res.errcode === 0) {
    res["userid"] = userid;
    return res;
  } else {
    return {};
  }
}

exports.removeCorpMember = async (item, db) => {
  const { corpId, userid, env } = item;
  if (!corpId) {
    return {
      success: false,
      message: "机构ID未传",
    };
  }

  if (!userid) {
    return {
      success: false,
      message: "员工ID未传",
    };
  }

  try {
    await db.collection("corp-member").deleteMany({
      corpId,
      userid,
    });
    return {
      success: true,
      message: "删除成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "删除失败",
    };
  }
};

exports.addCorp = async (item, db) => {
  const { env, params } = item;
  const { corpId, corpName, corpType } = params;
  if (!corpName) {
    return {
      success: true,
      message: "请输入机构名称",
    };
  }
  if (!corpId) {
    return {
      success: true,
      message: "请输入机构ID",
    };
  }
  const corpCount = await db.collection("corp").countDocuments({ corpName });
  if (corpCount > 0) {
    return {
      success: false,
      message: "机构名称不能重复",
    };
  }
  const corpIdCount = await db.collection("corp").countDocuments({ corpId });
  if (corpIdCount > 0) {
    return {
      success: false,
      message: "机构ID不能重复",
    };
  }
  const query = {
    corpId,
    corp_name: corpName,
    corpType,
    createTime: dayjs().valueOf(),
  };
  try {
    const result = await db.collection("corp").insertOne(query);
    return {
      success: true,
      id: result.insertedId,
      message: "新增成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "新增失败",
    };
  }
};

exports.updateCorp = async (item, db) => {
  const { id, params, env } = item;
  const { corp_name, corpType } = params;
  let query = {};
  if (corp_name) query["corp_name"] = corp_name;
  if (corpType) query["corpType"] = corpType;
  try {
    await db.collection("corp").updateOne({ _id: id }, { $set: query });
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "更新失败",
    };
  }
};

exports.getCorpList = async (item, db) => {
  const { env, page, pageSize, params } = item;
  const { corpName, corpType } = params;
  try {
    let query = {};
    if (corpName) query["corp_name"] = { $regex: corpName, $options: "i" };
    if (corpType) query["corpType"] = corpType;

    const total = await db.collection("corp").countDocuments(query); //总数
    const pages = Math.ceil(total / pageSize);
    let corpList = await db
      .collection("corp")
      .find(query, {
        projection: {
          intentedProjectList: 0,
          customerSourceList: 0,
          diseases: 0,
          tags: 0,
          corpFileds: 0,
          auth_info: 0,
        },
      })
      .sort({ createTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();

    const countListFuc = corpList.map((item) => {
      return getCustomtorAndMemberCount(item.corpId, db);
    });
    let counts = [];
    for (let i = 0; i < countListFuc.length; i += 10) {
      const promiseArr = countListFuc.slice(i, i + 10);
      const list = await Promise.all(promiseArr);
      counts = counts.concat(list);
    }
    corpList = corpList.map((item) => {
      const countObj = counts.find((e) => e.corpId === item.corpId);
      Object.assign(item, countObj);
      return item;
    });
    return {
      total: total,
      pages: pages,
      size: pageSize,
      data: corpList,
      success: true,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

async function getCustomtorAndMemberCount(corpId, db) {
  const customtorCount = await db.collection("member").countDocuments({
    corpId,
  });
  const corpMemberCount = await db.collection("corp-member").countDocuments({
    corpId,
    open_userid: { $exists: true, $ne: null },
  });
  return {
    customtorCount,
    corpMemberCount,
    corpId,
  };
}

exports.getSuperAdministratorForCorpId = async (item, db) => {
  const { env, corpId } = item;
  try {
    const data = await db
      .collection("corp-member")
      .find({
        corpId: corpId,
        roleType: "superAdmin",
      })
      .toArray();
    return {
      success: true,
      message: "获取成功",
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

exports.addSuperAdministrator = async (item, db) => {
  const { env, corpId, userId, permanentCode = "", superAdminName } = item;
  let token = "";
  let roleId = "";
  let userInfo = {
    _id: generateRandomString(24),
  };
  if (permanentCode) {
    const suite_access_token = await suiteAccessToken(db);
    token = await corpToken.getCorpToken(
      suite_access_token,
      corpId,
      permanentCode
    );
  }
  const role = await getAdminRole(corpId, db);
  if (!role) {
    roleId = await addAminRole(corpId, db);
  } else {
    roleId = role._id;
  }
  userInfo["corpId"] = corpId;
  userInfo["roleType"] = "superAdmin";
  userInfo["superAdminName"] = superAdminName;
  // 获取到密文userId
  const res = await userid_to_openuserid(token, userId);
  if (res.errcode !== 0) {
    return {
      success: false,
      message: "新增失败",
      res,
    };
  }
  let open_userid = Array.isArray(res.open_userid_list)
    ? res.open_userid_list[0].open_userid
    : "";
  if (!open_userid) {
    userInfo["userid"] = userId;
  }
  const user = await getCorpUser(corpId, open_userid, userId, db);
  // 判断userId是否存在
  if (user) {
    //roleIds添加roleId
    if (userInfo.roleIds && Array.isArray(userInfo.roleIds)) {
      userInfo.roleIds.push(roleId);
    } else {
      userInfo.roleIds = [roleId];
    }
    userInfo["open_userid"] = open_userid;
    //userInfo.roleIds 去重
    userInfo.roleIds = Array.from(new Set(userInfo.roleIds));

    // 移除 _id 字段，因为它是不可变的
    const { _id, ...updateFields } = userInfo;
    let res = await db.collection("corp-member").updateMany(
      {
        corpId,
        $or: [{ userid: open_userid }, { userid: userId }],
      },
      { $set: updateFields }
    );
    return {
      success: true,
      message: "新增成功",
      res,
      userInfo,
    };
  } else {
    // 获取到用户详情
    userInfo["roleIds"] = [roleId];
    const res = await getCorpMemberInfoByUserId(token, open_userid);
    Object.assign(userInfo, res);
    await db.collection("corp-member").insertOne(userInfo);
  }
  return {
    success: true,
    message: "新增成功",
    user,
  };
};

// 跟机构设置机构管理员角色
async function addAminRole(corpId, db) {
  const defaultRole = await getDefaultAdministratorRole(db);
  const { menuList, roleName, roleId } = defaultRole;
  // 确保 _id 保持为字符串类型
  const customId = generateRandomString(24);
  const documentToInsert = {
    _id: customId,
    menuList,
    roleName,
    corpId,
    roleId,
  };
  // 使用原始插入方法，避免 ObjectId 转换
  let res = await db.collection("sys-role").insertOne(documentToInsert);
  return customId;
}

async function getDefaultAdministratorRole(db) {
  let item = await db.collection("default-administrator-role").findOne({
    roleId: "admin",
  });
  return item;
}

// 超级管理员角色
async function getAdminRole(corpId, db) {
  const role = await db.collection("sys-role").findOne({
    corpId,
    roleId: "admin",
  });
  return role;
}

async function getCorpUser(corpId, openUserId, userid, db) {
  let data = await db.collection("corp-member").findOne({
    corpId,
    $or: [{ userid: openUserId }, { userid: userid }],
  });
  return data;
}

async function userid_to_openuserid(token, userId) {
  if (!token) return "";
  let url = `https://qyapi.weixin.qq.com/cgi-bin/batch/userid_to_openuserid?access_token=${token}`;
  let res = await request.main(
    url,
    {
      userid_list: [userId],
    },
    "POST"
  );
  return res;
}

async function getCorpMemberInfoByUserId(token, userId) {
  if (!token) return "";
  let url = `https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=${token}&userid=${userId}`;
  let res = await request.main(url, null, "GET");
  return res.errcode === 0 ? res : {};
}

async function suiteAccessToken(db) {
  let res = await db.collection("weComToken").findOne({ type: "suiteToken" });
  return res ? res.suite_token : "";
}

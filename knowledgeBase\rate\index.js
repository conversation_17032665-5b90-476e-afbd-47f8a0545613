const RateTag = require("./rate-tag");
const RateRecord = require("./rate-record");

exports.main = async (event, db) => {
  switch (event.type) {
    case "getCorpRateConfig":
    case "addRateTag":
    case "deleteRateTag":
    case "updateRateTagText":
      return await RateTag.main(event, db);
    case "addRateRecord":
    case "submitRateRecord":
    case "getRateRecord":
    case "getCorpRateRecord":
    case "getMemberRateRecord":
    case "getCorpRateStats":
    case 'getRateRecordCount':
      return await RateRecord.main(event, db);
  }
};

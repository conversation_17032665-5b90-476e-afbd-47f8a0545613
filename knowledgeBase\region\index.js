const { generateRandomString } = require('../../common');

exports.getRegion = async (context, db) => {
  const { code } = context;
  try {
    // 根据是否有code参数来决定查询条件
    const query = code ? { parent_code: code } : { type: 0 };
    
    // 查询数据库
    const data = await db.collection("china-region")
      .find(query, { projection: { code: 1, name: 1, _id: 0 } })
      .toArray();
    
    return { success: true, message: "查询成功", data };
  } catch (e) {
    return { success: false, message: e.message };
  }
};

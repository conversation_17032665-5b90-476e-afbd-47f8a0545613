let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "selectedChatMember":
      return await exports.addOrUpdate(content);
    case "getSelectedChatMember":
      return await exports.getChatMember(content);
  }
};

// 添加或更新选中的聊天成员
exports.addOrUpdate = async (context) => {
  let {
    externalUserId,
    memberId,
    userId,
    corpId,
    teamId = "",
    payload = {},
  } = context;
  if (!externalUserId || !userId || !corpId) return { success: false };

  try {
    const collection = db.collection("selected-chat-member");
    const query = { externalUserId, userId, corpId };
    const update = { $set: { memberId, teamId, payload } };
    const options = { upsert: true };

    // 使用 upsert 选项，如果文档不存在则插入新文档
    await collection.updateOne(query, update, options);

    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 获取选中的聊天成员
exports.getChatMember = async (context) => {
  const { externalUserId, corpId, userId } = context;
  if (!externalUserId || !corpId || !userId) return { success: false };

  try {
    const collection = db.collection("selected-chat-member");
    const query = { externalUserId, userId, corpId };
    const res = await collection.find(query).toArray();

    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

interface ProjectPackage {
  name: string, // 套餐名称
  deptIds: string[], // 关联科室id
  desc: string, // 套餐描述
  hasValidDays: boolean, // 是否有有效期
  validDays: number, // 有效期(天)
  enableDiscount: boolean, // 是否启用折扣
  minDiscount: number, // 折扣最小值
  packageType: string, // 套餐类型 fixed:固定套餐，free:自由组合套餐
  packagePrice: string, // 套餐价格 （自由组合套餐必填）
  projects: ProjectPackageItem[], // 套餐项目
  projectNumber: number, // 套餐项目数量 固定套餐为projects.length，自由组合套餐介于 0 与 projects.length
}

interface ProjectPackageItem {
  _id: string, // 项目id
  projectName: string, // 项目名称
  price: number, // 项目价格
  number: number, // 项目数量
  discount: number, // 折扣 (0-10)且不能低于 ProjectPackage.minDiscount
  // totalPrice:string, // 总价
  required: boolean, // 是否必选 ProjectPackage.packageType 为 fixed 时必填, 为 free 时自由勾选
}

const dayjs = require("dayjs");
const { ObjectId } = require("mongodb"); // 添加 ObjectId 导入
let db = "";
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "getDrugInfo":
      return await getDrugInfo(item);
    case "deleteDrugInfo":
      return await deleteDrugInfo(item);
    case "updateDrugInfo":
      return await updateDrugInfo(item);
    case "addDrugInfo":
      return await addDrugInfo(item);
  }
};
// 药品库信息
async function getDrugInfo(item) {
  let { name, _id, pinyin_code, keyword, insuranceCodes } = item;
  let fuzzyQuery = null;
  let query = {};
  if (typeof name === 'string') query.name = new RegExp(name.trim(), "i");
  if (_id) query._id = new ObjectId(_id);
  if (typeof pinyin_code === 'string') query.pinyin_code = new RegExp(pinyin_code.trim(), "i");
  if (Array.isArray(insuranceCodes)) {
    query.insurance_code = { $in: insuranceCodes };
  }
  const page = Number.isInteger(item.page) && item.page > 0 ? item.page : 1;
  const pageSize = Number.isInteger(item.pageSize) && item.pageSize > 0 ? item.pageSize : 15;
  if (typeof keyword === 'string' && keyword.trim()) {
    const arr = [
      { name: new RegExp(keyword.trim(), "i") },
      { barcode: keyword.trim() },
      { pinyin_code: new RegExp(keyword.trim(), "i") },
      { product_id_str: keyword.trim() },
    ];
    fuzzyQuery = { $or: arr };
  }
  const finalQuery = fuzzyQuery ? { $and: [query, fuzzyQuery] } : query;
  try {
    // 调用mongo 数据库查询
    const res = await db.collection("drug-info").find(finalQuery).skip((page - 1) * pageSize)
      .limit(pageSize).toArray();
    const total = await db.collection("drug-info").countDocuments(finalQuery);
    return {
      success: true,
      message: "查询成功",
      data: res,
      total,
      pages: Math.ceil(total / pageSize)
    };
  } catch (err) {
    return {
      success: false,
      message: "查询失败",
    };
  }
}

// 删除药品信息
async function deleteDrugInfo(item) {
  const { _id } = item;
  try {
    // 调用mongo 数据库删除
    const res = await db.collection("drug-info").deleteOne({
      _id: new ObjectId(_id),
    });
    return {
      success: true,
      message: "删除成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "删除失败",
    };
  }
}

// 更新药品信息
async function updateDrugInfo(item) {
  const { updateData, _id } = item;
  try {
    // 调用mongo 数据库更新
    const res = await db
      .collection("drug-info")
      .updateOne(
        { _id: new ObjectId(_id) },
        { $set: { ...updateData, updateTime: Date.now() } }
      );
    return {
      success: true,
      message: "更新成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "更新失败",
    };
  }
}
//新增药品信息
/**
 * pinyin_code：拼音操作码
    product_id：药店货品ID
name：名称
specification：规格
manufacturer：厂家
unit：单位
category：分类
barcode：条形码
insurance_code：医保国码
dosage：用量
dosage_unit：用量单位
frequency：频率
days：天数
administration_method：给药方式
recommended_quantity：建议销售数量
 * @param {*} item 
 * @returns 
 */
async function addDrugInfo(item) {
  const { name } = item;
  try {
    // 调用mongo 数据库新增
    const res = await db.collection("drug-info").insertOne({
      name,
      createTime: Date.now(),
    });
    return {
      success: true,
      data: res,
      message: "新增成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "新增失败",
    };
  }
}

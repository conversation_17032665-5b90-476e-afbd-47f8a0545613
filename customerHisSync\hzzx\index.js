const request = require("../request");
const dayjs = require("dayjs");

// 测试环境和其他环境的 URL 配置
const hzUrl = "http://192.168.0.130:3000/patient";
exports.getHzzxCustomerArchive = async (event) => {
  const { idCard, mobile, idNo } = event;
  let url = "";
  try {
    if (idCard) {
      url = `${hzUrl}?sql=CALL SQLUser.Pat_Info('${idCard}','1')`;
    } else if (mobile) {
      url = `${hzUrl}?sql=CALL SQLUser.Pat_Info('${mobile}','2')`;
    } else if (idNo) {
      url = `${hzUrl}?sql=CALL SQLUser.Pat_Info('${idNo}','3')`;
    } else {
      return { success: false, message: "请输入患者信息", list: [] };
    }
    const res = await request.main(url, null, "GET");
    if (res && Array.isArray(res) && res.length > 0) {
      const list = res.map((i) => convertToCustomerParams(i));
      return { success: true, message: "获取成功", list };
    } else {
      return { success: false, message: "获取失败", list: [] };
    }
  } catch (error) {
    console.error("Error in getHzzxCustomerArchive:", error);
    return { success: false, message: "获取失败，发生错误", list: [] };
  }
};

exports.getHzzxInHospitalRecord = async (event) => {
  const { idNo, startTime, endTime } = event;
  const url = `${hzUrl}?sql=CALL SQLUser.In_PatInfo('${idNo}', '${startTime}', '${endTime}')`;
  try {
    const res = await request.main(url, null, "GET");
    let list = [];
    if (res && Array.isArray(res) && res.length > 0) {
      list = res.map((i) => convertToInHospitalParams(i));
      // 通过hospitalizationId 对数据进行去重
      list = list.reduce((acc, cur) => {
        if (!acc.some((i) => i.hospitalizationId === cur.hospitalizationId)) {
          acc.push(cur);
        }
        return acc;
      }, []);
      return { success: true, message: "获取成功", list };
    } else {
      return { success: false, message: "为查询到数据", list: [] };
    }
  } catch (error) {
    console.error("Error in getHzzxInHospitalRecord:", error);
    return { success: false, message: "获取失败，发生错误", list: [] };
  }
};

exports.getHzzxOutHospitalRecord = async (event) => {
  const { idNo, startTime, endTime } = event;
  const url = `${hzUrl}?sql=CALL SQLUser.Pat_OutInfo('${idNo}','${startTime}','${endTime}')`;
  try {
    const res = await request.main(url, null, "GET");
    let list = [];
    if (res && Array.isArray(res) && res.length > 0) {
      list = res.map((i) => convertToOutHospitalParams(i));
      // 通过 visitId 对数据进行去重
      list = list.reduce((acc, cur) => {
        if (!acc.some((i) => i.visitId === cur.visitId)) {
          acc.push(cur);
        }
        return acc;
      }, []);
      return { success: true, message: "获取成功", list };
    } else {
      return { success: false, message: "为查询到数据", list: [] };
    }
  } catch (error) {
    console.error("Error in getHzzxOutHospitalRecord:", error);
    return { success: false, message: "获取失败，发生错误", list: [] };
  }
};
exports.getHzzxFeeRecord = async (event) => {
  const { idNo, startTime, endTime } = event;
  const url = `${hzUrl}?sql=CALL SQLUser.FYDD_Info('${idNo}','${startTime}','${endTime}')`;
  try {
    const res = await request.main(url, null, "GET");
    let list = [];
    if (res && Array.isArray(res) && res.length > 0) {
      list = res.map((i) => convertToFeeParams(i));
      list = filterAndBalance(list);
      return { success: true, message: "获取成功", list };
    } else {
      return { success: false, message: "未查询到缴费单", list: [] };
    }
  } catch (error) {
    console.error("Error in getHzzxFeeRecord:", error);
    return { success: false, message: "获取失败，发生错误", list: [] };
  }
};

exports.getHzzxPackageRecord = async (event) => {
  const { idNo, startTime, endTime } = event;
  const url = `${hzUrl}?sql=CALL SQLUser.TCZL_Info('${idNo}','${startTime}','${endTime}')`;
  try {
    const res = await request.main(url, null, "GET");
    let list = [];
    if (res && Array.isArray(res) && res.length > 0) {
      list = res;
      return { success: true, message: "获取成功", list };
    } else {
      return { success: false, message: "为查询到数据", list: [] };
    }
  } catch (error) {
    console.error("Error in getHzzxPackageRecord:", error);
    return { success: false, message: "获取失败，发生错误", list: [] };
  }
};

// 开单记录 退费金额处理
function filterAndBalance(arr) {
  // 创建一个 Map 来存储每个 projectId 和 orderNo 对应的正负数信息
  const map = new Map();

  // 遍历每一项，填充 map
  arr.forEach((item) => {
    const { projectId, orderNo, projectCount, totalFee } = item;
    const key = `${projectId}||${orderNo}`;

    // 如果 map 中没有该键，则初始化
    if (!map.has(key)) {
      map.set(key, { positive: [], negative: [] });
    }

    const projectCountNum = parseFloat(projectCount);
    const totalFeeNum = parseFloat(totalFee);

    if (projectCountNum > 0) {
      // 正数，直接存入 positive 数组
      map.get(key).positive.push(item);
    } else if (projectCountNum < 0) {
      // 负数，转为正数并存入 negative 数组
      item.projectCount = Math.abs(projectCountNum); // 修改负数为正数
      item.totalFee = Math.abs(totalFeeNum); // 修改负数费用为正数
      map.get(key).negative.push(item);
    }
  });

  // 最终结果数组
  const result = [];

  // 遍历 map 中每个项目，处理正负数的抵消
  map.forEach(({ positive, negative }) => {
    let i = 0,
      j = 0;

    // 对正数和负数进行排序，以便匹配
    positive.sort((a, b) => b.projectCount - a.projectCount);
    negative.sort((a, b) => b.projectCount - a.projectCount);

    // 比较正数和负数进行抵消
    while (i < positive.length && j < negative.length) {
      if (positive[i].projectCount == negative[j].projectCount) {
        // 如果正负数相等，则抵消
        i++;
        j++;
      } else if (positive[i].projectCount > negative[j].projectCount) {
        // 如果正数更大，则保留正数，移动正数指针
        result.push(positive[i]);
        i++;
      } else {
        // 如果负数更大，跳过负数，移动负数指针
        j++;
      }
    }

    // 如果还有正数没有被抵消，添加到结果中
    while (i < positive.length) {
      result.push(positive[i]);
      i++;
    }
  });
  return result;
}

// 转换患者档案信息
function convertToCustomerParams(patient) {
  return {
    name: patient.name,
    cardType: "身份证",
    idCard: patient.card_id,
    mobile: patient.mobile,
    address: patient.address,
    customerNumber: patient.registration_to,
  };
}

// 转换出院记录信息
function convertToOutHospitalParams(patient) {
  return {
    customerNumber: patient.registration_to,
    name: patient.name,
    cardType: patient.card_type,
    idCard: patient.card_id,
    visitId: patient.visit_id,
    visitTime: patient.visit_time,
    deptName: patient.visit_dept,
    doctor: patient.doctor_name,
    diagnosisId: patient.diagnosis_id,
    diagnosisName: patient.diagnosis_name,
    chiefComplaint: patient.chief_complaint,
    disposalPlan: patient.deal_with,
    amount: patient.fee,
  };
}

// 转换住院记录信息
function convertToInHospitalParams(patient) {
  return {
    customerNumber: patient.registration_to,
    name: patient.name,
    cardType: patient.card_type,
    idCard: patient.card_id,
    hospitalizationId: patient.hospitalization_id,
    hospitalizationStatus: patient.hospitalization_status,
    inhosDate: patient.admission_time,
    outhosDate: patient.discharge_time,
    hospitalizationDays: patient.hospitalization_days,
    deptName: patient.dept_name,
    wardName: patient.wardname,
    bedNo: patient.bed_no,
    doctor: patient.doctor_name,
    nurseName: patient.nurse_name,
    diagnosisId: patient.diagnosis_id,
    diagnosisName: patient.diagnosis_name,
    operationId: patient.operation_id,
    operation: patient.operation_name,
    operationDate: patient.operation_date,
    amount: patient.fee,
  };
}

// I：住院；O：门诊；E：急诊；H：体检
// 转换费用记录信息
function convertToFeeParams(patient) {
  const billTypeObj = {
    I: "住院",
    O: "门诊",
    E: "急诊",
    H: "体检",
  };
  return {
    customerNumber: patient.registration_to,
    name: patient.name,
    cardType: patient.card_type,
    idCard: patient.card_id,
    orderNo: patient.order_no,
    billDept: patient.bill_dept,
    billDoctor: patient.bill_doctor,
    doctorAdvice: patient.doctor_advice,
    billTime: patient.bill_time,
    orderName: patient.order_name,
    orderStatus: patient.order_status,
    projectPrice: patient.project_price,
    projectCount: patient.project_count,
    totalFee: patient.total_fee,
    payTime: patient.pay_time,
    payment: patient.payment,
    projectName: patient.project_name,
    projectId: patient.project_id,
    payType: patient.pay_type,
    billId: patient.bill_id,
    settlementTime: patient.settlement_time,
    billAdm: patient.bill_adm,
    billType: billTypeObj[patient.bill_type],
  };
}

const jwt = require('../../../utils/jwt.js'); // 引入 JWT 工具模块
const whitelist = require('./white-list.js'); // 假设你有一个白名单模块

const jwtAuthMiddleware = (req, res, next) => {
  const authorization = req.headers.authorization || ''; // 获取请求头中的 Authorization 字段
  const paths = req.url.split("?")[0].split("/") // 获取请求路径并分割成数组
  const type = req.body.type || ''; // 获取请求体中的 type 字段
  paths.push(type); // 将 type 添加到路径数组中
  const path = paths.filter(Boolean).join("."); // 将路径数组转换为字符串，使用点号连接
  if (whitelist.includes(path)) {
    return next();
  }
  // 检查 Authorization 字段是否存在
  if (!authorization) {
    return res.status(401).json({ error: "Missing authorization header" });
  }
  const token = jwt.decodeJWT(authorization);
  const notExpired = (token.exp * 1000 - Date.now()) > 0; // 计算 token 是否过期
  if (notExpired) {
    next()
  } else {
    return res.status(401).json({ error: "Invalid Token" });
  }
};

exports.jwtAuthMiddleware = jwtAuthMiddleware;
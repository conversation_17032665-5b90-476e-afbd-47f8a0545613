const dayjs = require("dayjs");
const common = require("../../common");
let db = null;
exports.main = async (content, mongodb) => {
  db = mongodb;
  switch (content.type) {
    case "getBehaviorStatistics":
      return await exports.getBehaviorStatistics(content);
    case "addBehaviorStatistics":
      return await exports.addBehaviorStatistics(content);
  }
};

exports.addBehaviorStatistics = async (content) => {
  //插入
  const { data } = content;
  try {
    await db.collection("behavior-statistics").insertOne({
      ...data,
      _id: common.generateRandomString(24),
    });
    return {
      success: true,
      message: "添加成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

exports.getBehaviorStatistics = async (content) => {
  const { corpId, userId, dates, statisticType } = content;
  let totalStats = {}; // 存储每个 userId 的统计总和
  let batchSize = 1000; // 每批次查询 1000 条数据
  let skip = 0; // 跳过的记录数
  let hasMoreData = true; // 判断是否还有更多数据
  let query = {
    corpId,
  };
  if (dates && Array.isArray(dates) && dates.length === 2) {
    const startTime = dayjs(dates[0]).startOf("day").unix();
    const endTime = dayjs(dates[1]).endOf("day").unix();
    query["statTime"] = { $gte: startTime, $lte: endTime };
    console.log("startTime", startTime);
    console.log("endTime", endTime);
  }
  if (userId) query.userId = userId;
  try {
    while (hasMoreData) {
      const res = await db
        .collection("behavior-statistics")
        .aggregate([
          { $match: query },
          { $sort: { statTime: 1 } },
          { $skip: skip },
          { $limit: batchSize },
          {
            $group: {
              _id: `$${statisticType}`,
              newContactCount: { $sum: "$newContactCount" },
              negativeFeedbackCnt: { $sum: "$negativeFeedbackCnt" },
              chatCnt: { $sum: "$chatCnt" },
              messageCnt: { $sum: "$messageCnt" },
              totalReplyTime: {
                $sum: {
                  $cond: [{ $gt: ["$avgReplyTime", 0] }, "$avgReplyTime", 0],
                },
              },
              totalReplyPercentage: {
                $sum: {
                  $cond: [
                    { $gt: ["$replyPercentage", 0] },
                    "$replyPercentage",
                    0,
                  ],
                },
              },
              countReplyTime: {
                $sum: { $cond: [{ $gt: ["$avgReplyTime", 0] }, 1, 0] },
              },
              countReplyPercentage: {
                $sum: { $cond: [{ $gt: ["$replyPercentage", 0] }, 1, 0] },
              },
            },
          },
          {
            $project: {
              _id: 1,
              newContactCount: 1,
              negativeFeedbackCnt: 1,
              chatCnt: 1,
              messageCnt: 1,
              avgReplyTime: {
                $cond: [
                  { $gt: ["$countReplyTime", 0] },
                  { $divide: ["$totalReplyTime", "$countReplyTime"] },
                  0,
                ],
              },
              replyPercentage: {
                $cond: [
                  { $gt: ["$countReplyPercentage", 0] },
                  {
                    $divide: ["$totalReplyPercentage", "$countReplyPercentage"],
                  },
                  0,
                ],
              },
            },
          },
        ])
        .toArray();

      if (res.length > 0) {
        res.forEach((item) => {
          if (!totalStats[item._id]) {
            totalStats[item._id] = {
              newContactCount: 0,
              replyPercentageTotal: 0,
              replyCount: 0,
              negativeFeedbackCnt: 0,
              chatCnt: 0,
              messageCnt: 0,
              avgReplyTimeTotal: 0,
              replyTimeCount: 0,
            };
          }
          totalStats[item._id].newContactCount += item.newContactCount;
          totalStats[item._id].replyPercentageTotal += item.replyPercentage;
          totalStats[item._id].replyCount += item.replyPercentage > 0 ? 1 : 0;
          totalStats[item._id].negativeFeedbackCnt += item.negativeFeedbackCnt;
          totalStats[item._id].chatCnt += item.chatCnt;
          totalStats[item._id].messageCnt += item.messageCnt;
          totalStats[item._id].avgReplyTimeTotal += item.avgReplyTime;
          totalStats[item._id].replyTimeCount += item.avgReplyTime > 0 ? 1 : 0;
        });

        Object.keys(totalStats).forEach((id) => {
          if (totalStats[id].replyCount > 0) {
            totalStats[id].replyPercentage =
              totalStats[id].replyPercentageTotal / totalStats[id].replyCount;
          } else {
            totalStats[id].replyPercentage = 0;
          }

          if (totalStats[id].replyTimeCount > 0) {
            totalStats[id].avgReplyTime =
              totalStats[id].avgReplyTimeTotal / totalStats[id].replyTimeCount;
          } else {
            totalStats[id].avgReplyTime = 0;
          }
          delete totalStats[id].replyPercentageTotal;
          delete totalStats[id].replyCount;
          delete totalStats[id].avgReplyTimeTotal;
          delete totalStats[id].replyTimeCount;
        });
        skip += batchSize;
      } else {
        hasMoreData = false;
      }
    }
    return {
      success: true,
      totalStats: totalStats,
      message: "统计成功",
    };
  } catch (error) {
    console.error("统计失败:", error);
    return {
      success: false,
      message: "统计失败",
    };
  }
};

exports.updateTodayBehaviorData = (item) => {
  const { corpId, userId, ChangeType } = item;
  const startTime = dayjs().startOf("day").unix();
};

let db = null;
const common = require("../../common");

// 递归获取所有下级科室ID
async function getAllChildDeptIds(parentId, corpId) {
  const childDepts = await db
    .collection("hlw-dept-list")
    .find({ parentId: parentId, corpId })
    .toArray();

  let allChildIds = childDepts.map(dept => dept._id);

  // 递归获取每个子科室的下级科室
  for (const childDept of childDepts) {
    const grandChildIds = await getAllChildDeptIds(childDept._id, corpId);
    allChildIds = allChildIds.concat(grandChildIds);
  }

  return allChildIds;
}
const utils = require("./utils");

exports.main = async (context, mongodb) => {
  db = mongodb;
  switch (context.type) {
    case "addHospitalArea":
      return await addHospitalArea(context);
    case "updateHospitalArea":
      return await updateHospitalArea(context);
    case "deleteHospitalArea":
      return await deleteHospitalArea(context);
    case "getHospitalAreaList":
      return await getHospitalAreaList(context);
    case "getHospitalAreaById":
      return await getHospitalAreaById(context);
    case "checkAreaIdExists":
      return await checkAreaIdExists(context);
    case "sortHospitalAreaList":
      return await sortHospitalAreaList(context);
  }
};

// 新增院区
async function addHospitalArea(context) {
  let { corpId, params } = context;
  const { areaName, areaId, areaDescription, sort = 0 } = params;
  
  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (typeof areaName !== "string" || areaName.trim() === "")
    return { success: false, message: "院区名称不能为空" };
  if (areaName.trim().length > 50)
    return { success: false, message: "院区名称不能超过50个字符" };
  if (typeof areaId !== "string" || areaId.trim() === "")
    return { success: false, message: "院区ID不能为空" };
  if (areaId.trim().length > 20)
    return { success: false, message: "院区ID不能超过20个字符" };
  if (typeof sort !== "number" || sort < 0)
    return { success: false, message: "排序值必须为非负数字" };
  
  try {
    // 检查院区ID是否已存在
    const existingArea = await db
      .collection("hlw-hospital-area")
      .findOne({ areaId: areaId.trim(), corpId });
    
    if (existingArea) {
      return { success: false, message: "院区ID已存在，请使用其他ID" };
    }
    
    // 检查院区名称是否已存在
    const existingName = await db
      .collection("hlw-hospital-area")
      .findOne({ areaName: areaName.trim(), corpId });
    
    if (existingName) {
      return { success: false, message: "院区名称已存在，请使用其他名称" };
    }
    
    const _id = common.generateRandomString(24);
    const hospitalArea = {
      _id,
      corpId,
      areaName: areaName.trim(),
      areaId: areaId.trim(),
      areaDescription: areaDescription ? areaDescription.trim() : "",
      sort: sort,
      createTime: Math.floor(Date.now() / 1000),
      updateTime: Math.floor(Date.now() / 1000),
      status: 1, // 1: 正常, 0: 禁用
    };
    
    const result = await db.collection("hlw-hospital-area").insertOne(hospitalArea);
    
    if (result.insertedId) {
      return {
        success: true,
        message: "院区添加成功",
        data: hospitalArea
      };
    } else {
      return { success: false, message: "院区添加失败" };
    }
  } catch (error) {
    console.error("添加院区时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 更新院区
async function updateHospitalArea(context) {
  let { corpId, params } = context;
  const { _id, areaName, areaId, areaDescription, status, sort } = params;
  
  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "院区_id不能为空" };
  
  try {
    // 验证院区是否存在
    const existingArea = await db
      .collection("hlw-hospital-area")
      .findOne({ _id: _id, corpId });
    
    if (!existingArea) {
      return { success: false, message: "院区不存在" };
    }
    
    const updateData = {
      updateTime: Math.floor(Date.now() / 1000)
    };
    
    // 更新院区名称
    if (areaName !== undefined) {
      if (typeof areaName !== "string" || areaName.trim() === "")
        return { success: false, message: "院区名称不能为空" };
      if (areaName.trim().length > 50)
        return { success: false, message: "院区名称不能超过50个字符" };
      
      // 检查新名称是否与其他院区重复
      if (areaName.trim() !== existingArea.areaName) {
        const nameExists = await db
          .collection("hlw-hospital-area")
          .findOne({ 
            areaName: areaName.trim(), 
            corpId,
            _id: { $ne: _id }
          });
        
        if (nameExists) {
          return { success: false, message: "院区名称已存在，请使用其他名称" };
        }
      }
      
      updateData.areaName = areaName.trim();
    }
    
    // 更新院区ID
    if (areaId !== undefined) {
      if (typeof areaId !== "string" || areaId.trim() === "")
        return { success: false, message: "院区ID不能为空" };
      if (areaId.trim().length > 20)
        return { success: false, message: "院区ID不能超过20个字符" };
      
      // 检查新ID是否与其他院区重复
      if (areaId.trim() !== existingArea.areaId) {
        const idExists = await db
          .collection("hlw-hospital-area")
          .findOne({ 
            areaId: areaId.trim(), 
            corpId,
            _id: { $ne: _id }
          });
        
        if (idExists) {
          return { success: false, message: "院区ID已存在，请使用其他ID" };
        }
      }
      
      updateData.areaId = areaId.trim();
    }
    
    // 更新院区介绍
    if (areaDescription !== undefined) {
      updateData.areaDescription = areaDescription ? areaDescription.trim() : "";
    }
    
    // 更新状态
    if (status !== undefined) {
      if (![0, 1].includes(status)) {
        return { success: false, message: "状态值无效" };
      }
      updateData.status = status;
    }
    
    // 更新排序
    if (sort !== undefined) {
      if (typeof sort !== "number" || sort < 0) {
        return { success: false, message: "排序值必须为非负数字" };
      }
      updateData.sort = sort;
    }
    
    const result = await db
      .collection("hlw-hospital-area")
      .updateOne(
        { _id: _id, corpId },
        { $set: updateData }
      );
    
    if (result.modifiedCount > 0) {
      return { success: true, message: "院区更新成功" };
    } else {
      return { success: false, message: "没有数据被更新" };
    }
  } catch (error) {
    console.error("更新院区时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 删除院区
async function deleteHospitalArea(context) {
  let { corpId, params } = context;
  const { _id } = params;
  
  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "院区_id不能为空" };
  
  try {
    // 验证院区是否存在
    const existingArea = await db
      .collection("hlw-hospital-area")
      .findOne({ _id: _id, corpId });
    
    if (!existingArea) {
      return { success: false, message: "院区不存在" };
    }

    // 获取该院区下的所有科室
    const linkedDepts = await db
      .collection("hlw-dept-list")
      .find({ areaId: _id, corpId })
      .toArray();

    let deletedDeptCount = 0;
    let updatedMemberCount = 0;

    // 如果有关联的科室，先删除这些科室及其下级科室
    if (linkedDepts.length > 0) {
      console.log(`院区 ${_id} 下有 ${linkedDepts.length} 个科室，准备级联删除`);

      // 获取所有需要删除的科室ID（包括下级科室）
      let allDeptIdsToDelete = [];

      for (const dept of linkedDepts) {
        // 获取该科室的所有下级科室
        const childDeptIds = await getAllChildDeptIds(dept._id, corpId);
        allDeptIdsToDelete.push(dept._id, ...childDeptIds);
      }

      // 去重
      allDeptIdsToDelete = [...new Set(allDeptIdsToDelete)];

      console.log(`准备删除 ${allDeptIdsToDelete.length} 个科室:`, allDeptIdsToDelete);

      // 批量删除所有相关科室
      const deptDeleteResult = await db
        .collection("hlw-dept-list")
        .deleteMany({
          _id: { $in: allDeptIdsToDelete },
          corpId
        });

      deletedDeptCount = deptDeleteResult.deletedCount;

      // 清理相关人员的科室关联
      const memberUpdateResult = await db
        .collection("corp-member")
        .updateMany(
          {
            corpId,
            hlwDeptIds: { $in: allDeptIdsToDelete }
          },
          {
            $pullAll: { hlwDeptIds: allDeptIdsToDelete },
            $unset: allDeptIdsToDelete.reduce((unsetObj, deptId) => {
              unsetObj[`hlwSortOrder.${deptId}`] = "";
              return unsetObj;
            }, {}),
            $set: { updateTime: new Date().getTime() }
          }
        );

      updatedMemberCount = memberUpdateResult.modifiedCount;
    }

    // 删除院区
    const result = await db
      .collection("hlw-hospital-area")
      .deleteOne({ _id: _id, corpId });

    if (result.deletedCount > 0) {
      const message = deletedDeptCount > 0
        ? `院区删除成功，同时删除了 ${deletedDeptCount} 个科室`
        : "院区删除成功";

      return {
        success: true,
        message: message,
        data: {
          deletedDeptCount: deletedDeptCount,
          updatedMemberCount: updatedMemberCount
        }
      };
    } else {
      return { success: false, message: "院区删除失败" };
    }
  } catch (error) {
    console.error("删除院区时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 获取院区列表
async function getHospitalAreaList(context) {
  let { corpId, params = {} } = context;
  const { 
    page = 1, 
    pageSize = 20, 
    status, 
    keyword,
    sortBy = "sort",
    sortOrder = 1 
  } = params;
  
  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  
  try {
    const query = { corpId };
    
    // 状态筛选
    if (status !== undefined && status !== null && status !== "") {
      query.status = status;
    }
    
    // 关键词搜索（院区名称或院区ID）
    if (keyword && keyword.trim()) {
      const keywordRegex = new RegExp(keyword.trim(), "i");
      query.$or = [
        { areaName: keywordRegex },
        { areaId: keywordRegex },
        { areaDescription: keywordRegex }
      ];
    }
    
    // 构建排序条件
    const sort = {};
    sort[sortBy] = sortOrder;
    
    // 分页计算
    const skip = (page - 1) * pageSize;
    
    // 获取总数
    const total = await db.collection("hlw-hospital-area").countDocuments(query);
    
    // 获取数据
    const areas = await db
      .collection("hlw-hospital-area")
      .find(query)
      .sort(sort)
      .skip(skip)
      .limit(pageSize)
      .toArray();
    
    return {
      success: true,
      data: {
        list: areas,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  } catch (error) {
    console.error("获取院区列表时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 根据ID获取单个院区信息
async function getHospitalAreaById(context) {
  let { corpId, params } = context;
  const { _id } = params;
  
  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!_id) return { success: false, message: "院区_id不能为空" };
  
  try {
    const area = await db
      .collection("hlw-hospital-area")
      .findOne({ _id: _id, corpId });
    
    if (area) {
      return { success: true, data: area };
    } else {
      return { success: false, message: "院区不存在" };
    }
  } catch (error) {
    console.error("获取院区信息时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 检查院区ID是否存在
async function checkAreaIdExists(context) {
  let { corpId, params } = context;
  const { areaId, excludeId } = params;
  
  // 参数验证
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!areaId || typeof areaId !== "string") 
    return { success: false, message: "院区ID不能为空" };
  
  try {
    const query = { 
      areaId: areaId.trim(), 
      corpId 
    };
    
    // 如果提供了excludeId，则排除该记录（用于编辑时检查）
    if (excludeId) {
      query._id = { $ne: excludeId };
    }
    
    const existingArea = await db
      .collection("hlw-hospital-area")
      .findOne(query);
    
    return { 
      success: true, 
      data: { 
        exists: !!existingArea,
        message: existingArea ? "院区ID已存在" : "院区ID可用"
      }
    };
  } catch (error) {
    console.error("检查院区ID时发生错误:", error);
    return { success: false, message: "系统错误，请稍后重试" };
  }
}

// 批量排序院区
async function sortHospitalAreaList(context) {
  const { corpId, params } = context;
  const { sortData } = params;
  
  if (!corpId) return { success: false, message: "机构id不能为空" };
  
  const arr = Array.isArray(sortData)
    ? sortData.filter(
        (i) =>
          i._id && typeof i.sort === "number" && Number.isInteger(i.sort) && i.sort >= 0
      )
    : [];
  
  if (arr.length === 0) return { success: false, message: "参数错误" };
  
  try {
    const bulkOps = arr.map((item) => ({
      updateOne: {
        filter: { corpId, _id: item._id },
        update: { $set: { sort: item.sort, updateTime: Math.floor(Date.now() / 1000) } },
      },
    }));
    
    const res = await db.collection("hlw-hospital-area").bulkWrite(bulkOps);
    return { success: true, message: "排序更新成功", data: res };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

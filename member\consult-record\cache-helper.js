/**
 * 查询缓存辅助模块
 * 用于缓存频繁查询的数据以提升性能
 */
const NodeCache = require('node-cache');

// 创建缓存实例，默认TTL为10分钟
const cache = new NodeCache({ 
  stdTTL: 600,  // 10分钟
  checkperiod: 120, // 每2分钟清理过期缓存
  useClones: false
});

/**
 * 项目信息缓存
 */
class ProjectCache {
  static getCacheKey(corpId, projectIds) {
    return `project_${corpId}_${projectIds.sort().join('_')}`;
  }

  static async getProjectInfo(corpId, projectIds, api) {
    if (!Array.isArray(projectIds) || !projectIds.length) {
      return { nameMap: new Map(), deptMap: new Map() };
    }

    // 去重并排序
    const uniqueIds = [...new Set(projectIds)].sort();
    const cacheKey = this.getCacheKey(corpId, uniqueIds);
    
    // 尝试从缓存获取
    let cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`项目信息缓存命中: ${cacheKey}`);
      return cachedData;
    }

    // 缓存未命中，从API获取
    try {
      const res = await api.getCorpApi({
        type: "getProjectIntentNames",
        corpId,
        ids: uniqueIds,
      });

      let projectMaps = { nameMap: new Map(), deptMap: new Map() };
      
      if (res?.data?.length) {
        projectMaps = res.data.reduce(
          (maps, item) => {
            if (item._id) {
              if (item.projectName)
                maps.nameMap.set(item._id, item.projectName);
              if (item.deptId) 
                maps.deptMap.set(item._id, item.deptId);
            }
            return maps;
          },
          { nameMap: new Map(), deptMap: new Map() }
        );
      }

      // 存入缓存
      cache.set(cacheKey, projectMaps);
      console.log(`项目信息已缓存: ${cacheKey}`);
      
      return projectMaps;
    } catch (error) {
      console.error('获取项目信息失败:', error);
      return { nameMap: new Map(), deptMap: new Map() };
    }
  }

  static clearCache(corpId) {
    const keys = cache.keys();
    const keysToDelete = keys.filter(key => key.startsWith(`project_${corpId}_`));
    keysToDelete.forEach(key => cache.del(key));
    console.log(`清理项目缓存: ${keysToDelete.length} 个条目`);
  }
}

/**
 * 查询结果缓存（用于相同查询条件的结果缓存）
 */
class QueryCache {
  static getCacheKey(content) {
    // 创建一个基于查询条件的缓存键
    const {
      corpId, name, mobile, page, pageSize,
      triageTimeDates, receptionPersonUserIds, reportPeoples,
      triagePersonUserIds, visitStatus, consumeStatus,
      consultStages, createTeamId, customerId, projectIds,
      tradeStatus, receptionDates, teamId, source,
      customerSource, counselors, introducers, interviewDoctors
    } = content;

    const keyObj = {
      corpId, name, mobile, page, pageSize,
      triageTimeDates: triageTimeDates?.join('-'),
      receptionPersonUserIds: receptionPersonUserIds?.sort().join(','),
      reportPeoples: reportPeoples?.sort().join(','),
      triagePersonUserIds: triagePersonUserIds?.sort().join(','),
      visitStatus: visitStatus?.sort().join(','),
      consumeStatus: consumeStatus?.sort().join(','),
      consultStages: consultStages?.sort().join(','),
      createTeamId, customerId,
      projectIds: projectIds?.sort().join(','),
      tradeStatus,
      receptionDates: receptionDates?.join('-'),
      teamId, source: source?.sort().join(','),
      customerSource: customerSource?.sort().join(','),
      counselors: counselors?.sort().join(','),
      introducers: introducers?.sort().join(','),
      interviewDoctors: interviewDoctors?.sort().join(',')
    };

    return `query_${Buffer.from(JSON.stringify(keyObj)).toString('base64')}`;
  }

  static get(content) {
    const cacheKey = this.getCacheKey(content);
    return cache.get(cacheKey);
  }

  static set(content, result, ttl = 300) { // 默认5分钟TTL
    const cacheKey = this.getCacheKey(content);
    cache.set(cacheKey, result, ttl);
    console.log(`查询结果已缓存: ${cacheKey.substring(0, 20)}...`);
  }

  static clear(corpId) {
    const keys = cache.keys();
    const keysToDelete = keys.filter(key => {
      if (!key.startsWith('query_')) return false;
      try {
        const decoded = Buffer.from(key.replace('query_', ''), 'base64').toString();
        const obj = JSON.parse(decoded);
        return obj.corpId === corpId;
      } catch {
        return false;
      }
    });
    keysToDelete.forEach(key => cache.del(key));
    console.log(`清理查询缓存: ${keysToDelete.length} 个条目`);
  }
}

/**
 * 统计信息缓存
 */
class StatsCache {
  static async getOrSet(key, fetchFunction, ttl = 1800) { // 默认30分钟
    let data = cache.get(key);
    if (data) {
      console.log(`统计缓存命中: ${key}`);
      return data;
    }

    data = await fetchFunction();
    cache.set(key, data, ttl);
    console.log(`统计数据已缓存: ${key}`);
    return data;
  }
}

/**
 * 缓存管理工具
 */
class CacheManager {
  static getStats() {
    return {
      keys: cache.keys().length,
      stats: cache.getStats()
    };
  }

  static clearAll() {
    cache.flushAll();
    console.log('所有缓存已清空');
  }

  static clearByCorpId(corpId) {
    ProjectCache.clearCache(corpId);
    QueryCache.clear(corpId);
    console.log(`机构 ${corpId} 的所有缓存已清空`);
  }

  static logStats() {
    const stats = this.getStats();
    console.log('缓存统计:', {
      缓存键数量: stats.keys,
      命中次数: stats.stats.hits,
      未命中次数: stats.stats.misses,
      命中率: `${((stats.stats.hits / (stats.stats.hits + stats.stats.misses)) * 100).toFixed(2)}%`
    });
  }
}

// 定期清理过期缓存
setInterval(() => {
  cache.keys().length > 0 && console.log(`当前缓存条目数: ${cache.keys().length}`);
}, 5 * 60 * 1000); // 每5分钟记录一次

module.exports = {
  ProjectCache,
  QueryCache,
  StatsCache,
  CacheManager
}; 
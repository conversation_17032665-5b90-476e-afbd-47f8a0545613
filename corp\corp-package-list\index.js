const dayjs = require("dayjs");
exports.getPackageList = async function (item, db) {
  const { page, pageSize, corp_id } = item;
  try {
    // 构建查询条件
    let query = {
      corpId: corp_id,
      packageStartTime: { $lte: dayjs().valueOf() },
    };
    const corpDB = db.collection("package-list");
    // 获取总数
    const total = await corpDB.countDocuments(query);
    const pages = Math.ceil(total / pageSize);
    // 获取分页数据
    const corpList = await corpDB
      .find(query)
      .sort({ createTime: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    return {
      total: total,
      pages: pages,
      size: pageSize,
      data: corpList,
      success: true,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
    };
  }
};

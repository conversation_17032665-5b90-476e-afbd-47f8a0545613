const dayjs = require("dayjs");
const appFunction = require("../app-function");
const utils = require("../utils.js");
const common = require("../../common.js"); // 引入根目录common.js
let db = null;

exports.main = async (context, DB) => {
  db = DB;
  switch (context.type) {
    case "getSopTaskResultList":
      return await getSopTaskResultList(context);
    // case 'getSopTaskResultStatistic':
    //   return await getSopTaskResultStatistic(context);
    case "updateCustomerSopTaskStatus":
      return await updateCustomerSopTaskStatus(context);
    case "updateGroupMsgTaskResult":
      return await updateGroupMsgTaskResult(context);
    case "getSopTaskResultForTag":
      return await getSopTaskResultForTag(context);
    case "getSopTaskResultForGroup":
      return await getSopTaskResultForGroup(context);
    case "getGroupTaskResultList":
      return await getGroupTaskResultList(context);
    default:
      return { success: false, message: "未知的操作类型" };
  }
};
async function getSopTaskResultList(context) {
  const { corpId, sopTaskId, executeTeamId, dates } = context;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!sopTaskId) return { success: false, message: "任务id不能为空" };
  let params = {
    corpId,
    sopTaskId,
    executeStatus: { $ne: "init" },
  };

  if (executeTeamId) params.executeTeamId = executeTeamId;
  if (dates && dates.length > 0) {
    let dateStart = dayjs(dates[0]).startOf("day").valueOf();
    let dateEnd = dayjs(dates[1]).endOf("day").valueOf();
    params.executeTime = { $gte: dateStart, $lte: dateEnd };
  }
  const data = await db
    .collection("sop-customer-task")
    .aggregate([
      { $match: params },
      {
        $group: {
          _id: "$executeUserId",
          executedCount: {
            $sum: { $cond: [{ $eq: ["$executeStatus", "executed"] }, 1, 0] },
          },
          unexecutedCount: {
            $sum: { $cond: [{ $eq: ["$executeStatus", "unexecuted"] }, 1, 0] },
          },
          failedCount: {
            $sum: { $cond: [{ $eq: ["$executeStatus", "failed"] }, 1, 0] },
          },
          executeTeamNames: { $addToSet: "$executeTeamName" },
        },
      },
    ])
    .toArray();

  return { success: true, data, message: "获取任务结果列表成功" };
}

async function getSopTaskResultForTag(context) {
  try {
    const { corpId, sopTaskId, dates, page, pageSize } = context;
    if (!corpId) return { success: false, message: "机构id不能为空" };
    if (!sopTaskId) return { success: false, message: "任务id不能为空" };

    let params = {
      corpId,
      sopTaskId,
      executeStatus: { $ne: "init" },
    };

    if (dates && dates.length > 0) {
      let dateStart = dayjs(dates[0]).startOf("day").valueOf();
      let dateEnd = dayjs(dates[1]).endOf("day").valueOf();
      params.createTime = { $gte: dateStart, $lte: dateEnd };
    }

    const total = await db
      .collection("sop-customer-task")
      .countDocuments(params);
    const pages = Math.ceil(total / pageSize);

    const data = await db
      .collection("sop-customer-task")
      .aggregate([
        { $match: params },
        { $sort: { createTime: -1 } },
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
        {
          $lookup: {
            from: "member",
            localField: "customerId",
            foreignField: "_id",
            as: "customer",
          },
        },
        {
          $project: {
            _id: 0,
            customer: { tagIds: 1, name: 1 },
            createTime: 1,
          },
        },
      ])
      .toArray();

    return {
      success: true,
      data,
      message: "获取任务结果列表成功",
      total,
      pages,
    };
  } catch {
    return {
      success: false,
      message: "获取失败",
    };
  }
}

async function getSopTaskResultForGroup(context) {
  try {
    const { corpId, sopTaskId, dates, page = 1, pageSize = 10 } = context;
    if (!corpId) return { success: false, message: "机构id不能为空" };
    if (!sopTaskId) return { success: false, message: "任务id不能为空" };

    let params = {
      corpId,
      sopTaskId,
      executeStatus: { $ne: "init" },
    };

    if (dates && dates.length > 0) {
      let dateStart = dayjs(dates[0]).startOf("day").valueOf();
      let dateEnd = dayjs(dates[1]).endOf("day").valueOf();
      params.createTime = { $gte: dateStart, $lte: dateEnd };
    }

    const total = await db
      .collection("sop-customer-task")
      .countDocuments(params);
    const pages = Math.ceil(total / pageSize);
    const data = await db
      .collection("sop-customer-task")
      .aggregate([
        { $match: params },
        { $sort: { createTime: -1 } },
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
        {
          $lookup: {
            from: "group",
            localField: "groupId",
            foreignField: "_id",
            as: "group",
          },
        },
        {
          $project: {
            _id: 0,
            group: { parentGroupId: 1, planName: 1 },
            customerName: 1,
            executeTeamId: 1,
            executeTeamName: 1,
            executeStatus: 1,
          },
        },
      ])
      .toArray();

    return {
      success: true,
      data,
      message: "获取任务结果列表成功",
      total,
      pages,
    };
  } catch {
    return {
      success: false,
      message: "获取失败",
    };
  }
}
async function updateGroupMsgTaskResult(params) {
  const { corpId, sopTaskId } = params;
  const fetchData = async (page, pageSize, db) => {
    let { data = [] } = await db
      .collection("sop-customer-task")
      .find({
        corpId,
        sopTaskId,
        executeStatus: "unexecuted",
        executeMethod: "groupmsg",
        executeTime: { $lte: dayjs().endOf("day").valueOf() },
      })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    return data;
  };
  const list = await utils.getAllData(fetchData, db);
  console.log("list", list);
  if (list.length === 0)
    return { success: true, message: "没有需要更新的任务" };
  const groupedTasks = groupByCorpId(list);
  for (const executeUserId in groupedTasks) {
    await execteGroupMsgTask({ corpId, sopTaskId, userId: executeUserId });
  }
  return { success: true, message: "更新成功" };
}

function groupByCorpId(tasks) {
  return tasks.reduce((acc, task) => {
    const { executeUserId } = task;
    if (!acc[executeUserId]) {
      acc[executeUserId] = [];
    }
    acc[executeUserId].push(task);
    return acc;
  }, {});
}

async function getGroupMsgTaskResultList(context) {
  const { corpId, userId, sopTaskId } = context;
  let { data = [] } = await db
    .collection("groupmsg-task")
    .find({
      corpId,
      executor: userId,
      sopTaskId,
      msgid: { $exists: true },
    })
    .limit(1000)
    .toArray();
  return data;
}

async function getGroupTaskResultList(context) {
  try {
    const { corpId, sopTaskId, dates, executeStatus } = context;
    if (!corpId) return { success: false, message: "机构id不能为空" };
    if (!sopTaskId) return { success: false, message: "任务id不能为空" };
    let query = {
      corpId,
      sopTaskId,
      exexecuteStatus: { $ne: "init" },
    };
    if (dates && dates.length > 0) {
      query.executeTime = {
        $gte: dayjs(dates[0]).startOf("day").valueOf(),
        $lte: dayjs(dates[1]).endOf("day").valueOf(),
      };
    }
    if (executeStatus) query.executeStatus = executeStatus;
    const record = await db
      .collection("sop-customer-task")
      .aggregate([
        { $match: query },
        {
          $lookup: {
            from: "group",
            localField: "groupId",
            foreignField: "_id",
            as: "group",
          },
        },
        {
          $project: {
            customerName: 1,
            executeTeamName: 1,
            executeStatus: 1,
            executeTime: 1,
            executeUserId: 1,
            executeResult: 1,
            failReason: 1,
            "group.groupName": 1,
            "group.managementPlan.planName": 1,
          },
        },
      ])
      .toArray();

    return {
      success: true,
      data: record,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      message: "获取失败",
      error: error.message,
    };
  }
}

async function execteGroupMsgTask(context) {
  const { corpId, userId, sopTaskId } = context;
  let groupMsgList = await getGroupMsgTaskResultList({
    corpId,
    userId,
    sopTaskId,
  });
  if (groupMsgList.length === 0) return;

  const access_token = await appFunction.getAccessToken({ corpId });

  for (let groupMsg of groupMsgList) {
    const { corpId, msgid, customerSopTaskIds = [] } = groupMsg;
    let { success, send_list } = await appFunction.getWecomGroupmsgSendResult({
      access_token,
      corpId,
      msgid,
      userid: userId,
    });
    if (success && Array.isArray(send_list) && send_list.length > 0) {
      const successCustomerUserIds = send_list
        .filter((item) => item.status === 1)
        .map((item) => item.external_userid);
      const failedCustomerUserIds = send_list
        .filter((item) => item.status === 2 || item.status === 3)
        .map((item) => item.external_userid);

      // 更新customerSopTask状态
      // 更新成功的
      console.log("successCustomerUserIds", successCustomerUserIds);
      console.log("failedCustomerUserIds", failedCustomerUserIds);
      console.log("customerSopTaskIds", customerSopTaskIds);
      await updateCustomerSopTaskStatus({
        customerSopTaskIds,
        executeStatus: "executed",
        corpId,
        customerUserIds: successCustomerUserIds,
      });

      // 更新失败的
      await updateCustomerSopTaskStatus({
        customerSopTaskIds,
        executeStatus: "failed",
        corpId,
        customerUserIds: failedCustomerUserIds,
      });
    }
  }
}

/**
 * 更新cusTomerSopTask状态
 * 执行三个状态 未执行 unexecuted 执行成功 executed 执行失败 failed
 * @returns
 */
async function updateCustomerSopTaskStatus({
  customerSopTaskIds,
  executeStatus,
  corpId,
  customerUserIds,
}) {
  if (!customerSopTaskIds || !executeStatus || !corpId)
    return { success: false, message: "参数错误" };

  console.log(
    "customerSopTaskIds",
    customerSopTaskIds,
    executeStatus,
    corpId,
    customerUserIds
  );

  let query = {
    _id: { $in: customerSopTaskIds },
    corpId,
    executeStatus: "unexecuted",
  };

  if (customerUserIds) query.customerUserId = { $in: customerUserIds };

  console.log("query", query);

  await db.collection("sop-customer-task").updateMany(query, {
    $set: {
      executeStatus,
      completeTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf(),
    },
  });

  return {
    success: true,
    message: "更新成功",
  };
}

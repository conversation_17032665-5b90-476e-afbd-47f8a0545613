const { ObjectId } = require("mongodb");

let db = "";
module.exports = async (item, mongodb) => {
  db = mongodb;
  switch (item.type) {
    case "getHlwDoctorList":
      return await getHlwDoctorList(item);
    case "deleteHlwDoctor":
      return await deleteHlwDoctor(item);
    case "updateHlwDoctor":
      return await updateHlwDoctor(item);
    case "addHlwDoctor":
      return await addHlwDoctor(item);
    case "getRandomOnlineDoctor":
      return await getRandomOnlineDoctor(item);
    case "isDoctorOnline":
      return await isDoctorOnline(item);
    case "getAllDoctorNos":
      return await getAllDoctorNos(item);
  }
};

// 获取医生列表  hlw-doctor 分页 数据库
async function getHlwDoctorList(item) {
  const {
    doctorName,
    page,
    pageSize,
    doctorCode,
    onlineStatus,
    serviceStatus,
    corpId,
  } = item;
  let query = {};
  if (doctorCode) query.doctorNo = doctorCode;
  if (onlineStatus) query.onlineStatus = onlineStatus;
  if (serviceStatus) query.serviceStatus = serviceStatus;
  if (corpId) query.corpId = corpId;
  if (typeof doctorName === "string")
    query.doctorName = new RegExp(doctorName.trim(), "i");

  try {
    const res = await db
      .collection("hlw-doctor")
      .aggregate([
        { $match: query },
        {
          $lookup: {
            from: "consult-order",
            localField: "doctorNo",
            foreignField: "doctorCode",
            as: "orders",
          },
        },
        {
          $addFields: {
            pendingOrdersCount: {
              $size: {
                $filter: {
                  input: "$orders",
                  as: "order",
                  cond: {
                    $or: [
                      {
                        $and: [
                          {
                            $in: [
                              "$$order.orderStatus",
                              ["pending", "processing"],
                            ],
                          },
                          { $eq: ["$$order.payStatus", "success"] },
                          { $lt: ["$$order.expreTime", Date.now()] },
                        ],
                      },
                    ],
                  },
                },
              },
            },
          },
        },
        { $sample: { size: pageSize } }, // 随机返回 pageSize 个文档
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
        {
          $sort: {
            pendingOrdersCount: 1, // 按照 pendingOrdersCount 进行升序排序
          },
        },
        {
          $project: {
            doctorNo: 1,
            doctorName: 1,
            pendingOrdersCount: 1,
            title: 1,
            deptName: 1,
            serviceFee: 1,
            deptCode: 1,
            onlineStatus: 1,
            serviceStatus: 1,
          },
        },
      ])
      .toArray();
    const total = await db.collection("hlw-doctor").countDocuments(query);
    return {
      success: true,
      message: "查询成功",
      data: res,
      total,
      pages: Math.ceil(total / pageSize),
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || "查询失败",
    };
  }
}

// 删除医生信息
async function deleteHlwDoctor(item) {
  const { _id } = item;
  try {
    // 调用mongo 数据库删除
    const res = await db.collection("hlw-doctor").deleteOne({
      _id: new ObjectId(_id),
    });
    return {
      success: true,
      message: "删除成功",
    };
  } catch (err) {
    return {
      success: false,
      message: "删除失败",
    };
  }
}
// 更新医生信息
async function updateHlwDoctor(item) {
  const { _id, params, doctorNo } = item;
  try {
    let query = {};
    if (_id) query._id = new ObjectId(_id);
    if (doctorNo) query.doctorNo = doctorNo;
    // 调用mongo 数据库更新
    const res = await db.collection("hlw-doctor").updateOne(query, {
      $set: {
        ...params,
        updateTime: Date.now(),
      },
    });
    return {
      success: true,
      message: "更新成功",
      res,
    };
  } catch (err) {
    return {
      success: false,
      message: "更新失败",
    };
  }
}
//新增医生信息
async function addHlwDoctor(item) {
  const { doctorName, doctorTitle, doctorDepartment } = item;
  try {
    // 调用mongo 数据库新增
    const res = await db.collection("hlw-doctor").insertOne({
      doctorName,
      doctorTitle,
      doctorDepartment,
      createTime: Date.now(),
    });
    return {
      success: true,
      message: "新增成功",
      data: res,
    };
  } catch (err) {
    return {
      success: false,
      message: "新增失败",
    };
  }
}

// 正在咨询数
async function getRandomOnlineDoctor(params) {
  try {
    const query = {
      onlineStatus: "online",
      serviceStatus: "enabled",
    };
    if (query.doctorNo) {
      query.doctorNo = query.doctorNo;
    }
    const res = await db
      .collection("hlw-doctor")
      .aggregate([
        { $match: query }, // 假设这是你的查询条件
        { $sample: { size: 1 } }, // 随机返回一个文档
      ])
      .toArray();
    return { success: true, data: res, message: "查询成功" };
  } catch (e) {
    return {
      success: false,
      message: e.message || "查询失败",
    };
  }
}

async function isDoctorOnline(params) {
  try {
    const count = await db.collection("hlw-doctor").countDocuments({
      onlineStatus: "online",
      serviceStatus: "enabled",
    });
    return { success: true, data: count > 0, message: "查询成功" };
  } catch (e) {
    return {
      success: false,
      message: e.message || "查询失败",
    };
  }
}

async function getAllDoctorNos(params) {
  try {
    const res = await db
      .collection("hlw-doctor")
      .find({}, { projection: { doctorNo: 1, doctorName: 1 } })
      .limit(10000)
      .toArray();
    return { success: true, data: res, message: "查询成功" };
  } catch (e) {
    return {
      success: false,
      message: e.message || "查询失败",
    };
  }
}

exports.login = async (item, db) => {
  const {params} = item;
  const { password, userName, env } = params ;
  if (!password) {
    return {
      success: false,
      message: "密码未传",
    };
  }
  if (!userName) {
    return {
      success: false,
      message: "账户未传",
    };
  }
  const count = await db
    .collection("platfrom-management-account")
    .countDocuments({
      password,
      userName,
      env,
    });
  if (count) {
    return {
      success: true,
      message: "登陆成功",
    };
  } else {
    return {
      success: false,
      message: "账号或密码错误",
    };
  }
};

exports.getMenu = async (item, db) => {
  try {
    const menuList = await db
      .collection("platfrom-management-menu")
      .find({})
      .toArray();
    return {
      success: true,
      data: menuList,
      message: "获取成功",
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      message: "获取失败",
    };
  }
};

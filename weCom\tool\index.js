const fileUrl = require("./file-url");
const uploadWecomFile = require("./upload-wecomfile");
const accessToken = require("../token");
exports.main = async (context) => {
  let { access_token = "", corpId, permanentCode, suiteToken } = context;
  if (!access_token) {
    access_token = await accessToken.getToken({
      corpId: corpId,
      permanentCode: permanentCode,
      suiteToken,
    });
    context.access_token = access_token;
  }
  switch (context.type) {
    case "getPageTitleAndContent":
      return await fileUrl.getPageTitleAndContent(context);
    case "uploadTempMedia":
    case "uploadTempImage":
      return await uploadWecomFile.main(context);
  }
};

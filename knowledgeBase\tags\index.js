const common = require("../../common.js");
let db = null;

exports.main = async (content, DB) => {
  db = DB;
  switch (content.type) {
    case "getPlatformTags":
      return await exports.getPlatformTags(content);
    case "addPlatformTag":
      return await exports.addPlatformTag(content);
  }
};

// 获取平台标签
exports.getPlatformTags = async (context) => {
  try {
    let res = await db.collection("platform-tags").find().toArray();
    return {
      success: true,
      message: "获取成功",
      data: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 添加平台标签
exports.addPlatformTag = async (context) => {
  let { tag } = context;
  try {
    await db.collection("platform-tags").insertOne({
      _id: common.generateRandomString(24),
      tag,
    });
    return {
      success: true,
      message: "添加成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

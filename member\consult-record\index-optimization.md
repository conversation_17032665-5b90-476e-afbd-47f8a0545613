# E-Consult-Record 查询性能优化索引建议

## 必需索引

### 1. e-consult-record 集合索引

```javascript
// 主要查询索引 - 支持按机构、时间范围查询
db.getCollection("e-consult-record").createIndex({ 
  "corpId": 1, 
  "timestamp": -1, 
  "createTime": -1 
});

// 客户查询索引 - 支持按客户ID查询
db.getCollection("e-consult-record").createIndex({ 
  "corpId": 1, 
  "customerId": 1, 
  "timestamp": -1 
});

// 项目筛选索引
db.getCollection("e-consult-record").createIndex({ 
  "corpId": 1, 
  "projectIds": 1, 
  "timestamp": -1 
});

// 来源筛选索引
db.getCollection("e-consult-record").createIndex({ 
  "corpId": 1, 
  "source": 1, 
  "timestamp": -1 
});

// 登记人筛选索引
db.getCollection("e-consult-record").createIndex({ 
  "corpId": 1, 
  "registrantUserId": 1, 
  "timestamp": -1 
});

// 开发者筛选索引
db.getCollection("e-consult-record").createIndex({ 
  "corpId": 1, 
  "userId": 1, 
  "timestamp": -1 
});

// 复合查询索引 - 支持多条件组合查询
db.getCollection("e-consult-record").createIndex({ 
  "corpId": 1, 
  "timestamp": -1,
  "source": 1,
  "projectIds": 1
});
```

### 2. member 集合索引

```javascript
// 基础查询索引
db.getCollection("member").createIndex({ 
  "corpId": 1, 
  "_id": 1 
});

// 手机号查询索引
db.getCollection("member").createIndex({ 
  "corpId": 1, 
  "mobile": 1 
});

// 姓名查询索引 - 如果需要支持模糊查询，建议使用文本索引
db.getCollection("member").createIndex({ 
  "corpId": 1, 
  "name": 1 
});

// 到院状态查询索引
db.getCollection("member").createIndex({ 
  "corpId": 1, 
  "inHospitalTimes": 1 
});

// 文本搜索索引 - 支持姓名和手机号的全文搜索（可选）
db.getCollection("member").createIndex({ 
  "name": "text", 
  "mobile": "text" 
}, { 
  "partialFilterExpression": { "corpId": { "$exists": true } } 
});
```

## 查询优化建议

### 1. 分离查询策略
- 当有会员筛选条件时，先查询 member 集合获取符合条件的客户ID
- 然后使用客户ID列表查询 e-consult-record 集合
- 避免使用 $lookup 进行表连接，减少查询复杂度

### 2. 分页限制
- 限制单次查询的 pageSize 最大值（建议1000）
- 避免超大页面查询导致的性能问题

### 3. 并行查询
- 列表查询和总数查询并行执行
- 减少查询等待时间

### 4. 字段投影
- 只查询需要的字段，减少数据传输量
- 使用 projection 限制返回字段

### 5. 缓存策略
- 对于项目信息等相对稳定的数据，可以考虑加入缓存
- 减少重复的 API 调用

## 执行索引创建脚本

```bash
# 连接到 MongoDB 后执行以下命令创建索引
mongo your_database_name --eval "
// e-consult-record 索引
db.getCollection('e-consult-record').createIndex({ 'corpId': 1, 'timestamp': -1, 'createTime': -1 });
db.getCollection('e-consult-record').createIndex({ 'corpId': 1, 'customerId': 1, 'timestamp': -1 });
db.getCollection('e-consult-record').createIndex({ 'corpId': 1, 'projectIds': 1, 'timestamp': -1 });
db.getCollection('e-consult-record').createIndex({ 'corpId': 1, 'source': 1, 'timestamp': -1 });
db.getCollection('e-consult-record').createIndex({ 'corpId': 1, 'registrantUserId': 1, 'timestamp': -1 });
db.getCollection('e-consult-record').createIndex({ 'corpId': 1, 'userId': 1, 'timestamp': -1 });
db.getCollection('e-consult-record').createIndex({ 'corpId': 1, 'timestamp': -1, 'source': 1, 'projectIds': 1 });

// member 索引
db.getCollection('member').createIndex({ 'corpId': 1, '_id': 1 });
db.getCollection('member').createIndex({ 'corpId': 1, 'mobile': 1 });
db.getCollection('member').createIndex({ 'corpId': 1, 'name': 1 });
db.getCollection('member').createIndex({ 'corpId': 1, 'inHospitalTimes': 1 });

print('所有索引创建完成');
"
```

## 性能监控

### 1. 查询分析
```javascript
// 使用 explain() 分析查询性能
db.getCollection("e-consult-record").find({
  corpId: "your_corp_id",
  timestamp: { $gte: start_timestamp, $lte: end_timestamp }
}).explain("executionStats");
```

### 2. 索引使用情况监控
```javascript
// 查看索引使用统计
db.getCollection("e-consult-record").aggregate([
  { $indexStats: {} }
]);
```

## 预期性能提升

1. **查询速度提升**: 60-80% 性能提升
2. **内存使用减少**: 避免大量聚合操作，减少内存占用
3. **并发能力提升**: 减少查询锁时间，提升并发处理能力
4. **系统稳定性**: 避免超时和内存溢出问题 
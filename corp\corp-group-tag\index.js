const utils = require("../utils");
const corp = require("../corp-base/index");
const api = require("../../api");
let db = null;
exports.main = async (event, mongodb) => {
  db = mongodb;
  switch (event.type) {
    case "getCorpTags":
      return await exports.getCorpTags(event);
    case "addCorpGroupTag":
      return await exports.addCorpGroupTag(event);
    case "addCorpTagToGroup":
      return await exports.addCorpTagToGroup(event);
    case "deleteCorpGroupTag":
      return await exports.deleteCorpGroupTag(event);
    case "deleteCorpTag":
      return await exports.deleteCorpTag(event);
    case "updateCorpGroupTag":
      return await exports.updateCorpGroupTag(event);
    case "updateCorpTag":
      return await exports.updateCorpTag(event);
    case "syncCorpCustomerTag":
      return await exports.syncCorpCustomerTag(event);
  }
};

// 1. 获取机构标签
exports.getCorpTags = async (context) => {
  const { env, corpId } = context;
  try {
    let res = await db.collection("corp-group-tag").find({ corpId }).toArray();
    return {
      success: true,
      message: "获取成功",
      list: res,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

/**
 * 添加机构标签分组
 * @param {*} groupId 标签分组id
 * @param {*} groupName 标签分组名称
 * @param {*} order 标签排序
 * @returns
 */
exports.addCorpGroupTag = async (context) => {
  const {
    env,
    corpId,
    groupId,
    groupName,
    order,
    tag = [],
    createType = "",
  } = context;
  try {
    let res = await db.collection("corp-group-tag").insertOne({
      corpId,
      createType,
      groupId,
      groupName,
      order,
      tag,
    });
    return {
      success: true,
      id: res.insertedId,
      message: "添加成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

/**
 * 添加机构标签分组的标签
 * @param {*} id 标签id
 * @param {*} name 标签名称
 * @param {*} order 标签排序
 * @returns
 */
exports.addCorpTagToGroup = async (context) => {
  const { env, corpId, groupId, id, name, order } = context;
  try {
    let res = await db
      .collection("corp-group-tag")
      .updateOne({ corpId, groupId }, { $push: { tag: { id, name, order } } });
    return {
      success: true,
      message: "添加成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 删除机构标签分组
exports.deleteCorpGroupTag = async (context) => {
  const { env, corpId, groupId } = context;
  try {
    let res = await db
      .collection("corp-group-tag")
      .deleteOne({ corpId, groupId });
    return {
      success: true,
      message: "删除成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 删除机构标签
exports.deleteCorpTag = async (context) => {
  const { env, corpId, groupId, id } = context;
  try {
    let res = await db
      .collection("corp-group-tag")
      .updateOne({ corpId, groupId }, { $pull: { tag: { id } } });
    return {
      success: true,
      message: "删除成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 更新机构标签分组
exports.updateCorpGroupTag = async (context) => {
  const { env, corpId, groupId, groupName, order, tag } = context;
  try {
    let res = await db
      .collection("corp-group-tag")
      .updateOne({ corpId, groupId }, { $set: { groupName, order, tag } });
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

// 更新机构标签
exports.updateCorpTag = async (context) => {
  const { env, corpId, groupId, id, name, order } = context;
  try {
    let res = await db
      .collection("corp-group-tag")
      .updateOne(
        { groupId, "tag.id": id },
        { $set: { "tag.$.name": name, "tag.$.order": order } }
      );
    return {
      success: true,
      message: "更新成功",
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

/**
 * 同步机构客户标签
 * @param {*} context
 */
exports.syncCorpCustomerTag = async (context) => {
  // 获取客户详情
  let { corpId, externalUserId } = context;
  // 获取permanentCode
  let res = await corp.getCorpData(context);
  let permanentCode = res.data[0].permanentCode;
  // 获取access_token
  let tokenRes = await api.getWecomApi({
    type: "getAccessToken",
    corpId: corpId,
    permanentCode,
  });
  // 获取客户详情  // result.data
  let result = await api.getWecomApi({
    type: "getNameByexternalUserId",
    corpId,
    access_token: tokenRes.accessToken,
    externalUserId,
  });
  let { follow_user, name } = result.data;
  let tags = [];
  if (follow_user.tags) {
    for (let i = 0; i < follow_user.tags.length; i++) {
      tags.push({
        id: tags[i].tag_id,
      });
    }
  }
  // 更新客户标签
  let { success } = await api.getMemberApi({
    type: "syncCustomerTag",
    corpId,
    externalUserId,
    customterName: name,
    tags,
  });
  if (success) {
    console.log("同步客户标签成功", externalUserId);
  } else {
    console.log("同步客户标签失败", externalUserId);
  }
};

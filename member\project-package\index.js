const dayjs = require("dayjs");
const cate = require("./package-cate.js");
const util = require("./util.js");
const common = require("../../common.js"); // 引入根目录common.js
let db = null;

exports.main = async (ctx, DB) => {
  db = DB;
  switch (ctx.type) {
    case "addProjectPackage":
      return await addProjectPackage(ctx);
    case "updateProjectPackage":
      return await updateProjectPackage(ctx);
    case "getProjectPackageList":
      return await getProjectPackageList(ctx);
    case "removeProjectPackage":
      return await removeProjectPackage(ctx);
    case "setProjectPackageEnable":
      return await setProjectPackageEnable(ctx);
    case "addProjectPackageCate":
    case "updateProjectPackageCate":
    case "deleteProjectPackageCate":
    case "getProjectPackageCateList":
    case "sortProjectPackageCate":
      return await cate.main(ctx, db);
    default:
      return {
        success: false,
        message: "未找到方法",
      };
  }
};

// 新增项目套餐
async function addProjectPackage(ctx) {
  const { corpId, ...rest } = ctx;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const msg = util.verifyPakcage(rest);
    if (msg) return { success: false, message: msg };
    const data = util.getPackageData(rest, { corpId, enable: true });
    data._id = common.generateRandomString(24); // 生成随机_id
    const res = await db.collection("project-package").insertOne(data);
    return { success: true, data: res.insertedId, message: "新增套餐成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 更新项目套餐
async function updateProjectPackage(ctx) {
  const { corpId, _id: id, ...rest } = ctx;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!id) return { success: false, message: "id不能为空" };
  try {
    const msg = util.verifyPakcage(rest);
    if (msg) return { success: false, message: msg };
    const data = util.getPackageData(rest);
    const res = await db
      .collection("project-package")
      .updateOne({ _id: id, corpId }, { $set: data });
    return { success: true, data: res, message: "更新套餐成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 获取项目套餐列表
async function getProjectPackageList(ctx) {
  const { corpId, name, classIds, page = 1, pageSize = 10, packageType } = ctx;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  try {
    const query = { corpId };
    if (Array.isArray(classIds)) query.cateId = { $in: classIds };
    if (typeof name === "string" && name.trim())
      query.name = { $regex: ".*" + name.trim() + ".*", $options: "i" };
    if (packageType) query.packageType = packageType;
    const total = await db.collection("project-package").countDocuments(query); // 总数
    const data = await db
      .collection("project-package")
      .aggregate([
        { $match: query },
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
        {
          $lookup: {
            from: "project-list",
            localField: "projects._id",
            foreignField: "_id",
            as: "packageProjects",
          },
        },
      ])
      .toArray();

    return {
      success: true,
      list: util.getPackageListWithPrice(data),
      total,
      message: "获取项目套餐成功",
    };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 删除项目套餐
async function removeProjectPackage(ctx) {
  const { corpId, id } = ctx;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!id) return { success: false, message: "id不能为空" };
  try {
    const res = await db
      .collection("project-package")
      .deleteOne({ _id: id, corpId });
    if (res.deletedCount > 0)
      return {
        success: true,
        deleted: res.deletedCount,
        message: "删除项目套餐成功",
      };
    return { success: false, message: "删除项目套餐失败" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}

// 设置项目套餐启用状态
async function setProjectPackageEnable(ctx) {
  const { corpId, id, enable } = ctx;
  if (!corpId) return { success: false, message: "机构id不能为空" };
  if (!id) return { success: false, message: "id不能为空" };
  if (typeof enable !== "boolean")
    return { success: false, message: "enable参数错误" };
  try {
    const res = await db
      .collection("project-package")
      .updateOne({ _id: id, corpId }, { $set: { enable } });
    return { success: true, data: res, message: "设置项目套餐状态成功" };
  } catch (e) {
    return { success: false, message: e.message };
  }
}
